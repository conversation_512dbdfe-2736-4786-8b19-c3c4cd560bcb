#coding=utf-8
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
from xtquant import xtconstant
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QPushButton, QLabel, QLineEdit, 
                           QTextEdit, QGridLayout, QMessageBox, QTableWidget,
                           QTableWidgetItem, QDialog, QComboBox, QGroupBox, QFrame, QCheckBox, QHeaderView, QSizePolicy, QScrollBar)
from PyQt5.QtCore import Qt, QProcess, pyqtSignal, QObject, QTimer, QDateTime, QPointF, QTime
from PyQt5.QtGui import QColor, QFont, QPainter
#from PyQt5.QtChart import QChart, QChartView, QLineSeries, QValueAxis, QDateTimeAxis
import sys
import subprocess
import asyncio
import os
import time
from datetime import datetime, time as dt_time, timedelta
from xtquant import xtdata
import json
import pyqtgraph as pg
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import socket
import threading

from stock_utils import my_download

# 设置 pyqtgraph 的全局配置
pg.setConfigOptions(antialias=True)  # 启用抗锯齿
pg.setConfigOption('background', 'w')  # 设置背景为白色
pg.setConfigOption('foreground', 'k')  # 设置前景为黑色

def plot_time_series(df, title):
    """绘制分时图"""
    try:
        # 检查数据是否为空
        if df.empty:
            raise ValueError("数据为空")
            
        # 检查是否存在必要的列
        required_columns = ['time', 'open', 'close', 'high', 'low', 'volume', 'amount']
        if not all(col in df.columns for col in required_columns):
            raise ValueError("数据缺少必要的列")
        
        # 关闭所有已打开的图形
        plt.close('all')
        
        # 创建图形和子图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 9), height_ratios=[2.5, 1])
        fig.suptitle(title, fontsize=12, y=0.95)  # 调整标题位置
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 确保数据按时间排序
        df = df.sort_values('time')
        
        # 计算OCHL平均值
        df['price'] = (df['open'] + df['close'] + df['high'] + df['low']) / 4
        
        # 获取第一个价格作为基准价
        base_price = df.iloc[0]['open']
        
        # 分别处理上午和下午的数据
        morning_mask = df['time'].apply(lambda x: dt_time(9, 15) <= x.time() <= dt_time(11, 30))
        afternoon_mask = df['time'].apply(lambda x: dt_time(13, 0) <= x.time() <= dt_time(15, 0))
        
        morning_data = df[morning_mask].copy()
        afternoon_data = df[afternoon_mask].copy()
        
        # 调整下午时段的时间
        if not afternoon_data.empty:
            # 获取上午结束时间点
            morning_end = datetime.combine(df['time'].iloc[0].date(), dt_time(11, 30))
            
            # 调整下午数据的时间 - 直接接上午时段
            afternoon_data['time'] = afternoon_data['time'].apply(
                lambda x: morning_end + timedelta(minutes=(x.hour - 13) * 60 + x.minute)
            )
        
        # 合并数据
        df_adjusted = pd.concat([morning_data, afternoon_data]).sort_values('time')
        
        # 计算时间间隔
        time_diff = (df_adjusted['time'].iloc[1] - df_adjusted['time'].iloc[0]).total_seconds()
        bar_width = timedelta(seconds=time_diff * 0.8)
        
        # 调整时间轴，使点位于bar的中心
        plot_times = df_adjusted['time'] + timedelta(seconds=time_diff/2)
        
        # 绘制价格线 - 使用OCHL平均值
        ax1.plot(plot_times, df_adjusted['price'], 'b-', linewidth=1, label='价格')
        
        # 计算并绘制均价线
        total_amount = df_adjusted['amount'].cumsum()
        total_volume = df_adjusted['volume'].cumsum()
        df_adjusted['avg_price'] = total_amount / total_volume
        ax1.plot(plot_times, df_adjusted['avg_price'], 'r--', linewidth=1, label='均价')
        
        # 计算并绘制20分钟均线 - 使用OCHL平均值
        # 修改为使用min_periods=1，这样不足20个点时也会计算均线
        df_adjusted['ma20'] = df_adjusted['price'].rolling(window=20, min_periods=1).mean()
        ax1.plot(plot_times, df_adjusted['ma20'], 'g-', linewidth=1, label='20分钟均线')
        
        # 设置价格轴
        min_price = df_adjusted['price'].min()
        max_price = df_adjusted['price'].max()
        
        # 计算价格轴范围，使昨收位于中间
        price_range = max(abs(max_price - base_price), abs(min_price - base_price))
        if price_range == 0:  # 如果价格没有变化
            price_range = base_price * 0.01  # 设置为基准价的1%
        else:
            price_range = price_range * 1.1  # 留出10%的边距
        
        ax1.set_ylim(base_price - price_range, base_price + price_range)
        
        # 添加基准价水平线
        ax1.axhline(y=base_price, color='gray', linestyle=':', alpha=0.5)
        
        # 计算最大和最小涨跌幅
        ratio_range = price_range / base_price * 100
        ax1_right = ax1.twinx()
        ax1_right.set_ylim(-ratio_range, ratio_range)
        ax1_right.set_ylabel('涨跌幅(%)')
        
        # 设置网格
        ax1.grid(True, linestyle='--', alpha=0.3)
        ax1.set_ylabel('价格')
        ax1.legend(loc='best', fontsize=9, framealpha=0.5)
        
        # 绘制成交量柱状图
        volume_colors = ['red' if c >= o else 'green' 
                        for o, c in zip(df_adjusted['open'], df_adjusted['close'])]
        ax2.bar(df_adjusted['time'], df_adjusted['volume'], 
                width=bar_width, color=volume_colors, alpha=0.7)
        ax2.grid(True, linestyle='--', alpha=0.3)
        ax2.set_ylabel('成交量')
        
        # 设置x轴时间刻度
        today = df_adjusted['time'].iloc[0].date()
        morning_start = datetime.combine(today, dt_time(9, 30))
        morning_end = datetime.combine(today, dt_time(11, 30))
        
        # 创建时间刻度（每15分钟一个刻度）
        trading_hours = []
        time_labels = []
        
        # 添加上午时段的刻度
        current = morning_start
        while current <= morning_end:
            if current.minute % 15 == 0:
                trading_hours.append(current)
                time_labels.append(current.strftime('%H:%M'))
            current += timedelta(minutes=15)
        
        # 添加下午时段的刻度（显示实际时间）
        afternoon_times = pd.date_range(
            start=datetime.combine(today, dt_time(13, 0)),
            end=datetime.combine(today, dt_time(15, 0)),
            freq='15min'
        )
        
        for t in afternoon_times:
            if t.minute % 15 == 0:
                # 计算对应的调整后时间 - 直接接上午时段
                adjusted_time = morning_end + timedelta(minutes=(t.hour - 13) * 60 + t.minute)
                trading_hours.append(adjusted_time)
                time_labels.append(t.strftime('%H:%M'))  # 显示实际时间
        
        # 设置x轴刻度和标签
        for ax in [ax1, ax2]:
            ax.set_xticks(trading_hours)
            ax.set_xticklabels(time_labels, rotation=45)  # 添加rotation参数使标签倾斜显示
            ax.set_xlim(morning_start - timedelta(minutes=5),
                       trading_hours[-1] + timedelta(minutes=5))
        
        # 添加垂直分隔线标识上下午
        ax1.axvline(x=morning_end, color='gray', linestyle='--', alpha=0.3)
        ax2.axvline(x=morning_end, color='gray', linestyle='--', alpha=0.3)
        
        # 调整布局
        plt.tight_layout(rect=[0.02, 0.02, 0.98, 0.98])
        
        # 调整子图间距
        plt.subplots_adjust(hspace=0.1)
        
        # 调整字体大小
        ax1.tick_params(labelsize=9)
        ax2.tick_params(labelsize=9)
        ax1_right.tick_params(labelsize=9)
        
        plt.show()
        
    except Exception as e:
        print(f"绘制分时图时出错: {str(e)}")
        raise

class SignalManager(QObject):
    # 定义信号
    append_text = pyqtSignal(str)

class MyXtQuantTraderCallback(XtQuantTraderCallback):
    def __init__(self, ui):
        super().__init__()
        self.ui = ui

    def on_disconnected(self):
        """连接断开"""
        self.ui.on_disconnected()

    def on_stock_order(self, order):
        """委托回报推送"""
        try:
            # 更新委托状态显示
            msg = (f"委托状态更新 - 委托号:{order.order_id}\n"
                   f"  状态: {self.get_order_status_text(order.order_status)}\n"
                   f"  已成交: {order.traded_volume}")
            self.ui.info_append(msg)
            
            # 如果撤单对话框打开，刷新显示
            if hasattr(self.ui, 'cancel_dialog') and self.ui.cancel_dialog.isVisible():
                self.ui.refresh_cancel_table()
            
        except Exception as e:
            self.ui.info_append(f"处理委托回报出错: {str(e)}")

    def get_order_status_text(self, status):
        """获取委托状态文本"""
        # 使用 XT Trader 文档中定义的正确状态码
        status_map = {
            xtconstant.ORDER_UNREPORTED: "未报",          # 48
            xtconstant.ORDER_WAIT_REPORTING: "待报",      # 49
            xtconstant.ORDER_REPORTED: "已报",            # 50
            xtconstant.ORDER_REPORTED_CANCEL: "已报待撤",  # 51
            xtconstant.ORDER_PARTSUCC_CANCEL: "部成待撤",  # 52
            xtconstant.ORDER_PART_CANCEL: "部撤",         # 53
            xtconstant.ORDER_CANCELED: "已撤",            # 54
            xtconstant.ORDER_PART_SUCC: "部成",           # 55
            xtconstant.ORDER_SUCCEEDED: "已成",           # 56
            xtconstant.ORDER_JUNK: "废单",               # 57
            xtconstant.ORDER_UNKNOWN: "未知"             # 255
        }
        return status_map.get(status, f"未知状态({status})")

    def on_stock_trade(self, trade):
        """成交变动推送"""
        self.ui.on_trade(trade)

    def on_order_error(self, order_error):
        """委托失败推送"""
        self.ui.on_order_error(order_error)

    def on_cancel_error(self, cancel_error):
        """撤单失败推送"""
        self.ui.on_cancel_error(cancel_error)

    def on_cancel_order_stock_async_response(self, response):
        """异步撤单回报"""
        account_type = response.account_type
        account_id = response.account_id
        order_id = response.order_id
        order_sysid = response.order_sysid
        cancel_result = response.cancel_result
        seq = response.seq

        msg = (f"收到撤单回报 - 账号:{account_id} 订单号:{order_id} "
               f"柜台编号:{order_sysid} 结果:{cancel_result}")
        self.ui.info_append(msg)  # 使用新的方法

class TradingUI(QMainWindow):
    def __init__(self):
        super().__init__()
        # 在初始化时读取股票买入价格
        self.stock_prices = {}
        
        # 创建信号管理器
        self.signal_manager = SignalManager()
        self.signal_manager.append_text.connect(self.append_text_safe)
        
        # 初始化盈亏曲线数据
        self.x_data = []
        self.y_data = []
        self.current_total_profit = 0
        
        # 初始化做T相关的变量
        self.stock_open_prices = {}  # 记录每只股票的开盘价
        self.last_cross_prices = {}  # 记录每只股票上一次交叉时的价格
        self.last_cross_type = {}    # 记录每只股票上一次交叉类型（'golden' 或 'death'）
        self.first_cross = True      # 标记是否为第一次交叉
        self.base_positions = {}     # 记录每只股票的开市前持仓量
        self.t_trading_states = {}   # 用于存储每个股票的做T状态
        
        self.setup_ui()  # 先创建UI
        self.setup_trader()  # 再初始化交易接口
        self.connect_signals()
        self.load_pending_orders()  # 加载待委托列表
        
        # 读取股票买入价格
        self.stock_prices = self.read_stock_prices_from_excel()
        
        # 创建定时器用于更新资产信息
        self.asset_timer = QTimer()
        self.asset_timer.timeout.connect(self.update_asset_info)
        self.asset_timer.start(5000)  # 每5秒更新一次
        
        # 初始化开市前持仓量
        self.init_base_positions()
        
        # 添加自动保存定时器
        self.setup_auto_save_timer()
        
        # 初始化IPC服务器
        self.setup_ipc_server()
        
        # 添加昨日总市值属性
        self.yesterday_total_value = None
        # 初始化昨日总市值
        self.init_yesterday_total_value()
        
        self.init_yesterday_total_value()

    def read_stock_prices_from_excel(self):
        """获取当前持仓股票的成本价"""
        try:
            # 获取当前持仓情况
            positions = self.xt_trader.query_stock_positions(self.acc)
            if not positions:
                self.info_append("当前账户无持仓")
                return {}
                
            # 过滤掉持仓量为0的证券
            positions = [pos for pos in positions if pos.volume > 0]
            if not positions:
                self.info_append("当前账户无有效持仓")
                return {}
                
            # 使用账户中的成本价
            stock_prices = {}
            self.info_append("\n持仓成本价:")
            for pos in positions:
                stock_prices[pos.stock_code] = pos.avg_price
                self.info_append(
                    f"{pos.stock_code}:\n"
                    f"  持仓量: {pos.volume}\n"
                    f"  成本价: {pos.avg_price:.3f}"
                )
            
            return stock_prices
                
        except Exception as e:
            self.info_append(f"获取持仓成本价失败: {str(e)}")
            return {}

    def init_base_positions(self):
        """初始化所有股票的开市前持仓量"""
        try:
            # 获取今天的日期字符串
            today = datetime.now().strftime('%Y%m%d')
            file_path = f'f:/work/base_positions_{today}.json'
            
            # 检查是否为交易日
            if not self.is_trading_day(today):
                self.info_append("今日非交易日，不初始化持仓量")
                return
            
            # 尝试从文件读取
            if os.path.exists(file_path):
                self.info_append("从文件读取开市前持仓量...")
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.base_positions = json.load(f)
                if not self.base_positions:
                    self.info_append("开市前持仓为空")
                else:
                    self.info_append(f"已读取开市前持仓量: {self.base_positions}")
                return
            
            # 如果文件不存在，从账户获取持仓信息
            self.info_append("文件不存在，从账户获取持仓量...")
            positions = self.xt_trader.query_stock_positions(self.acc)
            
            # 初始化空字典
            self.base_positions = {}
            
            if positions:
                # 过滤掉持仓量为0的证券
                valid_positions = [pos for pos in positions if pos.volume > 0]
                
                # 记录每只股票的持仓量和开盘价
                for pos in valid_positions:
                    self.base_positions[pos.stock_code] = pos.volume
                    self.info_append(f"记录{pos.stock_code}开市前持仓量: {pos.volume}")
            else:
                self.info_append("当前账户无持仓")
            
            # 保存持仓记录
            self.save_base_positions()
            self.info_append("已保存今日持仓记录")
            
        except Exception as e:
            self.info_append(f"初始化开市前持仓量失败: {str(e)}")

    def save_base_positions(self):
        """保存开市前持仓量到文件"""
        try:
            # 获取今天的日期字符串
            today = datetime.now().strftime('%Y%m%d')
            file_path = f'f:/work/base_positions_{today}.json'
            
            # 确保目录存在
            os.makedirs('f:/work', exist_ok=True)
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.base_positions, f, ensure_ascii=False, indent=2)
            
            self.info_append(f"已保存开市前持仓量到文件: {file_path}")
            if not self.base_positions:
                self.info_append("今日无持仓记录")
            
        except Exception as e:
            self.info_append(f"保存开市前持仓量失败: {str(e)}")

    def append_text_safe(self, text):
        """在主线程中安全地添加文本"""
        self.info_text.append(text)

    def info_append(self, text):
        """通过信号发送文本"""
        self.signal_manager.append_text.emit(text)

    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle('委托交易')
        # 将窗口宽度从1600增加到1800，高度保持1200不变
        self.resize(1800, 1200)
        
        # 创建中心部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)
        
        # 创建左右分栏
        left_frame = QFrame()
        right_frame = QFrame()
        # 设置左右比例为 1:2
        main_layout.addWidget(left_frame, 1)
        main_layout.addWidget(right_frame, 2)
        
        # 左侧布局
        left_layout = QVBoxLayout(left_frame)
        left_layout.setSpacing(10)
        
        # 修改左侧布局的比例设置
        left_layout.setStretch(0, 2)  # 委托下单
        left_layout.setStretch(1, 1)  # 交易按钮
        left_layout.setStretch(2, 2)  # 系统日志
        
        # 委托下单区
        order_group = QGroupBox("委托下单")
        order_group.setFixedHeight(220)  # 设置固定高度为220像素
        # 或者使用最大/最小高度
        # order_group.setMaximumHeight(220)  # 设置最大高度
        # order_group.setMinimumHeight(200)  # 设置最小高度
        
        order_layout = QGridLayout()
        order_layout.setSpacing(3)
        
        # 添加下单控件
        self.stock_code_edit = QLineEdit()
        self.stock_code_edit.setClearButtonEnabled(True)  # 添加清空按钮
        
        # 价格输入区域
        price_widget = QWidget()
        price_layout = QHBoxLayout(price_widget)
        price_layout.setContentsMargins(0, 0, 0, 0)
        
        self.price_minus_btn = QPushButton("-")
        self.price_minus_btn.setFixedWidth(25)
        self.price_edit = QLineEdit()
        self.price_edit.setClearButtonEnabled(True)  # 添加清空按钮
        self.price_plus_btn = QPushButton("+")
        self.price_plus_btn.setFixedWidth(25)
        
        price_layout.addWidget(self.price_minus_btn)
        price_layout.addWidget(self.price_edit)
        price_layout.addWidget(self.price_plus_btn)
        
        # 金额输入区域
        amount_widget = QWidget()
        amount_layout = QHBoxLayout(amount_widget)
        amount_layout.setContentsMargins(0, 0, 0, 0)
        
        self.amount_minus_btn = QPushButton("-")
        self.amount_minus_btn.setFixedWidth(25)
        self.amount_edit = QLineEdit("0")  # 将默认值改为0，而不是10000
        self.amount_edit.setClearButtonEnabled(True)  # 添加清空按钮
        self.amount_plus_btn = QPushButton("+")
        self.amount_plus_btn.setFixedWidth(25)
        
        amount_layout.addWidget(self.amount_minus_btn)
        amount_layout.addWidget(self.amount_edit)
        amount_layout.addWidget(self.amount_plus_btn)
        
        # 数量输入区域
        volume_widget = QWidget()
        volume_layout = QHBoxLayout(volume_widget)
        volume_layout.setContentsMargins(0, 0, 0, 0)
        
        self.volume_minus_btn = QPushButton("-")
        self.volume_minus_btn.setFixedWidth(25)
        self.volume_edit = QLineEdit()
        self.volume_edit.setClearButtonEnabled(True)  # 添加清空按钮
        self.volume_plus_btn = QPushButton("+")
        self.volume_plus_btn.setFixedWidth(25)
        
        volume_layout.addWidget(self.volume_minus_btn)
        volume_layout.addWidget(self.volume_edit)
        volume_layout.addWidget(self.volume_plus_btn)
        
        # 添加报价方式选择
        self.price_type_combo = QComboBox()
        self.price_type_combo.addItems(['最新价委托', '限价委托'])  # 调换顺序，使最新价委托为默认选项
        
        # 修改为包含最优价委托选项
        self.price_type_combo.clear()
        self.price_type_combo.addItems(['最新价委托', '限价委托', '最优五档即时成交'])
        
        # 添加稍后委托选项
        self.later_checkbox = QCheckBox("稍后委托")
        
        # 添加到下单布局
        order_layout.addWidget(QLabel('证券代码:'), 0, 0)
        order_layout.addWidget(self.stock_code_edit, 0, 1)
        order_layout.addWidget(QLabel('委托价格:'), 1, 0)
        order_layout.addWidget(price_widget, 1, 1)
        order_layout.addWidget(QLabel('委托金额:'), 2, 0)
        order_layout.addWidget(amount_widget, 2, 1)
        order_layout.addWidget(QLabel('委托数量:'), 3, 0)
        order_layout.addWidget(volume_widget, 3, 1)
        order_layout.addWidget(QLabel('报价方式:'), 4, 0)
        order_layout.addWidget(self.price_type_combo, 4, 1)
        order_layout.addWidget(self.later_checkbox, 5, 1)
        
        order_group.setLayout(order_layout)
        left_layout.addWidget(order_group)
        
        # 交易按钮区
        button_group = QGroupBox("交易操作")
        button_group.setFixedHeight(180)  # 设置固定高度为180像素
        
        button_layout = QVBoxLayout()
        button_layout.setSpacing(8)  # 设置按钮之间的间距
        
        self.buy_button = QPushButton('买入')
        self.sell_button = QPushButton('卖出')
        self.cancel_button = QPushButton('撤单')
        self.query_button = QPushButton('查询')
        
        for btn in [self.buy_button, self.sell_button, self.cancel_button, self.query_button]:
            button_layout.addWidget(btn)
        
        button_group.setLayout(button_layout)
        left_layout.addWidget(button_group)
        
        # 系统日志区域 - 移到左侧底部
        log_group = QGroupBox("系统日志")
        log_layout = QVBoxLayout()
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        log_layout.addWidget(self.info_text)
        log_group.setLayout(log_layout)
        left_layout.addWidget(log_group)
        
        # 右侧布局
        right_layout = QVBoxLayout(right_frame)
        
        # 待委托列表区域
        self.pending_orders_group = QGroupBox("待委托列表")
        pending_layout = QVBoxLayout()
        
        # 添加顶部控制区域
        top_control = QWidget()
        top_layout = QHBoxLayout(top_control)
        top_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加直接委托勾选框
        self.direct_order_checkbox = QCheckBox("直接委托")
        self.direct_order_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 13px;
                font-weight: bold;
                color: #17a2b8;
            }
        """)
        top_layout.addWidget(self.direct_order_checkbox)
        
        # 添加全部委托按钮
        commit_all_btn = QPushButton("全部委托")
        commit_all_btn.clicked.connect(self.commit_all_pending_orders)
        commit_all_btn.setFixedWidth(200)
        commit_all_btn.setStyleSheet("""
            QPushButton {
                height: 28px;
                background-color: #f0f0f0;
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                font-size: 13px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
        """)
        top_layout.addWidget(commit_all_btn, alignment=Qt.AlignRight)
        
        pending_layout.addWidget(top_control)
        
        self.pending_table = QTableWidget()
        self.pending_table.setColumnCount(8)
        self.pending_table.setHorizontalHeaderLabels([
            '证券代码', '方向', '类型', '价格', '数量', '金额', '委托', '删除'
        ])
        
        # 设置表格样式
        self.pending_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.pending_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.pending_table.horizontalHeader().setStretchLastSection(False)
        
        # 设置列宽 - 根据实际总宽度分配
        header = self.pending_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # 按实际需要分配宽度
        column_widths = [160, 80, 140, 100, 100, 120, 70, 70]  # 总宽度840px
        for col, width in enumerate(column_widths):
            self.pending_table.setColumnWidth(col, width)
        
        # 设置表格样式
        self.pending_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                border: 1px solid #d0d0d0;
            }
            QTableWidget::item {
                padding: 4px;
                color: black;
            }
            QHeaderView::section {
                background-color: #f8f8f8;
                padding: 4px;
                border: none;
                border-right: 1px solid #d0d0d0;
                border-bottom: 1px solid #d0d0d0;
                font-weight: bold;
            }
        """)
        
        pending_layout.addWidget(self.pending_table)
        self.pending_orders_group.setLayout(pending_layout)
        right_layout.addWidget(self.pending_orders_group)
        
        # 添加持仓信息区域
        positions_group = QGroupBox("持仓信息")
        positions_layout = QVBoxLayout()
        
        # 添加顶部信息栏
        top_info = QWidget()
        top_info_layout = QHBoxLayout(top_info)
        top_info_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加资产信息
        asset_info = QWidget()
        asset_layout = QHBoxLayout(asset_info)
        asset_layout.setContentsMargins(0, 0, 0, 0)
        asset_layout.setSpacing(20)  # 设置标签之间的间距
        
        # 设置资产信息字体
        asset_font = QFont()
        asset_font.setPointSize(9)
        
        # 增加持仓市值和冻结资金标签
        self.cash_label = QLabel("可用资金:   --")
        self.frozen_label = QLabel("冻结资金:   --")  # 新增
        self.market_value_label = QLabel("持仓市值:   --")  # 新增
        self.total_label = QLabel("总资产:     --")
        
        # 设置字体
        for label in [self.cash_label, self.frozen_label, self.market_value_label, self.total_label]:
            label.setFont(asset_font)
        
        asset_layout.addWidget(self.cash_label)
        asset_layout.addWidget(self.frozen_label)
        asset_layout.addWidget(self.market_value_label)
        asset_layout.addWidget(self.total_label)
        top_info_layout.addWidget(asset_info)
        
        # 添加按钮容器
        buttons_widget = QWidget()
        buttons_layout = QHBoxLayout(buttons_widget)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加刷新、清半仓、清仓和交易记录按钮
        refresh_btn = QPushButton("刷新")
        half_clear_btn = QPushButton("清半仓")
        clear_all_btn = QPushButton("一键清仓")
        trade_record_btn = QPushButton("交易记录")  # 新增交易记录按钮
        
        refresh_btn.clicked.connect(self.refresh_positions)
        half_clear_btn.clicked.connect(self.clear_half_positions)
        clear_all_btn.clicked.connect(self.clear_all_positions)
        trade_record_btn.clicked.connect(self.show_trade_record)  # 新增连接
        
        # 设置按钮宽度
        for btn in [refresh_btn, half_clear_btn, clear_all_btn, trade_record_btn]:
            btn.setFixedWidth(100)
        
        # 设置按钮样式
        button_style = """
            QPushButton {
                height: 28px;
                color: white;
                border: none;
                border-radius: 2px;
                font-size: 13px;
                font-weight: bold;
            }
        """
        
        # 设置各按钮的颜色
        refresh_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #17a2b8;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        
        half_clear_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #ffa500;
            }
            QPushButton:hover {
                background-color: #ff8c00;
            }
        """)
        
        clear_all_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #dc3545;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        
        trade_record_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #6c757d;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        
        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(half_clear_btn)
        buttons_layout.addWidget(clear_all_btn)
        buttons_layout.addWidget(trade_record_btn)
        buttons_layout.addStretch()  # 添加弹性空间，使按钮靠右对齐
        
        top_info_layout.addWidget(buttons_widget)
        
        positions_layout.addWidget(top_info)
        
        # 添加持仓表格
        self.positions_table = QTableWidget()
        self.positions_table.setColumnCount(12)  # 增加到12列，添加勾选框列
        self.positions_table.setHorizontalHeaderLabels([
            '选择', '证券代码', '证券名称', '持仓数量', '可用数量', 
            '成本价', '现价', '盈亏', '盈亏率', '市值', '分时图', '做T'
        ])
        
        # 设置持仓表格样式
        self.setup_positions_table_style()
        
        positions_layout.addWidget(self.positions_table)
        
        # 添加盈亏图表
        chart_widget = QWidget()
        chart_layout = QVBoxLayout(chart_widget)
        chart_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加图表按钮容器
        chart_buttons_widget = QWidget()
        chart_buttons_layout = QHBoxLayout(chart_buttons_widget)
        chart_buttons_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加重置按钮
        reset_chart_btn = QPushButton("重置图表")
        reset_chart_btn.clicked.connect(self.reset_profit_chart)
        reset_chart_btn.setFixedWidth(100)  # 设置宽度
        reset_chart_btn.setStyleSheet("""
            QPushButton {
                height: 28px;
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 2px;
                font-size: 13px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)

        # 添加做T勾选框
        self.auto_t_checkbox = QCheckBox("做T")
        self.auto_t_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 13px;
                font-weight: bold;
                color: #17a2b8;
            }
        """)
        self.auto_t_checkbox.stateChanged.connect(self.on_auto_t_changed)
        
        chart_buttons_layout.addWidget(reset_chart_btn)
        chart_buttons_layout.addWidget(self.auto_t_checkbox)
        chart_buttons_layout.addStretch()  # 添加弹性空间，使按钮靠右对齐
        
        chart_layout.addWidget(chart_buttons_widget)
        
        # 创建 PlotWidget
        self.profit_plot = pg.PlotWidget()
        self.profit_plot.setMinimumHeight(400)
        self.profit_plot.setTitle("盈亏走势", color='k', size='12pt')
        
        # 设置 X 轴为时间轴
        self.profit_plot.getAxis('bottom').setLabel('时间')
        self.profit_plot.getAxis('left').setLabel('盈亏(百元)')
        
        # 创建数据线 - 只保留原始曲线和10分钟均线
        self.profit_curve = self.profit_plot.plot(pen=pg.mkPen('r', width=2), name='盈亏')
        self.ma10_curve = self.profit_plot.plot(pen=pg.mkPen('b', width=1), name='10分钟均线')
        
        # 添加图例
        self.profit_plot.addLegend()
        
        # 自定义 X 轴刻度
        class TimeAxisItem(pg.AxisItem):
            def tickStrings(self, values, scale, spacing):
                strings = []
                for value in values:
                    if value < 0:
                        strings.append('')
                        continue
                        
                    # 将时间戳转换回实际时间
                    minutes_from_start = (value - self.start_time) / 60  # 转换为分钟
                    
                    if 0 <= minutes_from_start <= 120:  # 上午时段 (9:30 - 11:30)
                        actual_time = self.morning_start + timedelta(minutes=minutes_from_start)
                        strings.append(actual_time.strftime('%H:%M'))
                    elif 120 < minutes_from_start <= 240:  # 下午时段 (13:00 - 15:00)
                        afternoon_minutes = minutes_from_start - 120
                        actual_time = self.afternoon_start + timedelta(minutes=afternoon_minutes)
                        strings.append(actual_time.strftime('%H:%M'))
                    else:
                        strings.append('')
                
                return strings
            
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                today = datetime.now().date()
                self.morning_start = datetime.combine(today, dt_time(9, 30))
                self.afternoon_start = datetime.combine(today, dt_time(13, 0))
                self.start_time = self.morning_start.timestamp()
            
            def tickValues(self, minVal, maxVal, size):
                # 生成每30分钟一个刻度点
                ticks = []
                for minutes in range(0, 241, 30):  # 0到240分钟，每30分钟一个点
                    ticks.append(self.start_time + minutes * 60)
                return [(0, ticks)]
        
        # 替换默认的底部轴为自定义时间轴
        axis = TimeAxisItem(orientation='bottom')
        self.profit_plot.setAxisItems({'bottom': axis})
        
        # 设置字体
        self.profit_plot.getAxis('bottom').setStyle(tickFont=QFont('Arial', 8))
        
        # 设置X轴范围为交易时间
        today = datetime.now().date()
        morning_start = datetime.combine(today, dt_time(9, 30))
        x_min = morning_start.timestamp()
        x_max = x_min + 240 * 60  # 240分钟后
        self.profit_plot.setXRange(x_min, x_max)
        
        # 设置Y轴初始范围
        self.profit_plot.setYRange(-1, 1)  # 初始范围设为±1
        
        # 添加网格
        self.profit_plot.showGrid(x=True, y=True, alpha=0.3)
        
        # 禁用鼠标缩放X轴
        self.profit_plot.setMouseEnabled(x=False)
        
        # 设置图表大小策略
        self.profit_plot.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        chart_layout.addWidget(self.profit_plot)
        positions_layout.addWidget(chart_widget)
        
        positions_group.setLayout(positions_layout)
        right_layout.addWidget(positions_group)
        
        # 设置右侧两个区域的比例 (移除了系统日志后的新比例)
        right_layout.setStretch(0, 1)    # 待委托列表 - 从2改为1，减少一半高度
        right_layout.setStretch(1, 5)    # 持仓信息 - 从4改为5，增加高度

    def setup_trader(self):
        """初始化交易接口"""
        try:
            if hasattr(self, 'xt_trader'):
                try:
                    self.xt_trader.stop()
                except:
                    pass
            
            self.path = 'd:\\国金证券QMT交易端\\userdata_mini\\'
            self.session_id = int(time.time() * 1000)
            
            self.xt_trader = XtQuantTrader(self.path, self.session_id)
            self.acc = StockAccount('**********')
            self.callback = MyXtQuantTraderCallback(self)
            self.xt_trader.register_callback(self.callback)
            
            # 启动交易接口
            self.xt_trader.start()
            
            # 建立连接
            connect_result = self.xt_trader.connect()
            if connect_result != 0:
                raise Exception("连接失败")
            
            # 订阅回调
            subscribe_result = self.xt_trader.subscribe(self.acc)
            if subscribe_result != 0:
                raise Exception("订阅失败")
            
            # 查询资产信息
            self.query_asset()
            
            # 在成功初始化后添加定时刷新
            self.refresh_timer = QTimer()
            self.refresh_timer.timeout.connect(self.refresh_positions)
            self.refresh_timer.start(3000)  # 每3秒刷新一次持仓
            
            # 添加盈亏曲线更新定时器
            self.profit_chart_timer = QTimer()
            self.profit_chart_timer.timeout.connect(self.update_profit_chart)
            self.profit_chart_timer.start(60000)  # 每分钟更新一次盈亏曲线
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"初始化失败: {str(e)}")
            raise

    def connect_signals(self):
        """连接信号"""
        # 原有的信号连接
        self.buy_button.clicked.connect(self.on_buy)  # 改回原来的方法名
        self.sell_button.clicked.connect(self.on_sell)  # 改回原来的方法名
        self.cancel_button.clicked.connect(self.show_cancel_dialog)
        self.query_button.clicked.connect(self.on_query)
        
        # 价格加减按钮信号
        self.price_plus_btn.clicked.connect(lambda: self.adjust_price(0.01))
        self.price_minus_btn.clicked.connect(lambda: self.adjust_price(-0.01))
        
        # 金额加减按钮信号
        self.amount_plus_btn.clicked.connect(lambda: self.adjust_amount(10000))
        self.amount_minus_btn.clicked.connect(lambda: self.adjust_amount(-10000))
        
        # 价格变化时更新数量
        self.price_edit.textChanged.connect(self.calculate_volume)
        self.amount_edit.textChanged.connect(self.calculate_volume)
        
        # 添加价格类型变化的信号连接
        self.price_type_combo.currentIndexChanged.connect(self.on_price_type_changed)
        
        # 数量加减按钮信号
        self.volume_plus_btn.clicked.connect(lambda: self.adjust_volume(100))
        self.volume_minus_btn.clicked.connect(lambda: self.adjust_volume(-100))

    def adjust_price(self, delta):
        """调整价格"""
        try:
            current = float(self.price_edit.text() or '0')
            new_price = round(current + delta, 2)
            if new_price > 0:  # 确保价格为正
                self.price_edit.setText(f"{new_price:.2f}")
        except ValueError:
            self.price_edit.setText('0.00')

    def adjust_amount(self, delta):
        """调整金额"""
        try:
            # 获取当前金额
            try:
                current = float(self.amount_edit.text() or '0')
            except ValueError:
                current = 0
            
            # 计算新金额
            new_amount = current + delta
            if new_amount >= 0:
                # 直接更新金额，让信号自然触发
                self.amount_edit.setText(str(int(new_amount)))
                
                # 如果是最新价委托，确保价格是最新的
                if self.stock_code_edit.text().strip():
                    if self.price_type_combo.currentText() == '最新价委托':
                        stock_code = self.format_stock_code(self.stock_code_edit.text())
                        tick = xtdata.get_full_tick([stock_code])
                        if tick and stock_code in tick:
                            price = tick[stock_code]["lastPrice"]
                            if price > 0:
                                self.price_edit.setText(self.format_price(price, stock_code))
                
        except Exception as e:
            self.amount_edit.setText('0')
            self.info_append(f"调整金额出错: {str(e)}")

    def calculate_volume(self):
        """根据金额和价格计算数量"""
        try:
            # 如果已经手动输入了数量，则不需要重新计算
            if self.volume_edit.hasFocus() or self.volume_edit.text().strip():
                return
                
            # 1. 获取委托金额
            try:
                amount = float(self.amount_edit.text() or '0')
            except ValueError:
                amount = 0
            
            if amount <= 0:
                self.volume_edit.clear()
                return
            
            # 2. 获取股票代码
            stock_code = self.format_stock_code(self.stock_code_edit.text())
            if not stock_code:
                return
            
            # 3. 根据报价方式获取价格
            price = 0
            price_type = self.price_type_combo.currentText()
            if price_type != '限价委托':  # 非限价委托都使用最新价
                # 查询最新价
                tick = xtdata.get_full_tick([stock_code])
                if tick and stock_code in tick:
                    price = tick[stock_code]["lastPrice"]
                    if price > 0:
                        self.price_edit.setText(self.format_price(price, stock_code))
            else:  # 限价委托
                try:
                    price = float(self.price_edit.text() or '0')
                except ValueError:
                    price = 0
            
            # 4. 计算并更新数量
            if price > 0:
                volume = int(amount / price / 100) * 100  # 向下取整到100的倍数
                self.volume_edit.setText(str(volume))
            
        except Exception as e:
            self.info_append(f"计算数量出错: {str(e)}")

    def adjust_volume(self, delta):
        """调整数量"""
        try:
            current = int(self.volume_edit.text() or '0')
            new_volume = current + delta
            if new_volume >= 0:  # 确保数量为非负
                self.volume_edit.setText(str(new_volume))
        except ValueError:
            self.volume_edit.setText('0')

    def format_stock_code(self, code):
        """格式化证券代码，添加市场后缀"""
        if '.' not in code:
            if code.startswith('5'):  # 上海基金
                code = f"{code}.SH"
            elif code.startswith('15'):  # 深圳基金
                code = f"{code}.SZ"
            elif code.startswith('6'):  # 上海股票
                code = f"{code}.SH"
            elif code.startswith(('0', '3')):  # 深圳股票
                code = f"{code}.SZ"
            elif code.startswith('11'):  # 上海可转债
                code = f"{code}.SH"
            elif code.startswith('12'):  # 深圳可转债
                code = f"{code}.SZ"
            else:
                raise ValueError("无效的证券代码格式")
        return code

    def format_price(self, price, stock_code):
        """根据证券类型格式化价格"""
        if self.is_fund(stock_code):
            return f"{price:.3f}"  # 基金显示3位小数
        return f"{price:.2f}"  # 股票显示2位小数

    def is_fund(self, stock_code):
        """判断是否为基金"""
        if not stock_code:
            return False
        # 基金代码规则：
        # 上海基金以50/51/52开头
        # 深圳基金以15/16/17/18开头
        fund_prefixes = ('50', '51', '52', '15', '16', '17', '18')
        code = stock_code.split('.')[0]
        return code.startswith(fund_prefixes)

    def on_buy(self):
        """买入处理"""
        try:
            if self.later_checkbox.isChecked():
                self.save_pending_order(xtconstant.STOCK_BUY)
                return
            
            # 以下是直接委托的逻辑
            stock_code = self.format_stock_code(self.stock_code_edit.text())
            if not stock_code:
                raise ValueError("请输入正确的证券代码")
            
            # 获取数量：优先使用已输入的数量
            volume = self.volume_edit.text().strip()
            if not volume:
                raise ValueError("请输入委托数量")
            volume = int(volume)
            if volume <= 0:
                raise ValueError("委托数量必须大于0")
            
            # 获取价格和委托类型
            price_type = self.price_type_combo.currentText()
            price = 0  # 默认价格为0
            
            if price_type == '限价委托':
                # 只在限价委托时验证价格
                price = float(self.price_edit.text() or '0')
                if price <= 0:
                    raise ValueError("请输入正确的委托价格")
                price_type_value = xtconstant.FIX_PRICE
            else:  # 最新价委托和最优五档即时成交
                # 获取最新价
                tick = xtdata.get_full_tick([stock_code])
                if not tick or stock_code not in tick:
                    raise ValueError("获取最新价失败")
                price = tick[stock_code]["lastPrice"]
                if price <= 0:
                    raise ValueError("获取最新价失败")
                
                # 根据委托类型设置对应的价格类型
                if price_type == '最优五档即时成交':
                    if stock_code.startswith(('6', '5')):  # 上交所
                        price_type_value = xtconstant.MARKET_SH_CONVERT_5_CANCEL
                    else:  # 深交所
                        price_type_value = xtconstant.MARKET_SZ_CONVERT_5_CANCEL
                else:  # 最新价委托
                    price_type_value = xtconstant.MARKET_PEER_PRICE_FIRST
            
            # 打印界面下单的详细参数
            self.info_append("\n=== 界面下单请求 ===")
            self.info_append(f"股票代码: {stock_code}")
            self.info_append(f"委托方向: 买入")
            self.info_append(f"委托数量: {volume}")
            self.info_append(f"价格类型: {price_type_value}")
            self.info_append(f"委托价格: {price}")
            
            # 执行下单
            order_id = self.xt_trader.order_stock(
                self.acc,
                stock_code,
                xtconstant.STOCK_BUY,
                volume,
                price_type_value,
                price,
                "strategy",
                "remark"
            )
            
            # 打印下单结果
            if order_id > 0:
                self.info_append(f"界面下单成功，委托号: {order_id}")
            else:
                error_msg = f"界面下单失败，返回ID: {order_id}"
                self.info_append(error_msg)
                # 移除获取错误信息的尝试，直接抛出异常
                raise Exception(error_msg)
            
        except Exception as e:
            QMessageBox.warning(self, "错误", str(e))

    def on_sell(self):
        """卖出处理"""
        try:
            if self.later_checkbox.isChecked():
                self.save_pending_order(xtconstant.STOCK_SELL)
                return
            
            # 以下是直接委托的逻辑
            stock_code = self.format_stock_code(self.stock_code_edit.text())
            if not stock_code:
                raise ValueError("请输入正确的证券代码")
            
            # 获取数量：优先使用已输入的数量
            volume = self.volume_edit.text().strip()
            if not volume:
                raise ValueError("请输入委托数量")
            volume = int(volume)
            if volume <= 0:
                raise ValueError("委托数量必须大于0")
            
            # 获取价格和委托类型
            price_type = self.price_type_combo.currentText()
            price = 0  # 默认价格为0
            
            if price_type == '限价委托':
                # 只在限价委托时验证价格
                price = float(self.price_edit.text() or '0')
                if price <= 0:
                    raise ValueError("请输入正确的委托价格")
                price_type_value = xtconstant.FIX_PRICE
            else:  # 最新价委托和最优五档即时成交
                # 获取最新价
                tick = xtdata.get_full_tick([stock_code])
                if not tick or stock_code not in tick:
                    raise ValueError("获取最新价失败")
                price = tick[stock_code]["lastPrice"]
                if price <= 0:
                    raise ValueError("获取最新价失败")
                
                # 根据委托类型设置对应的价格类型
                if price_type == '最优五档即时成交':
                    if stock_code.startswith(('6', '5')):  # 上交所
                        price_type_value = xtconstant.MARKET_SH_CONVERT_5_CANCEL
                    else:  # 深交所
                        price_type_value = xtconstant.MARKET_SZ_CONVERT_5_CANCEL
                else:  # 最新价委托
                    price_type_value = xtconstant.MARKET_PEER_PRICE_FIRST
            
            # 执行委托
            order_id = self.xt_trader.order_stock(
                self.acc,
                stock_code,
                xtconstant.STOCK_SELL,
                volume,
                price_type_value,
                price,
                "strategy",
                "remark"
            )
            
            if order_id > 0:
                price_display = "最优价" if price_type == '最优五档即时成交' else (
                    "最新价" if price_type == '最新价委托' else self.format_price(price, stock_code)
                )
                self.info_append(f"卖出委托已提交 - 股票:{stock_code} 数量:{volume} 价格:{price_display}")
                self.clear_input_fields()  # 委托成功后清空输入框
            else:
                raise Exception("委托提交失败")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", str(e))

    def show_cancel_dialog(self):
        """显示撤单选择对话框"""
        try:
            # 创建撤单对话框
            self.cancel_dialog = QDialog(self)
            self.cancel_dialog.setWindowTitle("可撤销委托")
            self.cancel_dialog.setModal(True)
            layout = QVBoxLayout(self.cancel_dialog)
            
            # 创建委托列表表格
            self.cancel_table = QTableWidget()
            layout.addWidget(self.cancel_table)
            
            # 设置表格基本属性
            self.cancel_table.setColumnCount(8)
            self.cancel_table.setHorizontalHeaderLabels([
                '委托号', '股票代码', '方向', '状态',
                '委托价格', '委托数量', '成交数量', '委托时间'
            ])
            
            # 设置表格样式
            self.setup_cancel_table_style()
            
            # 设置双击处理
            self.cancel_table.itemDoubleClicked.connect(
                lambda item: self.handle_cancel_selection(item, self.cancel_dialog)
            )
            
            # 设置对话框大小
            self.cancel_dialog.resize(1400, 600)
            
            # 初始刷新数据
            self.refresh_cancel_table()
            
            self.cancel_dialog.exec_()
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"显示撤单对话框失败: {str(e)}")

    def setup_cancel_table_style(self):
        """设置撤单表格样式"""
        # 设置字体
        font = self.cancel_table.font()
        font.setPointSize(10)
        self.cancel_table.setFont(font)
        
        # 设置列宽
        self.cancel_table.setColumnWidth(0, 150)  # 委托号
        self.cancel_table.setColumnWidth(1, 120)  # 股票代码
        self.cancel_table.setColumnWidth(2, 80)   # 方向
        self.cancel_table.setColumnWidth(3, 100)  # 状态
        self.cancel_table.setColumnWidth(4, 100)  # 委托价格
        self.cancel_table.setColumnWidth(5, 100)  # 委托数量
        self.cancel_table.setColumnWidth(6, 100)  # 成交数量
        self.cancel_table.setColumnWidth(7, 200)  # 委托时间
        
        # 设置表格属性
        self.cancel_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.cancel_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.cancel_table.horizontalHeader().setStretchLastSection(True)
        self.cancel_table.verticalHeader().setDefaultSectionSize(30)
        
        # 添加以下代码来防止焦点自动跳转
        self.cancel_table.setFocusPolicy(Qt.StrongFocus)
        self.cancel_table.horizontalHeader().setFocusPolicy(Qt.NoFocus)
        self.cancel_table.verticalHeader().setFocusPolicy(Qt.NoFocus)
        
        # 添加滚动条设置
        scrollbar = self.cancel_table.verticalScrollBar()
        scrollbar.setFocusPolicy(Qt.NoFocus)
        
        self.cancel_table.setVerticalScrollMode(QTableWidget.ScrollPerPixel)
        self.cancel_table.setHorizontalScrollMode(QTableWidget.ScrollPerPixel)
        self.cancel_table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.cancel_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 禁用自动滚动
        self.cancel_table.setAutoScroll(False)
        
        # 设置滚动模式为按项滚动
        self.cancel_table.setVerticalScrollMode(QTableWidget.ScrollPerItem)
        
        # 添加事件过滤器
        self.cancel_table.viewport().installEventFilter(self)
        self.cancel_table.verticalScrollBar().installEventFilter(self)

    def handle_cancel_selection(self, item, dialog):
        """处理撤单选择"""
        try:
            row = item.row()
            order_id = int(self.cancel_table.item(row, 0).text())
            dialog.accept()  # 先关闭对话框
            self.on_cancel(order_id)
            
            # 延迟一下再重新打开撤单窗口
            QTimer.singleShot(100, self.show_cancel_dialog)
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"撤单操作失败: {str(e)}")

    def on_cancel(self, order_id):
        """执行撤单操作"""
        try:
            # 获取委托信息
            order = self.xt_trader.query_stock_order(self.acc, order_id)
            if not order:
                raise Exception("未找到委托信息")
            
            # 获取市场信息
            market = 0  # 投研端市场参数可以填0
            order_sysid = order.order_sysid  # 获取柜台合同编号
            
            # 使用异步撤单接口
            result = self.xt_trader.cancel_order_stock_sysid_async(
                self.acc,
                market, 
                order_sysid
            )
            
            if result > 0:
                self.info_append(f"撤单请求已提交，委托号: {order_id}，柜台合同编号: {order_sysid}")
            else:
                self.info_append(f"撤单请求失败，委托号: {order_id}，柜台合同编号: {order_sysid}")
                QMessageBox.warning(self, "错误", "撤单请求失败")
            
        except Exception as e:
            self.info_append(f"撤单出错: {str(e)}")
            QMessageBox.warning(self, "错误", str(e))

    def on_query(self):
        try:
            # 先确保连接正常
            self.ensure_connection()
            
            # 使用QTimer来设置查询超时
            self.query_timer = QTimer()
            self.query_timer.setSingleShot(True)
            self.query_timer.timeout.connect(self.on_query_timeout)
            self.query_timer.start(5000)  # 5秒超时
            
            # 查询资金
            self.info_append("\n==== 资金信息 ====")
            asset = self.xt_trader.query_stock_asset(self.acc)
            if asset:
                self.info_append(f"可用资金: {asset.cash:.2f}")
                self.info_append(f"总资产: {asset.total_asset:.2f}")
                self.info_append(f"市值: {asset.market_value:.2f}")
                self.info_append(f"冻结资金: {asset.frozen_cash:.2f}")
                
                # 更新资产显示
                self.cash_label.setText(f"可用资金:   {asset.cash:>13,.2f}")
                self.total_label.setText(f"总资产:     {asset.total_asset:>13,.2f}")
            else:
                self.info_append("未查询到资金信息")
            
            # 查询持仓
            self.info_append("\n==== 持仓信息 ====")
            positions = self.xt_trader.query_stock_positions(self.acc)
            if positions:
                # 过滤掉持仓量为0的证券
                valid_positions = [pos for pos in positions if pos.volume > 0]
                
                if valid_positions:
                    for pos in valid_positions:
                        self.info_append(
                            f"股票代码: {pos.stock_code}\n"
                            f"  持仓数量: {pos.volume}\n"
                            f"  可用数量: {pos.can_use_volume}\n"
                            f"  开仓价: {pos.open_price:.3f}\n"
                            f"  成本价: {pos.avg_price:.3f}\n"
                            f"  市值: {pos.market_value:.2f}\n"
                            f"  冻结数量: {pos.frozen_volume}\n"
                            f"  在途股份: {pos.on_road_volume}\n"
                            f"  昨日持仓: {pos.yesterday_volume}"
                        )
                else:
                    self.info_append("当前无持仓")
            else:
                self.info_append("当前无持仓")

            # 合并显示当日委托和成交
            self.info_append("\n==== 当日交易记录 ====")
            
            # 获取委托和成交记录
            orders = self.xt_trader.query_stock_orders(self.acc)
            trades = self.xt_trader.query_stock_trades(self.acc)
            
            # 过滤掉委托号为0的委托
            valid_orders = [order for order in orders if order.order_id > 0] if orders else []
            
            if not valid_orders and not trades:
                self.info_append("当日无交易记录")
                return
            
            # 创建委托号到成交信息的映射
            trade_map = {}
            if trades:
                for trade in trades:
                    if trade.order_id not in trade_map:
                        trade_map[trade.order_id] = []
                    trade_map[trade.order_id].append(trade)
            
            # 显示委托及其对应的成交信息
            for order in valid_orders:
                direction = "买入" if order.order_type == xtconstant.STOCK_BUY else "卖出"
                # 使用 XT Trader 文档中定义的状态码
                status_map = {
                    xtconstant.ORDER_UNREPORTED: "未报",          # 48
                    xtconstant.ORDER_WAIT_REPORTING: "待报",      # 49
                    xtconstant.ORDER_REPORTED: "已报",            # 50
                    xtconstant.ORDER_REPORTED_CANCEL: "已报待撤",  # 51
                    xtconstant.ORDER_PARTSUCC_CANCEL: "部成待撤",  # 52
                    xtconstant.ORDER_PART_CANCEL: "部撤",         # 53
                    xtconstant.ORDER_CANCELED: "已撤",            # 54
                    xtconstant.ORDER_PART_SUCC: "部成",          # 55
                    xtconstant.ORDER_SUCCEEDED: "已成",           # 56
                    xtconstant.ORDER_JUNK: "废单",               # 57
                    xtconstant.ORDER_UNKNOWN: "未知"             # 255
                }
                
                status = status_map.get(order.order_status, f"未知状态({order.order_status})")
                # 移除调试信息输出
                
                # 显示委托信息
                self.info_append(
                    f"委托号: {order.order_id}\n"
                    f"  股票: {order.stock_code}\n"
                    f"  方向: {direction}\n"
                    f"  状态: {status}\n"
                    f"  委托价格: {order.price:.3f}\n"
                    f"  委托数量: {order.order_volume}\n"
                    f"  成交数量: {order.traded_volume}\n"
                    f"  委托时间: {order.order_time}"
                )
                
                # 显示该委托对应的成交记录
                if order.order_id in trade_map:
                    self.info_append("  成交明细:")
                    for trade in trade_map[order.order_id]:
                        self.info_append(
                            f"    成交编号: {trade.traded_id}\n"
                            f"    成交价格: {trade.traded_price:.3f}\n"
                            f"    成交数量: {trade.traded_volume}\n"
                            f"    成交金额: {trade.traded_amount:.2f}\n"
                            f"    成交时间: {trade.traded_time}"
                        )
                self.info_append("")  # 添加空行分隔
            
            # 显示未找到对应委托的成交记录
            orphan_trades = [trade for trade in trades if trade.order_id not in {order.order_id for order in valid_orders}]
            if orphan_trades:
                self.info_append("其他成交记录:")
                for trade in orphan_trades:
                    direction = "买入" if trade.order_type == xtconstant.STOCK_BUY else "卖出"
                    self.info_append(
                        f"成交编号: {trade.traded_id}\n"
                        f"  股票: {trade.stock_code}\n"
                        f"  方向: {direction}\n"
                        f"  成交价格: {trade.traded_price:.3f}\n"
                        f"  成交数量: {trade.traded_volume}\n"
                        f"  成交金额: {trade.traded_amount:.2f}\n"
                        f"  成交时间: {trade.traded_time}"
                    )
                
            # 查询完成，停止定时器
            self.query_timer.stop()

        except Exception as e:
            self.info_append(f"查询出错: {str(e)}")
            QMessageBox.warning(self, "错误", str(e))
            # 打印详细错误信息以便调试
            import traceback
            self.info_append(f"错误详情:\n{traceback.format_exc()}")
        finally:
            # 确保定时器被停止
            if hasattr(self, 'query_timer'):
                self.query_timer.stop()

    def on_query_timeout(self):
        """查询超时处理"""
        self.info_append("查询超时，请重试")
        QMessageBox.warning(self, "提示", "查询超时，请重试")
        
        # 尝试重新初始化连接
        try:
            self.setup_trader()
        except:
            pass

    def on_trade(self, trade):
        """成交回报推送处理"""
        try:
            direction = "买入" if trade.order_type == xtconstant.STOCK_BUY else "卖出"
            msg = (f"成交回报 - 委托号:{trade.order_id}\n"
                   f"  股票代码: {trade.stock_code}\n"
                   f"  方向: {direction}\n"
                   f"  成交数量: {trade.traded_volume}\n"
                   f"  成交价格: {trade.traded_price:.3f}\n"
                   f"  成交金额: {trade.traded_amount:.2f}")
            self.info_append(msg)
        except Exception as e:
            self.info_append(f"处理成交回报出错: {str(e)}")

    def on_order_error(self, order_error):
        """委托失败推送处理"""
        try:
            msg = (f"委托失败 - 委托号:{order_error.order_id}\n"
                   f"  错误代码: {order_error.error_id}\n"
                   f"  错误信息: {order_error.error_msg}")
            self.info_append(msg)
        except Exception as e:
            self.info_append(f"处理委托失败回报出错: {str(e)}")

    def on_cancel_error(self, cancel_error):
        """撤单失败推送处理"""
        try:
            msg = (f"撤单失败 - 委托号:{cancel_error.order_id}\n"
                   f"  错误代码: {cancel_error.error_id}\n"
                   f"  错误信息: {cancel_error.error_msg}")
            self.info_append(msg)
        except Exception as e:
            self.info_append(f"处理撤单失败回报出错: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭时的处理"""
        try:
            if hasattr(self, 'process'):
                self.process.kill()  # 确保子进程被终止
                self.process.waitForFinished()
            if hasattr(self, 'xt_trader'):
                self.xt_trader.stop()
            if hasattr(self, 'refresh_timer'):
                self.refresh_timer.stop()
            if hasattr(self, 'profit_chart_timer'):
                self.profit_chart_timer.stop()
            self.asset_timer.stop()
        except:
            pass
        event.accept()

    def ensure_connection(self):
        """确保交易接口连接正常"""
        try:
            # 检查连接状态
            if not hasattr(self, 'xt_trader') or not self.xt_trader:
                self.setup_trader()
                return
            
            # 尝试重新连接
            connect_result = self.xt_trader.connect()
            if connect_result != 0:
                # 如果连接失败，重新初始化
                self.xt_trader.stop()
                time.sleep(1)  # 等待一下
                self.setup_trader()
            
            # 确保订阅
            subscribe_result = self.xt_trader.subscribe(self.acc)
            if subscribe_result != 0:
                raise Exception("订阅失败")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"连接失败: {str(e)}")
            raise

    def refresh_cancel_table(self):
        """刷新撤单表格"""
        try:
            orders = self.xt_trader.query_stock_orders(self.acc)
            
            # 过滤掉委托号为0和不可撤销的委托
            valid_orders = [
                order for order in orders 
                if order.order_id > 0 and order.order_status not in [5, 6, 8, 9, 10]
            ]
            
            # 更新表格
            self.cancel_table.setRowCount(len(valid_orders))
            
            if not valid_orders:
                self.cancel_dialog.accept()  # 关闭对话框
                QMessageBox.information(self, "提示", "当前没有可撤销的委托")
                return
            
            for row, order in enumerate(valid_orders):
                direction = "买入" if order.order_type == xtconstant.STOCK_BUY else "卖出"
                
                # 转换状态码为状态文本
                status_text = {
                    48: "未报",
                    49: "待报",
                    50: "已报",
                    51: "已报待撤",
                    52: "部成待撤",
                    53: "部撤",
                    54: "已撤",
                    55: "部成",
                    56: "已成",
                    57: "废单",
                    255: "未知"
                }.get(order.order_status, str(order.order_status))
                
                # 转换时间戳为北京时间
                try:
                    time_obj = datetime.fromtimestamp(int(order.order_time))
                    time_str = time_obj.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    time_str = order.order_time
                
                items = [
                    str(order.order_id),
                    order.stock_code,
                    direction,
                    status_text,
                    f"{order.price:.3f}",
                    str(order.order_volume),
                    str(order.traded_volume),
                    time_str
                ]
                
                for col, item in enumerate(items):
                    table_item = QTableWidgetItem(str(item))
                    table_item.setTextAlignment(Qt.AlignCenter)
                    
                    # 根据委托状态设置不同颜色
                    if order.order_status in [54, 57]:  # 已撤、废单
                        table_item.setForeground(QColor(0, 128, 0))  # 绿色
                    elif order.traded_volume < order.order_volume:  # 未完全成交
                        table_item.setForeground(QColor(255, 0, 0))  # 红色
                    
                    self.cancel_table.setItem(row, col, table_item)
                    
        except Exception as e:
            QMessageBox.warning(self, "错误", f"刷新失败: {str(e)}")
            self.cancel_dialog.accept()  # 出错时也关闭对话框

    def query_asset(self):
        """查询资产信息"""
        try:
            asset = self.xt_trader.query_stock_asset(self.acc)
            if asset:
                self.cash_label.setText(f"可用资金:   {asset.cash:>13,.2f}")
                self.total_label.setText(f"总资产:     {asset.total_asset:>13,.2f}")
            else:
                self.info_text.append("资产查询失败")
        except Exception as e:
            self.info_text.append(f"资产查询出错: {str(e)}")

    def clear_input_fields(self):
        """清空输入框"""
        try:
            # 检查信号是否已连接再断开
            try:
                self.amount_edit.textChanged.disconnect(self.calculate_volume)
            except:
                pass
            
            try:
                self.price_edit.textChanged.disconnect(self.calculate_volume)
            except:
                pass
            
            try:
                self.price_type_combo.currentIndexChanged.disconnect(self.calculate_volume)
            except:
                pass
            
            # 清空输入框
            self.stock_code_edit.clear()
            self.price_edit.clear()
            self.amount_edit.setText("0")  # 恢复默认金额为0
            self.volume_edit.clear()
            self.price_type_combo.setCurrentIndex(0)  # 恢复默认为最新价委托
            self.later_checkbox.setChecked(False)  # 取消稍后委托勾选
            
        finally:
            # 重新连接信号
            self.amount_edit.textChanged.connect(self.calculate_volume)
            self.price_edit.textChanged.connect(self.calculate_volume)
            self.price_type_combo.currentIndexChanged.connect(self.calculate_volume)

    def save_pending_order(self, order_type):
        """保存待委托订单"""
        try:
            # 验证股票代码
            stock_code = self.format_stock_code(self.stock_code_edit.text())
            if not stock_code:
                raise ValueError("请输入正确的证券代码")
            
            # 验证数量
            volume = self.volume_edit.text().strip()
            if not volume:
                raise ValueError("请输入委托数量")
            volume = int(volume)
            if volume <= 0:
                raise ValueError("委托数量必须大于0")
            
            # 获取价格和委托类型
            price_type = self.price_type_combo.currentText()
            price = 0  # 默认价格为0
            
            if price_type == '限价委托':
                # 只在限价委托时验证价格
                price = float(self.price_edit.text() or '0')
                if price <= 0:
                    raise ValueError("请输入正确的委托价格")
            else:  # 最新价委托和最优五档即时成交
                # 获取最新价用于显示
                tick = xtdata.get_full_tick([stock_code])
                if tick and stock_code in tick:
                    price = tick[stock_code]["lastPrice"]
            
            # 添加到待委托列表
            row = self.pending_table.rowCount()
            self.pending_table.insertRow(row)
            
            # 设置单元格数据
            items = [
                stock_code,
                "买入" if order_type == xtconstant.STOCK_BUY else "卖出",
                price_type,  # 保存价格类型
                "最优价" if price_type == '最优五档即时成交' else (
                    "最新价" if price_type == '最新价委托' else self.format_price(price, stock_code)
                ),
                str(volume),
                f"{price * volume:,.2f}" if price > 0 else "--",
                "委托",
                "删除"
            ]
            
            for col, item in enumerate(items[:-2]):  # 除了最后两列
                table_item = QTableWidgetItem(str(item))
                table_item.setTextAlignment(Qt.AlignCenter)
                self.pending_table.setItem(row, col, table_item)
            
            # 添加委托和删除按钮
            for col in [6, 7]:  # 最后两列
                btn = QPushButton(items[col])
                btn.setFixedWidth(65)  # 设置固定宽度
                
                # 设置按钮样式
                if col == 6:  # 委托按钮
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #007bff;
                            color: white;
                            border: none;
                            padding: 4px;
                            border-radius: 2px;
                            font-size: 12px;
                            height: 24px;
                        }
                        QPushButton:hover {
                            background-color: #0069d9;
                        }
                    """)
                    btn.clicked.connect(lambda checked, r=row: self.commit_pending_order(r))
                else:  # 删除按钮
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #dc3545;
                            color: white;
                            border: none;
                            padding: 4px;
                            border-radius: 2px;
                            font-size: 12px;
                            height: 24px;
                        }
                        QPushButton:hover {
                            background-color: #c82333;
                        }
                    """)
                    btn.clicked.connect(lambda checked, r=row: self.delete_pending_order(r))
                
                self.pending_table.setCellWidget(row, col, btn)
            
            # 清空输入框
            self.clear_input_fields()
            
            # 记录到日志
            self.info_append(f"已添加待委托 - 股票:{stock_code} 方向:{'买入' if order_type == xtconstant.STOCK_BUY else '卖出'}")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", str(e))

    def save_pending_orders(self):
        """保存待委托列表到文件"""
        try:
            orders = []
            for row in range(self.pending_table.rowCount()):
                order = {
                    'stock_code': self.pending_table.item(row, 0).text(),
                    'direction': self.pending_table.item(row, 1).text(),
                    'price_type': self.pending_table.item(row, 2).text(),
                    'price': self.pending_table.item(row, 3).text(),
                    'volume': self.pending_table.item(row, 4).text(),
                    'amount': self.pending_table.item(row, 5).text()
                }
                orders.append(order)
            
            # 保存到文件
            with open('f:/work/pending_orders.json', 'w', encoding='utf-8') as f:
                json.dump(orders, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            self.info_append(f"保存待委托列表失败: {str(e)}")

    def load_pending_orders(self):
        """加载待委托列表"""
        try:
            if not os.path.exists('f:/work/pending_orders.json'):
                return
                
            with open('f:/work/pending_orders.json', 'r', encoding='utf-8') as f:
                orders = json.load(f)
                
            # 清空当前表格
            self.pending_table.setRowCount(0)
            
            # 添加委托记录
            for order in orders:
                row = self.pending_table.rowCount()
                self.pending_table.insertRow(row)
                
                # 添加数据
                items = [
                    order['stock_code'],
                    order['direction'],
                    order['price_type'],
                    order['price'],
                    order['volume'],
                    order['amount']
                ]
                
                for col, item in enumerate(items):
                    self.pending_table.setItem(row, col, QTableWidgetItem(str(item)))
                
                # 添加委托和删除按钮
                commit_btn = QPushButton("委托")
                commit_btn.setFixedWidth(65)
                commit_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #007bff;
                        color: white;
                        border: none;
                        padding: 4px;
                        border-radius: 2px;
                        font-size: 12px;
                        height: 24px;
                    }
                    QPushButton:hover {
                        background-color: #0069d9;
                    }
                """)
                commit_btn.clicked.connect(lambda checked, r=row: self.commit_pending_order(r))
                
                delete_btn = QPushButton("删除")
                delete_btn.setFixedWidth(65)
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #dc3545;
                        color: white;
                        border: none;
                        padding: 4px;
                        border-radius: 2px;
                        font-size: 12px;
                        height: 24px;
                    }
                    QPushButton:hover {
                        background-color: #c82333;
                    }
                """)
                delete_btn.clicked.connect(lambda checked, r=row: self.delete_pending_order(r))
                
                self.pending_table.setCellWidget(row, 6, commit_btn)
                self.pending_table.setCellWidget(row, 7, delete_btn)
                
        except Exception as e:
            self.info_append(f"加载待委托列表失败: {str(e)}")

    def delete_pending_order(self, row):
        """删除待委托单"""
        try:
            # 获取实际选中的行
            selected_rows = self.pending_table.selectionModel().selectedRows()
            if selected_rows:
                # 如果有选中的行，使用选中的行
                actual_row = selected_rows[0].row()
            else:
                # 否则使用传入的行号
                actual_row = row
            
            # 确保行号有效
            if actual_row < 0 or actual_row >= self.pending_table.rowCount():
                raise ValueError("无效的行号")
            
            # 从表格中删除
            self.pending_table.removeRow(actual_row)
            
            # 保存更新后的待委托列表
            self.save_pending_orders()
            
            # 刷新表格显示
            self.pending_table.viewport().update()
            
            self.info_append("已删除待委托单")
            
        except Exception as e:
            self.info_append(f"删除待委托单失败: {str(e)}")

    def commit_all_pending_orders(self):
        """执行所有待委托订单"""
        try:
            while self.pending_table.rowCount() > 0:
                self.commit_pending_order(0)  # 总是执行第一行，因为成功后会自动删除
            self.info_append("已执行所有待委托订单")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"执行全部待委托失败: {str(e)}")

    def setup_positions_table_style(self):
        """设置持仓表格样式"""
        # 设置字体
        font = self.positions_table.font()
        font.setPointSize(10)
        self.positions_table.setFont(font)
        
        # 设置列宽
        column_widths = [
            40,    # 选择
            80,    # 证券代码
            100,   # 证券名称
            80,    # 持仓数量
            80,    # 可用数量
            80,    # 成本价
            80,    # 现价
            100,   # 盈亏
            80,    # 盈亏率
            100,   # 市值
            65,    # 分时图
            40     # 做T
        ]
        
        # 设置每列的宽度
        for col, width in enumerate(column_widths):
            self.positions_table.setColumnWidth(col, width)
        
        # 设置表格属性
        self.positions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.positions_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.positions_table.horizontalHeader().setStretchLastSection(True)
        self.positions_table.verticalHeader().setDefaultSectionSize(30)
        
        # 添加以下代码来防止焦点自动跳转
        self.positions_table.setFocusPolicy(Qt.StrongFocus)
        self.positions_table.horizontalHeader().setFocusPolicy(Qt.NoFocus)
        self.positions_table.verticalHeader().setFocusPolicy(Qt.NoFocus)
        
        # 获取表格的滚动区域并设置焦点策略
        scrollbar = self.positions_table.verticalScrollBar()
        scrollbar.setFocusPolicy(Qt.NoFocus)
        
        # 设置表格的滚动模式
        self.positions_table.setVerticalScrollMode(QTableWidget.ScrollPerPixel)
        self.positions_table.setHorizontalScrollMode(QTableWidget.ScrollPerPixel)
        
        # 设置表格的滚动条行为
        self.positions_table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.positions_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 设置表格样式
        self.positions_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                border: 1px solid #d0d0d0;
            }
            QTableWidget::item {
                padding: 4px;
                color: black;
            }
            QHeaderView::section {
                background-color: #f8f8f8;
                padding: 4px;
                border: none;
                border-right: 1px solid #d0d0d0;
                border-bottom: 1px solid #d0d0d0;
                font-weight: bold;
            }
        """)

        # 添加双击信号连接
        self.positions_table.itemDoubleClicked.connect(self.on_position_double_clicked)
        
        # 禁用自动滚动
        self.positions_table.setAutoScroll(False)
        
        # 添加滚动事件过滤器
        self.positions_table.viewport().installEventFilter(self)
        self.positions_table.verticalScrollBar().installEventFilter(self)
        
        # 设置滚动模式为按项滚动
        self.positions_table.setVerticalScrollMode(QTableWidget.ScrollPerItem)

    def on_position_double_clicked(self, item):
        """处理持仓表格双击事件"""
        try:
            row = item.row()
            
            # 获取股票信息
            stock_code = self.positions_table.item(row, 1).text()
            available_volume = int(self.positions_table.item(row, 4).text())
            
            # 如果可用数量为0，不执行操作
            if available_volume <= 0:
                QMessageBox.warning(self, "提示", "当前股票无可用数量")
                return
            
            # 获取最新价格
            tick = xtdata.get_full_tick([stock_code])
            if not tick or stock_code not in tick:
                QMessageBox.warning(self, "错误", "获取最新价失败")
                return
            
            current_price = tick[stock_code]["lastPrice"]
            
            # 根据证券类型设置委托价格
            if self.is_etf(stock_code):
                order_price = round(current_price - 0.003, 3)  # ETF减去0.003
            else:
                order_price = round(current_price - 0.10, 2)   # 股票减去0.10
            
            # 弹出确认对话框
            reply = QMessageBox.question(
                self, 
                "确认卖出", 
                f"是否以 {order_price} 价格卖出 {stock_code} {available_volume} 股？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # 执行卖出
                order_id = self.xt_trader.order_stock(
                    self.acc,
                    stock_code,
                    xtconstant.STOCK_SELL,
                    available_volume,
                    xtconstant.FIX_PRICE,  # 改为限价委托
                    order_price,  # 使用计算好的委托价格
                    "strategy",
                    "remark"
                )
                
                if order_id > 0:
                    self.info_append(f"卖出委托已提交 - 股票:{stock_code} 数量:{available_volume} 价格:{order_price}")
                else:
                    raise Exception("委托提交失败")
        
        except Exception as e:
            QMessageBox.warning(self, "错误", f"卖出操作失败: {str(e)}")

    def is_etf(self, stock_code):
        """判断是否为ETF"""
        if not stock_code:
            return False
        # ETF代码规则：
        # 上海ETF以51开头
        # 深圳ETF以15开头
        code = stock_code.split('.')[0]
        return code.startswith(('51', '15'))

    def is_money_fund(self, stock_code):
        """判断是否为货币基金"""
        money_funds = [
            "511800.SH",  # 易方达货币ETF
            "511880.SH",  # 银华日利
            "511990.SH",  # 华宝添益
            "511660.SH",  # 建信添益
            # 可以继续添加其他货币基金代码
        ]
        return stock_code in money_funds

    def refresh_positions(self):
        """刷新持仓信息"""
        try:
            # 保存当前的选择状态
            selected_stocks = {}
            for row in range(self.positions_table.rowCount()):
                checkbox = self.positions_table.cellWidget(row, 0)
                if checkbox and isinstance(checkbox.layout().itemAt(0).widget(), QCheckBox):
                    stock_code = self.positions_table.item(row, 1).text()
                    selected_stocks[stock_code] = checkbox.layout().itemAt(0).widget().isChecked()

            # 清空表格
            self.positions_table.setRowCount(0)
            
            # 获取持仓信息
            positions = self.xt_trader.query_stock_positions(self.acc)
            if not positions:
                return
                
            # 过滤掉持仓数量为0的证券
            positions = [pos for pos in positions if pos.volume > 0]
            if not positions:
                return
                
            # 获取所有股票代码
            stock_codes = [pos.stock_code for pos in positions]
            
            # 批量获取股票名称和最新价
            ticks = xtdata.get_full_tick(stock_codes)
            
            # 计算总盈亏（不包括货币基金）
            self.current_total_profit = 0
            
            # 更新表格
            for pos in positions:
                row = self.positions_table.rowCount()
                self.positions_table.insertRow(row)
                
                # 添加勾选框到第一列
                checkbox_widget = QWidget()
                checkbox_layout = QHBoxLayout(checkbox_widget)
                checkbox_layout.setContentsMargins(0, 0, 0, 0)
                checkbox_layout.setAlignment(Qt.AlignCenter)
                checkbox = QCheckBox()
                # 恢复之前的选择状态
                if pos.stock_code in selected_stocks:
                    checkbox.setChecked(selected_stocks[pos.stock_code])
                checkbox_layout.addWidget(checkbox)
                self.positions_table.setCellWidget(row, 0, checkbox_widget)
                
                # 获取股票名称和最新价
                tick_data = ticks.get(pos.stock_code, {})
                
                # 尝试获取证券名称
                stock_name = ""
                # 1. 先尝试从tick数据获取
                stock_name = tick_data.get("name", "")
                
                # 2. 如果没有获取到，尝试从合约信息获取
                if not stock_name:
                    instrument_info = xtdata.get_instrument_detail(pos.stock_code)
                    if instrument_info:
                        stock_name = instrument_info.get("InstrumentName", "")
                
                # 3. 如果仍然没有获取到，使用代码作为名称
                if not stock_name:
                    stock_name = pos.stock_code
                
                current_price = tick_data.get("lastPrice", 0)
                
                # 计算盈亏
                if pos.avg_price > 0 and current_price > 0:
                    profit_amount = (current_price - pos.avg_price) * pos.volume
                    profit_percent = (current_price - pos.avg_price) / pos.avg_price * 100
                    
                    # 只有非货币基金才计入总盈亏
                    if not self.is_money_fund(pos.stock_code):
                        self.current_total_profit += profit_amount
                else:
                    profit_amount = 0
                    profit_percent = 0
                
                # 设置单元格数据（不包括勾选框、分时图按钮和做T勾选框）
                items = [
                    pos.stock_code,          # 证券代码 (列1)
                    stock_name,              # 证券名称 (列2)
                    str(pos.volume),         # 持仓数量 (列3)
                    str(pos.can_use_volume), # 可用数量 (列4)
                    f"{pos.avg_price:.3f}",  # 成本价 (列5)
                    f"{current_price:.3f}",  # 现价 (列6)
                    f"{profit_amount:,.2f}", # 盈亏 (列7)
                    f"{profit_percent:+.2f}%", # 盈亏率 (列8)
                    f"{pos.market_value:,.2f}" # 市值 (列9)
                ]
                
                # 设置文本单元格
                for col, item in enumerate(items, start=1):  # 从第1列开始，因为第0列是勾选框
                    table_item = QTableWidgetItem(str(item))
                    table_item.setTextAlignment(Qt.AlignCenter)
                    
                    # 设置盈亏和盈亏率的颜色
                    if col in [7, 8]:  # 盈亏金额和盈亏率列
                        if profit_amount > 0:
                            table_item.setForeground(QColor('red'))
                        elif profit_amount < 0:
                            table_item.setForeground(QColor('green'))
                
                    self.positions_table.setItem(row, col, table_item)
                
                # 添加分时图按钮到第10列
                time_series_btn = QPushButton("分时图")
                time_series_btn.setFixedWidth(65)
                time_series_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #17a2b8;
                        color: white;
                        border: none;
                        padding: 4px;
                        border-radius: 2px;
                        font-size: 12px;
                    }
                    QPushButton:hover {
                        background-color: #138496;
                    }
                """)
                time_series_btn.clicked.connect(lambda checked, code=pos.stock_code: self.show_time_series(code))
                self.positions_table.setCellWidget(row, 10, time_series_btn)
                
                # 添加做T勾选框到第11列
                t_checkbox = QCheckBox()
                t_checkbox.setStyleSheet("""
                    QCheckBox {
                        margin-left: 20px;
                    }
                """)
                
                # 设置勾选状态为之前保存的状态
                if pos.stock_code in self.t_trading_states:
                    t_checkbox.setChecked(self.t_trading_states[pos.stock_code])
                
                t_checkbox.stateChanged.connect(
                    lambda state, code=pos.stock_code: self.on_t_checkbox_changed(code, state)
                )
                
                # 创建一个容器widget来居中显示做T勾选框
                container = QWidget()
                layout = QHBoxLayout(container)
                layout.addWidget(t_checkbox)
                layout.setAlignment(Qt.AlignCenter)
                layout.setContentsMargins(0, 0, 0, 0)
                self.positions_table.setCellWidget(row, 11, container)
                
        except Exception as e:
            self.info_append(f"刷新持仓信息失败: {str(e)}")

    def show_time_series(self, stock_code):
        """显示分时图"""
        try:
            # 获取当天的分时数据
            today = datetime.now().strftime('%Y%m%d')
            current_time = datetime.now().time()
            
            # 检查是否在交易时段
            if current_time < dt_time(9, 30):
                self.info_append(f"开市前无法显示分时图")
                return
                
            # 检查是否是交易日
            if not self.is_trading_day(today):
                self.info_append(f"非交易日无法显示分时图")
                return
            
            # 下载当日1分钟K线数据
            my_download([stock_code], '1m', today, today)
            time.sleep(1)  # 等待数据就绪
            
            # 获取1分钟K线数据 - 添加high和low字段
            minute_data = xtdata.get_market_data(
                field_list=['time', 'open', 'close', 'high', 'low', 'volume', 'amount'],
                stock_list=[stock_code],
                period='1m',
                start_time=today,
                end_time=today,
                count=-1
            )
            
            if not minute_data or len(minute_data) == 0:
                self.info_append(f"当前无交易数据")
                return
            
            # 创建DataFrame并转换时间为datetime格式
            df = pd.DataFrame({
                'time': pd.to_datetime(minute_data['time'].loc[stock_code], unit='ms') + pd.Timedelta(hours=8),
                'open': minute_data['open'].loc[stock_code],
                'close': minute_data['close'].loc[stock_code],
                'high': minute_data['high'].loc[stock_code],
                'low': minute_data['low'].loc[stock_code],
                'volume': minute_data['volume'].loc[stock_code],
                'amount': minute_data['amount'].loc[stock_code]
            })
            
            # 删除无效数据
            df = df.dropna()
            
            # 确保时间在当天
            today_date = datetime.now().date()
            df['time'] = df['time'].apply(lambda x: datetime.combine(today_date, x.time()))
            
            # 分别获取上午和下午的数据
            morning_mask = df['time'].apply(lambda x: dt_time(9, 15) <= x.time() <= dt_time(11, 30))
            afternoon_mask = df['time'].apply(lambda x: dt_time(13, 0) <= x.time() <= dt_time(15, 0))
            
            morning_data = df[morning_mask].copy()
            afternoon_data = df[afternoon_mask].copy()
            
            if not afternoon_data.empty:
                # 调整下午时间
                afternoon_data['time'] = afternoon_data['time'].apply(
                    lambda x: datetime.combine(
                        x.date(),
                        dt_time(11, 30)
                    ) + timedelta(minutes=(x.hour - 13) * 60 + x.minute + 90)
                )
            
            # 合并数据
            df = pd.concat([morning_data, afternoon_data]).sort_values('time')
            
            if len(df) == 0:
                self.info_append(f"{stock_code}当前无交易时段数据")
                return
            
            # 调用本地的plot_time_series函数
            plot_time_series(df, f"{stock_code} 分时图")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"显示分时图失败: {str(e)}")

    def is_trading_day(self, date_str):
        """判断是否为交易日"""
        try:
            # 转换日期字符串为datetime对象
            date = datetime.strptime(date_str, '%Y%m%d')
            
            # 如果是周末，返回False
            if date.weekday() >= 5:
                return False
                
            # 这里可以添加节假日的判断
            # 为简单起见，目前只判断周末
            return True
            
        except Exception as e:
            self.info_append(f"判断交易日出错: {str(e)}")
            return False

    def show_t_trade_dialog(self, stock_code):
        try:
            # 获取持仓信息
            position = next((pos for pos in self.xt_trader.query_stock_positions(self.acc) 
                            if pos.stock_code == stock_code), None)
            if not position:
                raise ValueError("未找到持仓信息")
            
            dialog = QDialog(self)
            dialog.setWindowTitle(f"做T交易 - {stock_code}")
            dialog.resize(300, 200)
            
            layout = QVBoxLayout()
            
            # 添加价格类型选择
            price_type_combo = QComboBox()
            price_type_combo.addItems(['最新价委托', '限价委托', '最优五档即时成交'])
            layout.addWidget(QLabel("报价方式:"))
            layout.addWidget(price_type_combo)
            
            # 添加价格输入
            price_edit = QLineEdit()
            price_label = QLabel("委托价格:")
            layout.addWidget(price_label)
            layout.addWidget(price_edit)
            
            # 添加数量输入
            volume_edit = QLineEdit()
            layout.addWidget(QLabel("委托数量:"))
            layout.addWidget(volume_edit)
            
            # 添加按钮
            button_layout = QHBoxLayout()
            buy_btn = QPushButton("买入")
            sell_btn = QPushButton("卖出")
            button_layout.addWidget(buy_btn)
            button_layout.addWidget(sell_btn)
            layout.addLayout(button_layout)
            
            dialog.setLayout(layout)
            
            # 处理价格类型变化
            def on_price_type_changed(index):
                price_type = price_type_combo.currentText()
                enable_price = (price_type == '限价委托')
                price_edit.setEnabled(enable_price)
                price_label.setEnabled(enable_price)
                if not enable_price:
                    price_edit.clear()
            
            # 连接价格类型变化信号
            price_type_combo.currentIndexChanged.connect(on_price_type_changed)
            # 初始化价格输入框状态
            on_price_type_changed(0)
            
            # 处理买入事件
            def on_buy():
                try:
                    volume = int(volume_edit.text())
                    if volume <= 0 or volume % 100 != 0:
                        raise ValueError("委托数量必须为100的整数倍")
                    
                    price_type = price_type_combo.currentText()
                    price = 0  # 默认价格为0
                    
                    if price_type == '限价委托':
                        # 只在限价委托时验证价格
                        price = float(price_edit.text() or '0')
                        if price <= 0:
                            raise ValueError("请输入正确的委托价格")
                        price_type_value = xtconstant.FIX_PRICE
                    elif price_type == '最优五档即时成交':
                        # 根据股票代码判断市场
                        if stock_code.startswith(('6', '5')):  # 上交所
                            price_type_value = xtconstant.MARKET_SH_CONVERT_5_CANCEL
                        else:  # 深交所
                            price_type_value = xtconstant.MARKET_SZ_CONVERT_5_CANCEL
                    else:  # 最新价委托
                        price_type_value = xtconstant.MARKET_PEER_PRICE_FIRST
                    
                    order_id = self.xt_trader.order_stock(
                        self.acc,
                        stock_code,
                        xtconstant.STOCK_BUY,
                        volume,
                        price_type_value,
                        price,
                        "strategy",
                        "remark"
                    )
                    
                    if order_id > 0:
                        price_display = "最优价" if price_type == '最优五档即时成交' else (
                            "最新价" if price_type == '最新价委托' else f"{price:.3f}"
                        )
                        self.info_append(f"买入委托已提交 - 股票:{stock_code} 数量:{volume} 价格:{price_display}")
                        dialog.accept()
                    else:
                        raise Exception("委托提交失败")
                    
                except Exception as e:
                    QMessageBox.warning(dialog, "错误", str(e))
            
            # 处理卖出事件的代码类似，也需要相应修改...
            
            buy_btn.clicked.connect(on_buy)
            
            dialog.exec_()
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"显示做T对话框失败: {str(e)}")

    def update_profit_chart(self):
        """更新盈亏曲线（每分钟调用一次）"""
        try:
            # 确保有昨日总市值
            if self.yesterday_total_value is None:
                self.init_yesterday_total_value()
                if self.yesterday_total_value is None:
                    return
            
            # 获取当前资产信息
            asset = self.xt_trader.query_stock_asset(self.acc)
            if not asset:
                return
                
            # 计算盈亏 = 当前总资产 - 昨日总市值
            current_profit = asset.total_asset - self.yesterday_total_value
            
            current_time = datetime.now()
            current_time_str = current_time.strftime('%H:%M')
            
            # 将盈亏转换为百元单位
            profit_in_hundred = current_profit / 100
            
            # 只在交易时段更新
            if "09:30" <= current_time_str <= "11:30" or "13:00" <= current_time_str <= "15:00":
                # 存储数据点
                if not hasattr(self, 'x_data'):
                    self.x_data = []
                    self.y_data = []
                
                timestamp = current_time.timestamp()
                
                # 调整下午时段的时间戳
                if "13:00" <= current_time_str <= "15:00":
                    morning_end = datetime.combine(current_time.date(), dt_time(11, 30))
                    afternoon_start = datetime.combine(current_time.date(), dt_time(13, 0))
                    time_diff = afternoon_start.timestamp() - morning_end.timestamp()
                    timestamp -= time_diff
                
                self.x_data.append(timestamp)
                self.y_data.append(profit_in_hundred)
                
                # 更新原始曲线
                self.profit_curve.setData(self.x_data, self.y_data)
                
                # 计算和更新10分钟均线
                ma10_data = []
                ma10_x = []
                if len(self.y_data) >= 10:
                    for i in range(9, len(self.y_data)):
                        ma10 = sum(self.y_data[i-9:i+1]) / 10
                        ma10_data.append(ma10)
                        ma10_x.append(self.x_data[i])
                    self.ma10_curve.setData(ma10_x, ma10_data)
                else:
                    self.ma10_curve.setData([], [])
                
                # 设置曲线颜色
                self.profit_curve.setPen(
                    pg.mkPen('r', width=2) if current_profit > 0 else pg.mkPen('g', width=2)
                )
                
                # 自动调整Y轴范围
                if len(self.y_data) > 0:
                    all_y_values = self.y_data.copy()
                    if ma10_data:
                        all_y_values.extend(ma10_data)
                    y_min = min(all_y_values)
                    y_max = max(all_y_values)
                    padding = (y_max - y_min) * 0.1 if y_max != y_min else abs(y_min) * 0.1 + 1
                    self.profit_plot.setYRange(y_min - padding, y_max + padding)
                
                # 在更新完曲线后检查做T信号
                self.check_t_signals()
            
        except Exception as e:
            self.info_append(f"更新盈亏曲线失败: {str(e)}")

    def clear_all_positions(self):
        """一键清仓功能"""
        try:
            # 获取所有选中的可卖出的持仓
            sellable_positions = []
            for row in range(self.positions_table.rowCount()):
                # 获取勾选框状态
                checkbox_widget = self.positions_table.cellWidget(row, 0)
                if not checkbox_widget or not isinstance(checkbox_widget.layout().itemAt(0).widget(), QCheckBox):
                    continue
                
                checkbox = checkbox_widget.layout().itemAt(0).widget()
                if not checkbox.isChecked():
                    continue
                
                stock_code = self.positions_table.item(row, 1).text()
                # 跳过货币ETF
                if stock_code == "511800.SH":
                    continue
                
                available_volume = int(self.positions_table.item(row, 4).text())
                stock_name = self.positions_table.item(row, 2).text()
                
                if available_volume > 0:
                    # 获取最新价格
                    tick = xtdata.get_full_tick([stock_code])
                    if not tick or stock_code not in tick:
                        continue
                        
                    current_price = tick[stock_code]["lastPrice"]
                    
                    sellable_positions.append({
                        'code': stock_code,
                        'name': stock_name,
                        'volume': available_volume,
                        'price': current_price  # 保存当前价格用于显示
                    })
            
            if not sellable_positions:
                QMessageBox.information(self, "提示", "没有选中的可卖出持仓")
                return
            
            # 构建确认消息
            confirm_msg = "确认以对手方最优价格卖出以下持仓？\n\n"
            for pos in sellable_positions:
                confirm_msg += f"{pos['code']} {pos['name']}: {pos['volume']}股 @ 对手方最优价\n"
                confirm_msg += f"  (当前价: {pos['price']})\n"
            
            reply = QMessageBox.question(
                self, 
                "确认清仓", 
                confirm_msg,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                success_count = 0
                fail_count = 0
                
                for pos in sellable_positions:
                    try:
                        order_id = self.xt_trader.order_stock(
                            self.acc,
                            pos['code'],
                            xtconstant.STOCK_SELL,
                            pos['volume'],
                            xtconstant.MARKET_PEER_PRICE_FIRST,  # 使用对手方最优价格
                            0,  # 市价委托时价格填0
                            "strategy",
                            "remark"
                        )
                        
                        if order_id > 0:
                            success_count += 1
                            self.info_append(
                                f"卖出委托已提交 - 股票:{pos['code']} "
                                f"数量:{pos['volume']} 价格:对手方最优价"
                            )
                        else:
                            fail_count += 1
                            self.info_append(f"卖出委托失败 - 股票:{pos['code']}")
                    
                    except Exception as e:
                        fail_count += 1
                        self.info_append(f"卖出委托异常 - 股票:{pos['code']}, 错误:{str(e)}")
                
                # 显示执行结果
                result_msg = f"清仓执行完成\n成功:{success_count}笔\n失败:{fail_count}笔"
                if fail_count > 0:
                    QMessageBox.warning(self, "清仓结果", result_msg)
                else:
                    QMessageBox.information(self, "清仓结果", result_msg)
        
        except Exception as e:
            QMessageBox.warning(self, "错误", f"清仓操作失败: {str(e)}")

    def reset_profit_chart(self):
        """重置盈亏曲线"""
        try:
            # 重新初始化昨日总市值
            self.init_yesterday_total_value()
            
            # 清空数据
            if hasattr(self, 'x_data'):
                self.x_data = []
            if hasattr(self, 'y_data'):
                self.y_data = []
            
            # 清空所有曲线显示
            self.profit_curve.setData([], [])
            self.ma10_curve.setData([], [])
            
            # 重置Y轴范围
            self.profit_plot.setYRange(-1, 1)
            
            # 重置X轴范围
            today = datetime.now().date()
            morning_start = datetime.combine(today, dt_time(9, 30))
            x_min = morning_start.timestamp()
            x_max = x_min + 240 * 60  # 240分钟后
            self.profit_plot.setXRange(x_min, x_max)
            
            self.info_append("盈亏曲线已重置")
            
        except Exception as e:
            self.info_append(f"重置盈亏曲线失败: {str(e)}")

    def on_auto_t_changed(self, state):
        """处理做T勾选框状态变化"""
        try:
            # 获取所有开启做T的股票
            active_t_stocks = [code for code, enabled in self.t_trading_states.items() if enabled]
            
            if state == Qt.Checked:
                self.info_append("已开启做T功能")
                # 初始化所有股票的做T相关数据
                ticks = xtdata.get_full_tick(active_t_stocks)
                for stock_code in active_t_stocks:
                    if stock_code in ticks:
                        # 只更新最新价格和交叉价格
                        self.last_cross_prices[stock_code] = ticks[stock_code]["lastPrice"]
                        
                        # 显示基准持仓量信息
                        base_volume = self.base_positions.get(stock_code, 0)
                        self.info_append(f"初始化{stock_code}做T数据 - 基准持仓量:{base_volume}")
            else:
                self.info_append("已关闭做T功能")
                # 清除所有股票的做T相关数据，但保留基准持仓量
                for stock_code in active_t_stocks:
                    self.last_cross_prices.pop(stock_code, None)
                
        except Exception as e:
            self.info_append(f"处理做T状态改变失败: {str(e)}")

    def check_t_signals(self):
        """检查做T信号"""
        try:
            # 如果没有开启做T的股票，直接返回
            if not self.t_trading_states or not any(self.t_trading_states.values()):
                return
                
            # 检查盈亏曲线信号 - 添加对auto_t_checkbox状态的检查
            if self.auto_t_checkbox.isChecked() and len(self.y_data) >= 10:
                # 获取最新的盈亏值和10分钟均线值
                current_value = self.y_data[-1]
                ma10_value = sum(self.y_data[-10:]) / 10
                
                # 判断金叉死叉
                if len(self.y_data) >= 11:
                    prev_value = self.y_data[-2]
                    prev_ma10 = sum(self.y_data[-11:-1]) / 10
                    
                    # 金叉：当前值在均线上方且前一个值在均线下方
                    is_golden_cross = current_value > ma10_value and prev_value <= prev_ma10
                    # 死叉：当前值在均线下方且前一个值在均线上方
                    is_death_cross = current_value < ma10_value and prev_value >= prev_ma10
                    
                    if is_golden_cross or is_death_cross:
                        # 获取所有开启做T的股票
                        active_t_stocks = [code for code, enabled in self.t_trading_states.items() if enabled]
                        if active_t_stocks:
                            self.handle_cross("golden" if is_golden_cross else "death", active_t_stocks)
        
            # 检查个股分时线信号
            active_t_stocks = [code for code, enabled in self.t_trading_states.items() if enabled]
            if not active_t_stocks:
                return
                
            # 获取当前时间
            current_time = datetime.now()
            today = current_time.strftime('%Y%m%d')
            
            # 对每只开启做T的股票进行分析
            for stock_code in active_t_stocks:
                try:
                    # 先获取最新tick数据
                    tick = xtdata.get_full_tick([stock_code])
                    if not tick or stock_code not in tick:
                        self.info_append(f"{stock_code} - 未获取到最新行情数据")
                        continue
                        
                    current_price = tick[stock_code]["lastPrice"]
                    self.info_append(f"\n开始检查 {stock_code} 的做T信号 - {current_time.strftime('%H:%M:%S')}")
                    self.info_append(f"最新价: {current_price:.3f}")

                    # 获取1分钟K线数据用于计算均线
                    minute_data = xtdata.get_market_data(
                        field_list=['time', 'close', 'volume'],
                        stock_list=[stock_code],
                        period='1m',
                        start_time=today,
                        end_time=today,
                        count=-1
                    )
                    
                    if not minute_data or len(minute_data) == 0:
                        self.info_append(f"{stock_code} - 未获取到分钟K线数据")
                        continue
                    
                    # 转换为DataFrame
                    df = pd.DataFrame({
                        'time': pd.to_datetime(minute_data['time'].loc[stock_code], unit='ms'),
                        'close': minute_data['close'].loc[stock_code],
                        'volume': minute_data['volume'].loc[stock_code]
                    })
                    
                    # 过滤掉无效数据
                    df = df[df['volume'] > 0].copy()
                    
                    if len(df) < 20:
                        self.info_append(f"{stock_code} - 有效数据点数量{len(df)}小于20，跳过")
                        continue
                    
                    # 计算20分钟均线
                    df['ma20'] = df['close'].rolling(window=20).mean()
                    
                    # 获取最新和前一个价格及均线值
                    current_ma = df['ma20'].iloc[-1]
                    prev_ma = df['ma20'].iloc[-2]
                    prev_price = df['close'].iloc[-2]  # 使用前一分钟的收盘价
                    
                    self.info_append(f"20分钟均线: {current_ma:.3f}")
                    
                    # 保存前一次的状态
                    prev_above_ma = getattr(self, f'prev_above_ma_{stock_code}', None)
                    current_above_ma = current_price > current_ma

                    # 判断交叉
                    is_golden_cross = False
                    is_death_cross = False
                    
                    if prev_above_ma is not None:  # 如果有前一次状态
                        is_golden_cross = current_above_ma and not prev_above_ma  # 由下穿上
                        is_death_cross = not current_above_ma and prev_above_ma   # 由上穿下
                    
                    # 更新状态
                    setattr(self, f'prev_above_ma_{stock_code}', current_above_ma)
                    
                    if is_golden_cross or is_death_cross:
                        cross_type = "golden" if is_golden_cross else "death"
                        self.info_append(f"检测到{cross_type}交叉")
                        
                        # 检查是否与上次信号间隔足够
                        last_cross_time = getattr(self, f'last_cross_time_{stock_code}', None)
                        
                        if last_cross_time:
                            time_diff = (current_time - last_cross_time).total_seconds()
                            self.info_append(f"{stock_code} - 距离上次信号时间: {time_diff:.0f}秒")
                        
                        if (last_cross_time is None or 
                            (current_time - last_cross_time).total_seconds() > 300):  # 5分钟间隔
                            
                            self.info_append(f"{stock_code} - 时间间隔符合要求，处理交叉信号")
                            # 处理交叉信号
                            self.handle_stock_cross(cross_type, stock_code, current_price)
                            
                            # 更新最后交叉时间
                            setattr(self, f'last_cross_time_{stock_code}', current_time)
                        else:
                            self.info_append(f"{stock_code} - 距离上次信号时间太短，跳过")
                    else:
                        self.info_append(f"{stock_code} - 未检测到交叉信号")
                        
                except Exception as e:
                    self.info_append(f"检查{stock_code}分时线信号出错: {str(e)}")
            
        except Exception as e:
            self.info_append(f"检查做T信号出错: {str(e)}")

    def handle_cross(self, cross_type, stock_codes):
        """处理交叉信号"""
        try:
            # 获取所有开启做T的持仓
            positions = self.xt_trader.query_stock_positions(self.acc)
            if not positions:
                return

            # 只处理开启做T的股票
            positions = [pos for pos in positions if pos.stock_code in stock_codes]
            if not positions:
                return

            # 获取所有股票的最新价
            ticks = xtdata.get_full_tick([pos.stock_code for pos in positions])

            # 记录当前交叉信号
            current_cross_time = datetime.now()
            self.info_append(f"\n=== {current_cross_time.strftime('%H:%M:%S')} 盈亏曲线发生{cross_type}交叉 ===")
            
            for pos in positions:
                try:
                    if pos.stock_code not in ticks:
                        continue

                    current_price = ticks[pos.stock_code]["lastPrice"]
                    self.info_append(f"\n股票: {pos.stock_code}")
                    self.info_append(f"当前价: {current_price:.3f}")
                    
                    # 获取上次交叉价格
                    last_price = self.last_cross_prices.get(pos.stock_code)
                    
                    if last_price:
                        # 计算价格变动比例
                        price_diff_ratio = (current_price - last_price) / last_price
                        self.info_append(f"与上次价格相比变动: {price_diff_ratio*100:.2f}%")
                        
                        # 根据交叉类型和价格变动决定是否交易
                        if cross_type == "death" and price_diff_ratio >= 0.002:
                            # 死叉且价格上涨超过0.2%时卖出
                            if pos.can_use_volume > 0:
                                self.handle_sell(pos, current_price)
                        elif cross_type == "golden" and price_diff_ratio <= -0.002:
                            # 金叉且价格下跌超过0.2%时买入
                            self.handle_buy(pos, current_price)
                    
                    # 更新交叉信息
                    self.last_cross_prices[pos.stock_code] = current_price
                    self.last_cross_type[pos.stock_code] = cross_type

                except Exception as e:
                    self.info_append(f"处理{pos.stock_code}交叉信号出错: {str(e)}")

        except Exception as e:
            self.info_append(f"处理交叉信号出错: {str(e)}")

    def handle_buy(self, position, current_price):
        """处理买入操作"""
        try:
            # 再次获取最新价格
            tick = xtdata.get_full_tick([position.stock_code])
            if tick and position.stock_code in tick:
                latest_price = tick[position.stock_code]["lastPrice"]
                if abs(latest_price - current_price) > 0.01:  # 如果价格差异超过0.01
                    self.info_append(f"警告：价格已发生变化 {current_price:.3f} -> {latest_price:.3f}")
                    current_price = latest_price
            
            # 使用开市前的持仓量作为基数
            base_volume = self.base_positions.get(position.stock_code, position.volume)
            # 计算买入数量（基准持仓量的20%，向下取整到100的倍数）
            buy_volume = int(base_volume * 0.2 / 100) * 100
            if buy_volume >= 100:
                self.info_append(f"计划买入: {buy_volume}股 (基准持仓量{base_volume}的20%)")
                self.add_t_order(position.stock_code, "buy", buy_volume, current_price)
            else:
                self.info_append(f"买入数量{buy_volume}小于100股，不操作")

        except Exception as e:
            self.info_append(f"处理买入操作出错: {str(e)}")

    def handle_sell(self, position, current_price):
        """处理卖出操作"""
        try:
            # 使用开市前的持仓量作为基数
            base_volume = self.base_positions.get(position.stock_code, position.volume)
            # 计算卖出数量（基准持仓量的20%，向下取整到100的倍数）
            sell_volume = int(base_volume * 0.2 / 100) * 100
            # 确保不超过可用数量
            sell_volume = min(sell_volume, int(position.can_use_volume / 100) * 100)
            
            if sell_volume >= 100:
                self.info_append(f"计划卖出: {sell_volume}股 (基准持仓量{base_volume}的20%)")
                self.add_t_order(position.stock_code, "sell", sell_volume, current_price)
            else:
                self.info_append(f"卖出数量{sell_volume}小于100股，不操作")

        except Exception as e:
            self.info_append(f"处理卖出操作出错: {str(e)}")

    def add_t_order(self, stock_code, direction, volume, current_price):
        """添加做T订单"""
        try:
            # 检查是否直接委托
            if self.direct_order_checkbox.isChecked():
                # 直接执行委托
                order_id = self.xt_trader.order_stock(
                    self.acc,
                    stock_code,
                    xtconstant.STOCK_BUY if direction == "buy" else xtconstant.STOCK_SELL,
                    volume,
                    xtconstant.MARKET_PEER_PRICE_FIRST,  # 做T使用最新价委托
                    0,  # 最新价委托时价格为0
                    "strategy",
                    "remark"
                )
                
                if order_id > 0:
                    self.info_append(f"做T委托已执行 - 股票:{stock_code} 方向:{direction} 数量:{volume}")
                else:
                    raise Exception("委托提交失败")
                return
            
            # 以下是添加到待委托列表的原有逻辑
            row = self.pending_table.rowCount()
            self.pending_table.insertRow(row)
            
            # 添加数据
            items = [
                stock_code,
                "买入" if direction == "buy" else "卖出",
                "最新价委托",
                "最新价",
                str(volume),
                f"{current_price * volume:,.2f}",
            ]
            
            for col, item in enumerate(items):
                self.pending_table.setItem(row, col, QTableWidgetItem(str(item)))
            
            # 添加委托和删除按钮
            commit_btn = QPushButton("委托")
            commit_btn.setFixedWidth(65)
            commit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #007bff;
                    color: white;
                    border: none;
                    padding: 4px;
                    border-radius: 2px;
                    font-size: 12px;
                    height: 24px;
                }
                QPushButton:hover {
                    background-color: #0069d9;
                }
            """)
            commit_btn.clicked.connect(lambda checked, r=row: self.commit_pending_order(r))
            
            delete_btn = QPushButton("删除")
            delete_btn.setFixedWidth(65)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 4px;
                    border-radius: 2px;
                    font-size: 12px;
                    height: 24px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.delete_pending_order(r))
            
            self.pending_table.setCellWidget(row, 6, commit_btn)
            self.pending_table.setCellWidget(row, 7, delete_btn)
            
            # 保存到文件
            self.save_pending_orders()
            
            self.info_append(f"已添加做T委托 - 股票:{stock_code} 方向:{direction} 数量:{volume}")
            
        except Exception as e:
            self.info_append(f"添加做T委托失败: {str(e)}")

    def update_asset_info(self):
        """更新资产信息"""
        try:
            # 获取资产信息
            asset_info = self.xt_trader.query_stock_asset(self.acc)
            if not asset_info:
                return
                
            # 获取资产信息
            available_cash = asset_info.cash  # 可用资金
            frozen_cash = asset_info.frozen_cash  # 冻结资金
            market_value = asset_info.market_value  # 持仓市值
            total_asset = asset_info.total_asset  # 总资产
            
            # 更新显示
            self.cash_label.setText(f"可用资金: {available_cash:>13,.2f}")
            self.frozen_label.setText(f"冻结资金: {frozen_cash:>13,.2f}")
            self.market_value_label.setText(f"持仓市值: {market_value:>13,.2f}")
            self.total_label.setText(f"总资产: {total_asset:>13,.2f}")
            
        except Exception as e:
            print(f"更新资产信息出错: {str(e)}")
            # 出错时停止定时器
            self.asset_timer.stop()
    
    def closeEvent(self, event):
        """窗口关闭时停止定时器"""
        self.asset_timer.stop()
        super().closeEvent(event)

    # 添加事件过滤器方法
    def eventFilter(self, obj, event):
        """事件过滤器，用于处理滚动事件"""
        if event.type() == event.Wheel:
            # 获取相关的表格
            table = None
            if isinstance(obj, QWidget):
                if isinstance(obj.parent(), QTableWidget):
                    table = obj.parent()
                elif isinstance(obj, QScrollBar) and isinstance(obj.parent().parent(), QTableWidget):
                    table = obj.parent().parent()
            
            if table:
                # 获取当前滚动条位置
                scrollbar = table.verticalScrollBar()
                current_value = scrollbar.value()
                
                # 计算滚动步长（按行数计算）
                delta = -1 if event.angleDelta().y() > 0 else 1
                
                # 设置新的滚动位置
                new_value = current_value + delta
                scrollbar.setValue(new_value)
                
                # 阻止事件继续传播
                event.accept()
                return True
                
        return super().eventFilter(obj, event)

    def on_t_checkbox_changed(self, stock_code, state):
        """处理做T勾选框状态改变"""
        try:
            # 保存状态到字典中
            self.t_trading_states[stock_code] = (state == Qt.Checked)
            
            if state == Qt.Checked:
                self.info_append(f"{stock_code} 已开启做T功能")
                # 初始化该股票的做T相关数据
                tick = xtdata.get_full_tick([stock_code])
                if tick and stock_code in tick:
                    # 只更新最新价格和交叉价格，不更新基准持仓量
                    self.last_cross_prices[stock_code] = tick[stock_code]["lastPrice"]
                    
                    # 显示基准持仓量信息
                    base_volume = self.base_positions.get(stock_code, 0)
                    self.info_append(f"初始化{stock_code}做T数据 - 基准持仓量:{base_volume}")
            else:
                self.info_append(f"{stock_code} 已关闭做T功能")
                # 清除该股票的做T相关数据，但保留基准持仓量
                self.last_cross_prices.pop(stock_code, None)
                
        except Exception as e:
            self.info_append(f"处理做T状态改变失败: {str(e)}")

    def handle_stock_cross(self, cross_type, stock_code, current_price):
        """处理个股交叉信号"""
        try:
            self.info_append(f"\n=== {datetime.now().strftime('%H:%M:%S')} {stock_code}发生{cross_type}交叉 ===")
            self.info_append(f"当前价: {current_price:.3f}")
            
            # 获取持仓信息
            positions = self.xt_trader.query_stock_positions(self.acc)
            position = next((pos for pos in positions if pos.stock_code == stock_code), None)
            
            if position:
                # 获取上次交叉价格
                last_price = self.last_cross_prices.get(stock_code)
                
                if last_price:
                    # 非第一次交叉的处理逻辑 - 保持不变
                    price_diff_ratio = (current_price - last_price) / last_price
                    self.info_append(f"与上次价格相比变动: {price_diff_ratio*100:.2f}%")
                    
                    # 根据交叉类型和价格变动决定是否交易
                    if cross_type == "death" and price_diff_ratio >= 0.002:
                        # 死叉且价格上涨超过0.2%时卖出
                        if position.can_use_volume > 0:
                            self.handle_sell(position, current_price)
                    elif cross_type == "golden" and price_diff_ratio <= -0.002:
                        # 金叉且价格下跌超过0.2%时买入
                        self.handle_buy(position, current_price)
                else:
                    # 第一次交叉的处理逻辑 - 使用第一根一分钟K线开盘价
                    self.info_append("首次交叉信号")
                    
                    # 获取当天第一根一分钟K线的开盘价
                    today = datetime.now().strftime('%Y%m%d')
                    minute_data = xtdata.get_market_data(
                        field_list=['time', 'open'],
                        stock_list=[stock_code],
                        period='1m',
                        start_time=today,
                        end_time=today,
                        count=-1
                    )
                    
                    if minute_data and len(minute_data['open'].loc[stock_code]) > 0:
                        first_open = minute_data['open'].loc[stock_code][0]
                        self.info_append(f"第一根一分钟K线开盘价: {first_open:.3f}")
                        
                        price_diff_ratio = (current_price - first_open) / first_open
                        self.info_append(f"与开盘价相比变动: {price_diff_ratio*100:.2f}%")
                        
                        # 首次交叉使用与第一根K线开盘价的比较来决定是否交易
                        if cross_type == "death" and price_diff_ratio >= 0.002:
                            # 首次死叉且价格高于开盘价0.2%时卖出
                            if position.can_use_volume > 0:
                                self.handle_sell(position, current_price)
                        elif cross_type == "golden" and price_diff_ratio <= -0.002:
                            # 首次金叉且价格低于开盘价0.2%时买入
                            self.handle_buy(position, current_price)
                    else:
                        self.info_append("无法获取第一根一分钟K线开盘价，跳过首次交叉")
                        return
                
                # 更新交叉信息
                self.last_cross_prices[stock_code] = current_price
                self.last_cross_type[stock_code] = cross_type
                
                # 再次获取最新价格以确保价格最新
                tick = xtdata.get_full_tick([stock_code])
                if tick and stock_code in tick:
                    latest_price = tick[stock_code]["lastPrice"]
                    if abs(latest_price - current_price) > 0.01:  # 如果价格差异超过0.01
                        self.info_append(f"警告：价格已发生变化 {current_price:.3f} -> {latest_price:.3f}")
                        current_price = latest_price
                
        except Exception as e:
            self.info_append(f"处理{stock_code}交叉信号出错: {str(e)}")

    def clear_half_positions(self):
        """清半仓功能"""
        try:
            # 获取所有选中的可卖出的持仓
            sellable_positions = []
            for row in range(self.positions_table.rowCount()):
                # 获取勾选框状态
                checkbox_widget = self.positions_table.cellWidget(row, 0)
                if not checkbox_widget or not isinstance(checkbox_widget.layout().itemAt(0).widget(), QCheckBox):
                    continue
                
                checkbox = checkbox_widget.layout().itemAt(0).widget()
                if not checkbox.isChecked():
                    continue
                
                stock_code = self.positions_table.item(row, 1).text()
                # 跳过货币ETF
                if stock_code == "511800.SH":
                    continue
                
                available_volume = int(self.positions_table.item(row, 4).text())
                stock_name = self.positions_table.item(row, 2).text()
                market_value = float(self.positions_table.item(row, 9).text().replace(',', ''))  # 获取市值
                
                # 只处理市值大于10000元的持仓
                if market_value > 10000 and available_volume > 0:
                    # 计算卖出数量（向下取整到100的倍数）
                    sell_volume = (available_volume // 2 // 100) * 100
                    if sell_volume >= 100:  # 确保卖出数量至少100股
                        # 获取最新价格并计算委托价格
                        tick = xtdata.get_full_tick([stock_code])
                        if not tick or stock_code not in tick:
                            continue
                            
                        current_price = tick[stock_code]["lastPrice"]
                        # 根据证券类型设置委托价格
                        if self.is_etf(stock_code):
                            order_price = round(current_price - 0.003, 3)  # ETF减去0.003
                        else:
                            order_price = round(current_price - 0.10, 2)   # 股票减去0.10
                        
                        sellable_positions.append({
                            'code': stock_code,
                            'name': stock_name,
                            'volume': sell_volume,
                            'price': order_price,
                            'market_value': market_value
                        })
            
            if not sellable_positions:
                QMessageBox.information(self, "提示", "没有符合条件的可卖出持仓\n(市值需大于1万元)")
                return
            
            # 构建确认消息
            confirm_msg = "确认以下列价格卖出持仓？\n\n"
            for pos in sellable_positions:
                confirm_msg += (f"{pos['code']} {pos['name']}: {pos['volume']}股 @ {pos['price']}\n"
                              f"  (市值: {pos['market_value']:,.2f})\n")
            
            reply = QMessageBox.question(
                self, 
                "确认清半仓", 
                confirm_msg,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                success_count = 0
                fail_count = 0
                
                for pos in sellable_positions:
                    try:
                        order_id = self.xt_trader.order_stock(
                            self.acc,
                            pos['code'],
                            xtconstant.STOCK_SELL,
                            pos['volume'],
                            xtconstant.FIX_PRICE,
                            pos['price'],
                            "strategy",
                            "remark"
                        )
                        
                        if order_id > 0:
                            success_count += 1
                            self.info_append(
                                f"卖出委托已提交 - 股票:{pos['code']} "
                                f"数量:{pos['volume']} 价格:{pos['price']}"
                            )
                        else:
                            fail_count += 1
                            self.info_append(f"卖出委托失败 - 股票:{pos['code']}")
                    
                    except Exception as e:
                        fail_count += 1
                        self.info_append(f"卖出委托异常 - 股票:{pos['code']}, 错误:{str(e)}")
                
                # 显示执行结果
                result_msg = f"清半仓执行完成\n成功:{success_count}笔\n失败:{fail_count}笔"
                if fail_count > 0:
                    QMessageBox.warning(self, "清半仓结果", result_msg)
                else:
                    QMessageBox.information(self, "清半仓结果", result_msg)
        
        except Exception as e:
            QMessageBox.warning(self, "错误", f"清半仓操作失败: {str(e)}")

    def show_trade_record(self):
        """显示交易记录"""
        try:
            # 创建交易记录对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("今日交易记录")
            dialog.resize(800, 600)
            
            layout = QVBoxLayout(dialog)
            
            # 创建表格
            table = QTableWidget()
            table.setColumnCount(9)  # 增加一列用于显示浮动盈亏
            table.setHorizontalHeaderLabels([
                '证券代码', '证券名称', '方向', '成交时间', 
                '成交数量', '成交价格', '成交金额', '已实现盈亏', '浮动盈亏'
            ])
            
            # 设置表格样式
            table.setStyleSheet("""
                QTableWidget {
                    gridline-color: #e0e0e0;
                    background-color: white;
                    border: 1px solid #d0d0d0;
                }
                QTableWidget::item {
                    padding: 5px;
                }
                QHeaderView::section {
                    background-color: #f8f8f8;
                    padding: 5px;
                    border: none;
                    border-right: 1px solid #d0d0d0;
                    border-bottom: 1px solid #d0d0d0;
                    font-weight: bold;
                }
            """)
            
            # 设置列宽
            column_widths = [100, 120, 60, 140, 80, 80, 100, 100, 100]
            for col, width in enumerate(column_widths):
                table.setColumnWidth(col, width)
            
            # 获取今日交易记录
            trades = self.xt_trader.query_stock_trades(self.acc)
            
            # 获取当前持仓信息
            positions = self.xt_trader.query_stock_positions(self.acc)
            position_dict = {pos.stock_code: pos for pos in positions}
            
            # 获取所有交易的股票代码
            stock_codes = list(set(trade.stock_code for trade in trades))
            
            # 获取最新价
            latest_prices = {}
            ticks = xtdata.get_full_tick(stock_codes)
            for code in stock_codes:
                if code in ticks:
                    latest_prices[code] = ticks[code]["lastPrice"]
            
            # 统计变量
            total_buy_amount = 0
            total_sell_amount = 0
            total_realized_profit = 0
            total_floating_profit = 0
            
            # 添加交易记录到表格
            if trades:
                for trade in trades:
                    row = table.rowCount()
                    table.insertRow(row)
                    
                    # 获取证券名称
                    stock_name = ""
                    instrument_info = xtdata.get_instrument_detail(trade.stock_code)
                    if instrument_info:
                        stock_name = instrument_info.get("InstrumentName", "")
                    
                    # 计算成交金额
                    trade_amount = trade.traded_price * trade.traded_volume
                    
                    # 计算已实现盈亏
                    realized_profit = trade.profit if hasattr(trade, 'profit') else 0
                    
                    # 计算浮动盈亏
                    floating_profit = 0
                    if trade.stock_code in position_dict and trade.stock_code in latest_prices:
                        position = position_dict[trade.stock_code]
                        latest_price = latest_prices[trade.stock_code]
                        if trade.order_type == xtconstant.STOCK_BUY:
                            # 计算预计卖出金额（含手续费）
                            sell_amount = latest_price * trade.traded_volume
                            sell_fee = sell_amount * 0.0001  # 万分之一手续费
                            
                            # 计算买入成本（含手续费）
                            buy_amount = trade.traded_price * trade.traded_volume
                            buy_fee = buy_amount * 0.0001  # 万分之一手续费
                            
                            # 浮动盈亏 = 预计卖出金额 - 预计手续费 - 买入金额 - 买入手续费
                            floating_profit = sell_amount - sell_fee - buy_amount - buy_fee
                            
                            # 在日志中显示详细计算过程
                            self.info_append(
                                f"\n{trade.stock_code} 浮动盈亏计算:\n"
                                f"  买入金额: {buy_amount:,.2f}\n"
                                f"  买入手续费: {buy_fee:,.2f}\n"
                                f"  预计卖出金额: {sell_amount:,.2f}\n"
                                f"  预计卖出手续费: {sell_fee:,.2f}\n"
                                f"  浮动盈亏: {floating_profit:,.2f}"
                            )
                    
                    # 更新统计数据
                    if trade.order_type == xtconstant.STOCK_BUY:
                        total_buy_amount += trade_amount
                    else:
                        total_sell_amount += trade_amount
                        total_realized_profit += realized_profit
                    total_floating_profit += floating_profit
                    
                    # 设置单元格数据
                    items = [
                        trade.stock_code,
                        stock_name,
                        "买入" if trade.order_type == xtconstant.STOCK_BUY else "卖出",
                        datetime.fromtimestamp(trade.traded_time).strftime("%H:%M:%S"),
                        str(trade.traded_volume),
                        f"{trade.traded_price:.3f}",
                        f"{trade_amount:,.2f}",
                        f"{realized_profit:,.2f}" if realized_profit != 0 else "--",
                        f"{floating_profit:,.2f}" if trade.order_type == xtconstant.STOCK_BUY else "--"
                    ]
                    
                    for col, item in enumerate(items):
                        table_item = QTableWidgetItem(str(item))
                        table_item.setTextAlignment(Qt.AlignCenter)
                        
                        # 设置盈亏颜色
                        if col in [7, 8]:  # 已实现盈亏和浮动盈亏列
                            if item != "--":
                                value = float(item.replace(",", ""))
                                if value > 0:
                                    table_item.setForeground(QColor('red'))
                                elif value < 0:
                                    table_item.setForeground(QColor('green'))
                    
                        table_item.setFlags(table_item.flags() & ~Qt.ItemIsEditable)
                        table.setItem(row, col, table_item)
            
            # 更新统计信息
            summary = (f"今日交易统计：\n"
                      f"买入总额：{total_buy_amount:,.2f}\n"
                      f"卖出总额：{total_sell_amount:,.2f}\n"
                      f"已实现盈亏：{total_realized_profit:,.2f}\n"
                      f"浮动盈亏：{total_floating_profit:,.2f}\n"
                      f"合计盈亏：{(total_realized_profit + total_floating_profit):,.2f}")
            summary_text = QTextEdit()
            summary_text.setReadOnly(True)
            summary_text.setMaximumHeight(100)
            summary_text.setStyleSheet("""
                QTextEdit {
                    background-color: #f8f8f8;
                    border: 1px solid #d0d0d0;
                    padding: 5px;
                }
            """)
            summary_text.setText(summary)
            
            # 添加保存按钮
            button_layout = QHBoxLayout()
            save_btn = QPushButton("保存交易记录")
            save_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 4px;
                    font-size: 14px;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
            save_btn.clicked.connect(lambda: self.save_trade_record_to_excel(dialog=dialog))
            button_layout.addStretch()
            button_layout.addWidget(save_btn)
            
            # 修改布局顺序
            layout.addWidget(table)
            layout.addWidget(summary_text)
            layout.addLayout(button_layout)  # 添加按钮布局
            
            # 显示对话框
            dialog.exec_()
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"获取交易记录失败: {str(e)}")

    def save_trade_record_to_excel(self, dialog=None, summary_text=None):
        """保存交易记录到Excel"""
        try:
            # 获取今日交易记录
            trades = self.xt_trader.query_stock_trades(self.acc)
            
            # 获取当前日期和时间
            now = datetime.now()
            today = now.strftime('%Y%m%d')
            current_time = now.strftime('%H:%M:%S')
            excel_path = f'f:/work/trade_records.xlsx'
            
            # 准备数据
            trade_data = []
            for trade in trades:
                # 获取证券名称
                stock_name = ""
                instrument_info = xtdata.get_instrument_detail(trade.stock_code)
                if instrument_info:
                    stock_name = instrument_info.get("InstrumentName", "")
                
                # 计算成交金额
                trade_amount = trade.traded_price * trade.traded_volume
                
                # 准备数据行
                trade_row = {
                    '交易日期': today,
                    '交易时间': datetime.fromtimestamp(trade.traded_time).strftime("%H:%M:%S"),
                    '证券代码': trade.stock_code,
                    '证券名称': stock_name,
                    '方向': "买入" if trade.order_type == xtconstant.STOCK_BUY else "卖出",
                    '成交数量': trade.traded_volume,
                    '成交价格': trade.traded_price,
                    '成交金额': trade_amount,
                    '已实现盈亏': trade.profit if hasattr(trade, 'profit') else 0
                }
                trade_data.append(trade_row)
            
            # 读取现有数据（如果文件存在）
            existing_trades_df = None
            existing_assets_df = None
            existing_positions_df = None
            
            if os.path.exists(excel_path):
                try:
                    with pd.ExcelFile(excel_path) as xls:
                        if 'transactions' in xls.sheet_names:
                            existing_trades_df = pd.read_excel(xls, 'transactions')
                        if 'assets' in xls.sheet_names:
                            existing_assets_df = pd.read_excel(xls, 'assets')
                        if 'positions' in xls.sheet_names:
                            existing_positions_df = pd.read_excel(xls, 'positions')
                except Exception as e:
                    self.info_append(f"读取现有Excel文件出错: {str(e)}")
            
            # 创建新的DataFrame
            trades_df = pd.DataFrame(trade_data)
            if existing_trades_df is not None and not existing_trades_df.empty:
                trades_df = pd.concat([existing_trades_df, trades_df], ignore_index=True)
            
            # 准备资产数据
            asset = self.xt_trader.query_stock_asset(self.acc)
            if asset:
                # 处理极小值，将接近0的数值设为0
                frozen_cash = 0 if abs(asset.frozen_cash) < 0.01 else asset.frozen_cash
                
                asset_data = pd.DataFrame([{
                    '日期': today,
                    '时间': current_time,
                    '总资产': asset.total_asset,
                    '可用资金': asset.cash,
                    '冻结资金': frozen_cash,  # 使用处理后的值
                    '持仓市值': asset.market_value
                }])
                if existing_assets_df is not None and not existing_assets_df.empty:
                    asset_data = pd.concat([existing_assets_df, asset_data], ignore_index=True)
            
            # 准备持仓数据
            positions = self.xt_trader.query_stock_positions(self.acc)
            position_data = []
            if positions:
                for pos in positions:
                    stock_name = ""
                    instrument_info = xtdata.get_instrument_detail(pos.stock_code)
                    if instrument_info:
                        stock_name = instrument_info.get("InstrumentName", "")
                    
                    position_row = {
                        '日期': today,
                        '时间': current_time,
                        '证券代码': pos.stock_code,
                        '证券名称': stock_name,
                        '持仓数量': pos.volume,
                        '可用数量': pos.can_use_volume,
                        '冻结数量': pos.frozen_volume,
                        '成本价': pos.avg_price,
                        '市值': pos.market_value,
                        '浮动盈亏': pos.market_value - (pos.avg_price * pos.volume)
                    }
                    position_data.append(position_row)
            
            positions_df = pd.DataFrame(position_data)
            if existing_positions_df is not None and not existing_positions_df.empty:
                positions_df = pd.concat([existing_positions_df, positions_df], ignore_index=True)
            
            # 保存到Excel
            with pd.ExcelWriter(excel_path, engine='openpyxl', mode='w') as writer:
                trades_df.to_excel(writer, sheet_name='transactions', index=False)
                if asset:
                    asset_data.to_excel(writer, sheet_name='assets', index=False)
                if not positions_df.empty:
                    positions_df.to_excel(writer, sheet_name='positions', index=False)
            
            # 显示成功消息
            save_msg = f"\n交易记录已保存到: {excel_path}"
            if dialog:  # 如果是从对话框调用
                QMessageBox.information(dialog, "成功", f"交易记录已保存到:\n{excel_path}")
            if summary_text:
                summary_text.append(save_msg)
            self.info_append(save_msg)
                
        except Exception as e:
            error_msg = f"保存交易记录失败: {str(e)}"
            if dialog:  # 如果是从对话框调用
                QMessageBox.warning(dialog, "错误", error_msg)
            if summary_text:
                summary_text.append(f"\n{error_msg}")
            self.info_append(error_msg)

    def setup_auto_save_timer(self):
        """设置自动保存定时器"""
        self.save_timer = QTimer()
        self.save_timer.timeout.connect(self.auto_save_check)
        self.save_timer.start(60000)  # 每分钟检查一次

    def auto_save_check(self):
        """检查是否需要自动保存"""
        try:
            current_time = datetime.now()
            current_time_str = current_time.strftime('%H:%M')
            
            # 在15:15自动保存交易记录
            if current_time_str == '15:15':
                self.info_append("\n=== 开始自动保存交易记录 ===")
                self.save_trade_record_to_excel()
                
                # 停止定时器，今天不再检查
                self.save_timer.stop()
                self.info_append("今日自动保存完成，定时器已停止")
                
        except Exception as e:
            self.info_append(f"自动保存检查出错: {str(e)}")

    def setup_ipc_server(self):
        """设置进程间通信服务器"""
        try:
            self.ipc_server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.ipc_server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.ipc_server.bind(('localhost', 9876))  # 使用9876端口
            self.ipc_server.listen(5)
            
            # 启动监听线程
            self.ipc_thread = threading.Thread(target=self.listen_for_requests, daemon=True)
            self.ipc_thread.start()
            self.info_append("IPC服务器已启动，监听端口9876")
        except Exception as e:
            self.info_append(f"IPC服务器启动失败: {str(e)}")

    def listen_for_requests(self):
        """监听其他脚本的请求"""
        while True:
            try:
                client, addr = self.ipc_server.accept()
                #self.info_append(f"接收到来自 {addr} 的连接")
                
                # 启动新线程处理请求
                client_thread = threading.Thread(
                    target=self.handle_client_request, 
                    args=(client,),
                    daemon=True
                )
                client_thread.start()
            except Exception as e:
                self.info_append(f"IPC服务器错误: {str(e)}")
                break

    def handle_client_request(self, client_socket):
        """处理客户端请求"""
        try:
            # 接收数据
            data = b""
            while True:
                chunk = client_socket.recv(4096)
                if not chunk:
                    break
                data += chunk
            
            if not data:
                return
                
            # 解析请求
            request = json.loads(data.decode('utf-8'))
            request_type = request.get('type')
            
            #self.info_append(f"收到请求类型: {request_type}")
            
            # 处理不同类型的请求
            response = {'status': 'error', 'message': '未知请求类型'}
            
            if request_type == 'query_positions':
                # 查询持仓
                positions = self.xt_trader.query_stock_positions(self.acc)
                position_list = []
                for pos in positions:
                    position_list.append({
                        'stock_code': pos.stock_code,
                        'stock_name': getattr(pos, 'stock_name', ''),
                        'volume': pos.volume,
                        'can_use_volume': pos.can_use_volume,
                        'frozen_volume': pos.frozen_volume,
                        'avg_price': pos.avg_price,
                        'market_value': pos.market_value
                    })
                response = {'status': 'success', 'data': position_list}
                
            elif request_type == 'query_orders':
                # 查询委托
                orders = self.xt_trader.query_stock_orders(self.acc)
                order_list = []
                for order in orders:
                    order_list.append({
                        'order_id': order.order_id,
                        'stock_code': order.stock_code,
                        'order_type': order.order_type,
                        'order_status': order.order_status,
                        'order_volume': order.order_volume,
                        'traded_volume': order.traded_volume,
                        'price': order.price,  # 使用price而不是order_price
                        'order_time': order.order_time
                    })
                response = {'status': 'success', 'data': order_list}
                
            elif request_type == 'query_trades':
                # 查询成交
                trades = self.xt_trader.query_stock_trades(self.acc)
                trade_list = []
                for trade in trades:
                    trade_list.append({
                        'order_id': trade.order_id,
                        'stock_code': trade.stock_code,
                        'traded_id': trade.traded_id,
                        'traded_volume': trade.traded_volume,
                        'traded_price': trade.traded_price,
                        'traded_time': trade.traded_time,
                        'order_type': trade.order_type,  # 添加委托类型字段，用于区分买卖方向
                        'direction': "买入" if trade.order_type == xtconstant.STOCK_BUY else "卖出"  # 添加方向字段
                    })
                response = {'status': 'success', 'data': trade_list}
                
            elif request_type == 'query_asset':
                # 查询资产
                asset = self.xt_trader.query_stock_asset(self.acc)
                if asset:
                    asset_data = {
                        'total_asset': asset.total_asset,
                        'cash': asset.cash,
                        'market_value': asset.market_value,
                        'frozen_cash': asset.frozen_cash
                    }
                    response = {'status': 'success', 'data': asset_data}
                else:
                    response = {'status': 'error', 'message': '获取资产信息失败'}
                
            elif request_type == 'place_order':
                # 下单请求
                params = request.get('params', {})
                try:
                    # 打印IPC接口下单的详细参数
                    self.info_append("\n=== IPC接口下单请求 ===")
                    self.info_append(f"股票代码: {params['stock_code']}")
                    self.info_append(f"委托方向: {'买入' if params['direction'] == xtconstant.STOCK_BUY else '卖出'}")
                    self.info_append(f"委托数量: {params['volume']}")
                    self.info_append(f"委托价格: {params['price']}")
                    self.info_append(f"方向参数值: {params['direction']} (买入={xtconstant.STOCK_BUY}, 卖出={xtconstant.STOCK_SELL})")
                    
                    # 执行下单
                    order_id = self.xt_trader.order_stock(
                        self.acc,
                        params['stock_code'],
                        params['direction'],
                        params['volume'],
                        params['price_type'],
                        params['price'],
                        "",
                        "external"
                    )
                    
                    # 打印下单结果
                    if order_id > 0:
                        self.info_append(f"IPC接口下单成功，委托号: {order_id}")
                        response = {'status': 'success', 'data': {'order_id': order_id}}
                    else:
                        error_msg = f"IPC接口下单失败，返回ID: {order_id}"
                        self.info_append(error_msg)
                        response = {'status': 'error', 'message': error_msg}
                    
                except Exception as e:
                    error_msg = f"IPC接口下单异常: {str(e)}"
                    self.info_append(error_msg)
                    response = {'status': 'error', 'message': error_msg}
            
            elif request_type == 'cancel_order':
                # 撤单请求
                try:
                    params = request.get('params', {})
                    order_id = params.get('order_id')
                    
                    if order_id is None:
                        response = {'status': 'error', 'message': '缺少委托号'}
                    else:
                        try:
                            # 确保order_id是整数类型
                            order_id = int(order_id)
                            
                            # 获取委托信息
                            order = self.xt_trader.query_stock_order(self.acc, order_id)
                            if not order:
                                response = {'status': 'error', 'message': '未找到委托信息'}
                            else:
                                # 获取市场信息
                                market = 0  # 投研端市场参数可以填0
                                order_sysid = order.order_sysid  # 获取柜台合同编号
                                
                                # 使用异步撤单接口
                                result = self.xt_trader.cancel_order_stock_sysid_async(
                                    self.acc,
                                    market, 
                                    order_sysid
                                )
                                
                                if result > 0:
                                    self.info_append(f"撤单请求已提交，委托号: {order_id}")
                                    response = {
                                        'status': 'success',
                                        'message': '撤单请求已提交',
                                        'data': {
                                            'cancel_seq': result,
                                            'order_sysid': order_sysid
                                        }
                                    }
                                else:
                                    self.info_append(f"撤单请求失败，委托号: {order_id}")
                                    response = {'status': 'error', 'message': '撤单请求失败'}
                            
                        except Exception as e:
                            response = {'status': 'error', 'message': str(e)}
                        
                except Exception as e:
                    response = {'status': 'error', 'message': str(e)}
            
            # 发送响应
            client_socket.sendall(json.dumps(response).encode('utf-8'))
            
        except Exception as e:
            # 发送错误响应
            error_response = {'status': 'error', 'message': str(e)}
            try:
                client_socket.sendall(json.dumps(error_response).encode('utf-8'))
            except:
                pass
            self.info_append(f"处理客户端请求出错: {str(e)}")
        finally:
            client_socket.close()

    def closeEvent(self, event):
        """窗口关闭时停止定时器和服务器"""
        # 停止定时器
        if hasattr(self, 'asset_timer'):
            self.asset_timer.stop()
        
        # 关闭IPC服务器
        if hasattr(self, 'ipc_server'):
            try:
                self.ipc_server.close()
            except:
                pass
        
        # 调用父类方法
        super().closeEvent(event)

    def on_price_type_changed(self):
        """处理价格类型改变"""
        price_type = self.price_type_combo.currentText()
        # 限价委托时启用价格输入，其他情况禁用
        enable_price = (price_type == '限价委托')
        
        # 设置价格输入框和加减按钮的启用状态
        self.price_edit.setEnabled(enable_price)
        self.price_minus_btn.setEnabled(enable_price)
        self.price_plus_btn.setEnabled(enable_price)
        
        if not enable_price:
            # 非限价委托时清空价格输入框
            self.price_edit.clear()
        
        # 重新计算数量
    def init_yesterday_total_value(self):
        """从Excel文件中获取昨日总市值"""
        try:
            excel_path = 'f:/work/trade_records.xlsx'
            if not os.path.exists(excel_path):
                self.info_append(f"找不到交易记录文件: {excel_path}")
                return
                
            # 读取assets表中的数据
            df = pd.read_excel(excel_path, sheet_name='assets')
            if df.empty:
                self.info_append("资产记录为空")
                return
                
            # 获取最新一条记录的总资产作为昨日总市值
            latest_record = df.iloc[-1]
            self.yesterday_total_value = latest_record['总资产']
            self.info_append(f"从Excel获取昨日总市值: {self.yesterday_total_value:.2f}")
            
        except Exception as e:
            self.info_append(f"获取昨日总市值失败: {str(e)}")
            # 如果获取失败，使用当前总资产作为基准
            try:
                asset = self.xt_trader.query_stock_asset(self.acc)
                if asset:
                    self.yesterday_total_value = asset.total_asset
                    self.info_append(f"使用当前总资产作为基准: {asset.total_asset:.2f}")
            except Exception as e2:
                self.info_append(f"获取当前资产也失败: {str(e2)}")

        self.calculate_volume()

def read_stock_prices_from_excel(excel_file):
    """从Excel文件中读取股票买入价格"""
    try:
        df = pd.read_excel(excel_file)
        # 假设Excel文件中有'股票代码'和'买入价格'这两列
        stock_prices = dict(zip(df['股票代码'], df['买入价格']))
        return stock_prices
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return {}

def main():
    # 读取Excel中的股票买入价格
    stock_prices = read_stock_prices_from_excel('股票价格.xlsx')
    
    # 其他代码...
    pass

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TradingUI()
    window.show()
    sys.exit(app.exec_())
