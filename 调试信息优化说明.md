# 调试信息优化说明

## 问题描述

用户反馈：调试信息 `[159516.SZ] 使用服务器数据: 市值10080.00 - 成本9950.60 = 129.40` 一直重复显示，希望只显示一次。

## 问题原因

由于持仓列表会定期刷新（每15秒一次），每次刷新都会重新计算和显示持仓信息，导致相同的调试信息重复输出。

## 解决方案

### 1. 智能调试信息缓存

为每种类型的调试信息添加缓存机制，只在数据发生变化时才输出：

- **服务器数据计算**：缓存 `(市值, 成本, 盈亏)` 三元组
- **服务器盈亏数据**：缓存盈亏金额
- **本地计算数据**：缓存 `(盈亏, 数据源)` 二元组
- **数据不完整警告**：使用集合记录已警告的股票代码

### 2. 缓存实现机制

```python
# 服务器数据缓存
if not hasattr(self, '_last_server_data') or self._last_server_data.get(code) != (server_market_value, cost_value, total_profit):
    if not hasattr(self, '_last_server_data'):
        self._last_server_data = {}
    self._last_server_data[code] = (server_market_value, cost_value, total_profit)
    self.add_record(f"[{code}] 使用服务器数据: 市值{server_market_value:.2f} - 成本{cost_value:.2f} = {total_profit:.2f}")
```

### 3. 缓存管理

- **程序启动时**：自动清理所有调试缓存
- **手动清理**：新增"清理缓存"按钮
- **内存管理**：提供 `clear_debug_cache()` 方法

### 4. 新增功能

**清理缓存按钮**：
- 位置：控制面板第二行
- 功能：清理所有调试信息缓存
- 用途：重置调试状态，重新显示调试信息

## 优化效果

### 修复前：
```
09:33:28 [159516.SZ] 使用服务器数据: 市值10080.00 - 成本9950.60 = 129.40
09:33:43 [159516.SZ] 使用服务器数据: 市值10080.00 - 成本9950.60 = 129.40
09:33:58 [159516.SZ] 使用服务器数据: 市值10080.00 - 成本9950.60 = 129.40
...（每15秒重复一次）
```

### 修复后：
```
09:33:28 [159516.SZ] 使用服务器数据: 市值10080.00 - 成本9950.60 = 129.40
（只在数据变化时才显示新的信息）
```

## 使用说明

1. **自动优化**：系统会自动避免重复的调试信息
2. **数据变化时**：当盈亏金额发生变化时，会显示新的调试信息
3. **手动清理**：点击"清理缓存"按钮可以重置调试状态
4. **程序重启**：每次启动程序时会自动清理缓存

## 技术细节

- 使用字典缓存数值型数据的变化
- 使用集合缓存一次性警告信息
- 在程序初始化时自动清理缓存
- 提供手动清理接口避免内存积累

这样既保留了有用的调试信息，又避免了信息重复，提升了用户体验。
