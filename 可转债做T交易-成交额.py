import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from stock_utils import StockCodeParser, QMTConnector, DataDownloader
from stock_utils import show_time_series, check_cross_signals
import threading
import time
from datetime import datetime, timedelta
from xtquant import xtdata
import pandas as pd
import json

import socket
from xtquant.xtconstant import STOCK_BUY, FIX_PRICE, STOCK_SELL
import random
from signal_analyzer import SignalAnalyzer

# 导入工具类
from trading_utils import (
    DataManager, PriceCalculator, PositionStateManager,
    TradingSignalChecker, LogManager
)

class TimeSeriesViewer:
    def __init__(self):
        # 初始化工具类
        self.data_manager = DataManager()
        self.price_calculator = PriceCalculator()
        self.position_state_manager = PositionStateManager()
        self.signal_checker = TradingSignalChecker()
        self.log_manager = LogManager()

        # 设置日志文件 - 保持兼容性
        self.log_file = self.log_manager.log_file

        # 简化的状态管理 - 逐步迁移到统一状态管理器
        self.trade_instructions = {}
        self.profit_taking_monitor = {}

        # 用户手动清除的持仓记录，不会被重新加载
        self.manually_cleared_positions = set()
        
        # 新增：初始化持仓统计变量
        self.max_positions_today = 0
        self.max_profit_today = 0.0
        self.max_loss_today = 0.0
        
        # 新增：初始化当日最大浮盈和最大浮亏
        self.daily_max_float_profit = 0.0  # 当日最大浮盈（总持仓）
        self.daily_max_float_loss = 0.0    # 当日最大浮亏（总持仓）
        self.daily_total_profit = 0.0      # 当日总盈亏（包括已平仓）
        
        # 新增：初始化单只股票最大盈亏记录
        self.single_stock_max_profit = 0.0  # 当日单只股票最大浮盈
        self.single_stock_max_loss = 0.0    # 当日单只股票最大浮亏
        
        # 新增：记录当前日期和统计保存状态
        self.current_date = datetime.now().strftime('%Y%m%d')
        self.daily_stats_saved = False
        
        # 初始化其他属性
        self.root = tk.Tk()
        self.root.title("可转债做T交易系统 - 成交额策略")
        # 紧凑布局的窗口尺寸
        self.root.geometry("1250x800")
        self.root.minsize(1200, 600)  # 设置最小窗口尺寸，紧凑但确保按钮不会挤压
        
        # 定义文件路径 - 使用当日日期
        self.today = datetime.now().strftime('%Y%m%d')
        self.position_file = f"持仓记录_{self.today}.json"
        self.trade_history_file = f"交易历史_{self.today}.json"
        
        # 加载交易数据
        self.trading_data = self.load_trading_data()
        self.position_records = self.trading_data.get('positions', {})
        self.trade_records = self.trading_data.get('trades', [])
        self.cross_records = self.trading_data.get('cross_records', {})

        
        # 设置交易参数
        self.trading_enabled = False
        self.monitoring_enabled = False
        # 简化的策略参数 - 专注于成交额策略
        self.strategy_params = {
            'amount_threshold': 50000000,    # 成交额阈值（5000万）
            'volume_ratio_threshold': 3.0,   # 量比阈值
            'price_change_threshold': 5.0,   # 涨幅阈值(%)
            'volume_ratio_periods': 5,       # 量比计算周期
            'price_change_periods': 3,       # 涨幅计算周期
            'max_wait_periods': 5,           # 最大等待周期数
            'min_profit': 100,               # 最小盈利金额
            'max_loss': -100,                # 最大亏损金额
            'price_adjust': 0.20             # 价格调整幅度
        }
        
        # 初始化QMT连接和数据下载器
        self.qmt = QMTConnector()
        if not self.qmt.ensure_connection():
            messagebox.showerror("错误", "无法连接到QMT")
            raise Exception("无法连接到QMT")
        
        self.downloader = DataDownloader()
        self.parser = StockCodeParser()
        
        # 交易相关变量
        self.trading_enabled = False  # 是否启用自动交易
        self.is_trading = False  # 是否正在交易
        
        # 添加连接状态变量
        self.is_connected = False
        
        # 创建界面元素
        self.create_widgets()
        
        # 加载股票代码
        self.load_stock_codes()
        
        # 初始化监控状态
        self.monitoring = False
        self.monitor_thread = None
        self.order_monitor_thread = None  # 添加委托监控线程
        
        # 更新持仓列表和交易汇总
        self.update_position_list()
        self.update_trade_summary()
        
        # 添加首次启动标记
        self.first_run = True
        
        # 启动连接状态检查
        self.check_connection()

        # 成交额策略参数
        self.strategy_params = {
            'amount_threshold': 50000000,    # 成交额阈值（5000万）
            'volume_ratio_threshold': 4.0,   # 量比阈值
            'price_change_threshold': 4.0    # 3周期涨幅阈值（4%）
        }

        # 初始化默认周期
        self.current_period = "5m"
        
        # 添加策略状态变量
        self.first_cross = None  # 记录第一次交叉信号
        self.wait_periods = 0    # 等待第二个信号的周期数
        
        # 启动持仓价格定时刷新
        self.start_price_refresh()
        
        # 在程序启动时处理成交回报，确保持仓价格准确
        if self.trading_enabled and self.position_records:
            self.add_record("程序启动时处理成交回报，更新持仓价格...")
            self.process_all_trade_reports()
    
        # 加载状态信息
        self.load_state()

        # 初始化价格缓存和浮盈缓存
        self.current_price_data = {}
        self.current_floating_profit = 0.0
    
    def setup_logging(self):
        """设置日志文件"""
        try:
            # 创建log文件夹（如果不存在）
            log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'log')
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            # 设置日志文件路径
            current_date = datetime.now().strftime('%Y%m%d')
            self.log_file = os.path.join(log_dir, f'trading_log_{current_date}.txt')
            
            # 如果日志文件已存在，先清空
            with open(self.log_file, 'w', encoding='utf-8') as f:
                f.write(f"=== 交易日志 {current_date} ===\n")
                f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            print(f"日志文件已创建: {self.log_file}")
        except Exception as e:
            print(f"设置日志文件失败: {str(e)}")
            self.log_file = None

    def write_log(self, text):
        """写入日志文件"""
        try:
            # 添加时间戳
            timestamp = datetime.now().strftime('%H:%M:%S')
            log_text = f"[{timestamp}] {text}"
            
            # 输出到日志文件
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_text + '\n')
                
        except Exception as e:
            print(f"写入日志失败: {str(e)}")
    
    def load_trading_data(self):
        """从文件加载交易数据"""
        # 检查是否有当日交易记录
        today = datetime.now().strftime('%Y%m%d')
        self.position_file = f"持仓记录_{today}.json"
        self.trade_history_file = f"交易历史_{today}.json"

        # 初始化交易记录
        self.trade_records = []
        self.cross_records = {}
        
        # 初始化当日总盈亏
        self.daily_total_profit = 0.0
        
        # 使用工具类加载持仓记录
        self.position_records = self.data_manager.load_json(self.position_file)
        if self.position_records:
            print(f"已加载 {len(self.position_records)} 条当日持仓记录")
        else:
            print("未找到当日持仓记录，初始化空记录")
            self.position_records = {}
        
        # 使用工具类加载交易历史
        trade_data = self.data_manager.load_json(self.trade_history_file)
        if trade_data and isinstance(trade_data, list):
            self.trade_records = trade_data
            print(f"已加载 {len(self.trade_records)} 条当日交易记录")
            # 计算当日总盈亏
            self.daily_total_profit = sum(record['profit'] for record in self.trade_records)
            self.daily_total_profit = round(self.daily_total_profit, 2)
            print(f"当日总盈亏: {self.daily_total_profit:.2f}")
        else:
            self.trade_records = []
        


        # 加载用户手动清除的持仓列表
        cleared_file = f"手动清除持仓_{today}.json"
        cleared_data = self.data_manager.load_json(cleared_file)
        if cleared_data and isinstance(cleared_data, list):
            self.manually_cleared_positions = set(cleared_data)
            print(f"已加载 {len(self.manually_cleared_positions)} 条手动清除的持仓记录")
        else:
            self.manually_cleared_positions = set()

        # 保存空记录（如果文件不存在）
        self.save_trading_data()
        
        return {
            'positions': self.position_records,
            'trades': self.trade_records,
            'cross_records': self.cross_records
        }
    
    def save_trading_data(self):
        """保存交易数据到文件"""
        today = datetime.now().strftime('%Y%m%d')

        # 使用工具类保存持仓记录
        self.data_manager.save_json(self.position_records, f"持仓记录_{today}.json")

        # 使用工具类保存交易历史
        self.data_manager.save_json(self.trade_records, f"交易历史_{today}.json")

        # 保存用户手动清除的持仓列表
        self.data_manager.save_json(list(self.manually_cleared_positions), f"手动清除持仓_{today}.json")

        # 保存状态信息
        self.save_state()
    
    def create_widgets(self):
        """创建界面元素"""
        # 创建标签框架 - 设置固定高度，防止布局变化
        control_frame = ttk.LabelFrame(self.root, text="控制面板")
        control_frame.pack(fill="x", padx=5, pady=5)

        # 配置控制面板的列权重，固定布局防止按钮位置变化
        for i in range(9):  # 0-8列
            if i == 7:  # "强制检查买卖点"按钮列稍宽
                control_frame.columnconfigure(i, weight=0, minsize=85, uniform="button_col")
            else:
                control_frame.columnconfigure(i, weight=0, minsize=65, uniform="button_col")  # 缩小列宽，刚好容纳按钮文本

        # 配置行高，确保按钮行高固定
        for i in range(3):  # 0-2行
            control_frame.rowconfigure(i, weight=0, minsize=35)
        
        # 创建按钮 - 紧凑布局，按钮宽度刚好显示文本
        button_width = 6   # 缩短按钮宽度，刚好显示4个字符的文本
        button_padx = 2    # 减小水平间距
        button_pady = 2    # 减小垂直间距

        # 第一行按钮 - 添加sticky属性确保位置固定
        self.monitor_button = ttk.Button(control_frame, text="开始监控", command=self.toggle_monitoring, width=button_width)
        self.monitor_button.grid(row=0, column=0, padx=button_padx, pady=button_pady, sticky="ew")

        self.load_button = ttk.Button(control_frame, text="加载代码", command=self.load_stock_codes, width=button_width)
        self.load_button.grid(row=0, column=1, padx=button_padx, pady=button_pady, sticky="ew")

        self.import_button = ttk.Button(control_frame, text="导入数据", command=self.import_trading_data, width=button_width)
        self.import_button.grid(row=0, column=2, padx=button_padx, pady=button_pady, sticky="ew")

        self.export_button = ttk.Button(control_frame, text="导出数据", command=self.export_trading_data, width=button_width)
        self.export_button.grid(row=0, column=3, padx=button_padx, pady=button_pady, sticky="ew")

        self.test_button = ttk.Button(control_frame, text="测试功能", command=self.test_order_functions, width=button_width)
        self.test_button.grid(row=0, column=4, padx=button_padx, pady=button_pady, sticky="ew")

        self.stats_button = ttk.Button(control_frame, text="统计报表", command=self.show_trading_statistics, width=button_width)
        self.stats_button.grid(row=0, column=5, padx=button_padx, pady=button_pady, sticky="ew")

        self.cleanup_button = ttk.Button(control_frame, text="整理板块", command=self.cleanup_sector_files, width=button_width)
        self.cleanup_button.grid(row=0, column=6, padx=button_padx, pady=button_pady, sticky="ew")

        # 强制检查买卖点按钮 - 移到第一行，紧凑宽度
        self.force_check_button = ttk.Button(control_frame, text="强制检查买卖点", command=self.force_check_signals, width=8)
        self.force_check_button.grid(row=0, column=7, padx=button_padx, pady=button_pady, sticky="ew")

        # 创建交易开关（第二行第一列）
        self.trading_var = tk.BooleanVar(value=True)
        self.trading_check = ttk.Checkbutton(
            control_frame,
            text="启用交易",
            variable=self.trading_var,
            command=self.toggle_trading
        )
        self.trading_check.grid(row=1, column=0, padx=button_padx, pady=button_pady, sticky="w")

        # 连接状态标签 - 移到第8列
        self.connection_status_label = ttk.Label(control_frame, text="连接状态: 未连接", foreground="red")
        self.connection_status_label.grid(row=0, column=8, padx=button_padx, pady=button_pady, sticky="ew")

        # 实时提示标签（跨9列显示）
        self.realtime_hint_label = ttk.Label(control_frame, text="实时提示: 等待监控开始...", wraplength=500)
        self.realtime_hint_label.grid(row=2, column=0, columnspan=9, padx=button_padx, pady=button_pady, sticky="ew")

        # 新增：添加持仓统计标签（调整位置避免重叠）
        self.position_stats_label = ttk.Label(control_frame, text="当前持仓: 0 | 今日最大持仓: 0")
        self.position_stats_label.grid(row=1, column=2, columnspan=3, padx=2, pady=2, sticky="w")

        # 添加周期选择下拉框
        period_label = ttk.Label(control_frame, text="周期:")
        period_label.grid(row=2, column=7, padx=2, pady=2, sticky="e")

        self.period_var = tk.StringVar(value="5m")  # 默认5分钟
        self.period_combo = ttk.Combobox(
            control_frame,
            textvariable=self.period_var,
            values=["1m", "5m", "15m"],
            state="readonly",
            width=8
        )
        self.period_combo.grid(row=2, column=8, padx=2, pady=2, sticky="ew")
        self.period_combo.bind('<<ComboboxSelected>>', self.on_period_changed)

        # 防止控制面板高度变化影响按钮布局
        control_frame.pack_propagate(False)
        control_frame.configure(height=100)  # 固定控制面板高度

        # 创建左右分栏
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 右侧栏 - 交易记录（先创建右侧栏，确保record_listbox最先可用）
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill="both", expand=True)
        
        # 创建记录列表
        record_frame = ttk.LabelFrame(right_frame, text="交易记录")
        record_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 添加滚动
        record_scrollbar = tk.Scrollbar(record_frame)
        record_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.record_listbox = tk.Listbox(record_frame, yscrollcommand=record_scrollbar.set)
        self.record_listbox.pack(fill="both", expand=True)
        record_scrollbar.config(command=self.record_listbox.yview)
        
        # 左侧栏 - 股票代码和持仓
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill="both", expand=True)
        
        # 创建可转债和ETF代码的框架
        codes_frame = ttk.Frame(left_frame)
        codes_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 创建可转债代码列表框
        bond_frame = ttk.LabelFrame(codes_frame, text="可转债代码")
        bond_frame.pack(side=tk.LEFT, fill="both", expand=True)
        
        # 添加滚动条
        bond_scrollbar = tk.Scrollbar(bond_frame)
        bond_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.bond_listbox = tk.Listbox(bond_frame, yscrollcommand=bond_scrollbar.set)
        self.bond_listbox.pack(fill="both", expand=True)
        bond_scrollbar.config(command=self.bond_listbox.yview)
        
        # 创建ETF代码列表框
        etf_frame = ttk.LabelFrame(codes_frame, text="ETF代码")
        etf_frame.pack(side=tk.LEFT, fill="both", expand=True)
        
        # 添加滚动条
        etf_scrollbar = tk.Scrollbar(etf_frame)
        etf_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.etf_listbox = tk.Listbox(etf_frame, yscrollcommand=etf_scrollbar.set)
        self.etf_listbox.pack(fill="both", expand=True)
        etf_scrollbar.config(command=self.etf_listbox.yview)
        
        # 双击事件已移除
        
        # 创建持仓列表
        position_frame = ttk.LabelFrame(left_frame, text="当前持仓")
        position_frame.pack(fill="both", expand=False, padx=5, pady=5, ipady=30)
        
        # 添加滚动条
        position_scrollbar = tk.Scrollbar(position_frame)
        position_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.position_listbox = tk.Listbox(position_frame, yscrollcommand=position_scrollbar.set)
        self.position_listbox.pack(fill="both", expand=True)
        position_scrollbar.config(command=self.position_listbox.yview)
        
        # 绑定点击事件到持仓列表框
        self.position_listbox.bind('<Button-3>', self.show_position_menu)
        
        # 创建右键菜单
        self.position_menu = tk.Menu(self.root, tearoff=0)
        self.position_menu.add_command(label="清除持仓", command=self.clear_selected_position)
        
        # 创建交易汇总
        summary_frame = ttk.LabelFrame(left_frame, text="交易汇总")
        summary_frame.pack(fill="x", padx=5, pady=5)
        
        self.trade_summary_label = ttk.Label(summary_frame, text="", wraplength=400)
        self.trade_summary_label.pack(anchor="w", padx=5, pady=2)
    
    def load_stock_codes(self):
        """加载股票代码"""
        try:
            # 通过对话框选择可转债文件
            bond_file_path = filedialog.askopenfilename(
                title="选择可转债代码文件",
                filetypes=[("通达信板块文件", "*.blk"), ("所有文件", "*.*")],
                initialdir=os.getcwd()  # 使用当前目录
            )
            
            if not bond_file_path:  # 用户取消了选择
                return
            
            # 通过对话框选择ETF文件
            etf_file_path = filedialog.askopenfilename(
                title="选择ETF代码文件",
                filetypes=[("通达信板块文件", "*.blk"), ("所有文件", "*.*")],
                initialdir=os.getcwd()  # 使用当前目录
            )
            
            # 读取可转债代码
            self.bond_codes = self.parser.read_stock_codes(bond_file_path)
            
            # 读取ETF代码（如果选择了文件）
            if etf_file_path:
                self.etf_codes = self.parser.read_stock_codes(etf_file_path)
            else:
                self.etf_codes = []
                self.add_record("未选择ETF代码文件，将只加载可转债代码")
            
            # 合并所有代码用于监控
            self.stock_codes = self.bond_codes + self.etf_codes
            
            if not self.stock_codes:
                messagebox.showwarning("警告", "未找到有效的股票代码")
                return
            
            # 清空并重新填充可转债列表框
            self.bond_listbox.delete(0, tk.END)
            for code in self.bond_codes:
                self.bond_listbox.insert(tk.END, code)
            
            # 清空并重新填充ETF列表框
            self.etf_listbox.delete(0, tk.END)
            for code in self.etf_codes:
                self.etf_listbox.insert(tk.END, code)
            
            # 更新状态
            total_codes = len(self.bond_codes) + len(self.etf_codes)
            self.root.title(f"可转债做T交易系统 - 成交额策略 - {total_codes}只股票")
            
            # 记录加载成功信息
            self.add_record(f"成功加载 {len(self.bond_codes)} 只可转债代码")
            if self.etf_codes:
                self.add_record(f"成功加载 {len(self.etf_codes)} 只ETF代码")
            print(f"成功加载 {total_codes} 只股票代码")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载股票代码失败: {str(e)}")
            print(f"加载股票代码失败: {str(e)}")
    
    def filter_stocks(self, *args):
        """根据搜索框内容过滤股票列表"""
        search_text = self.search_var.get().upper()
        self.stock_listbox.delete(0, tk.END)
        
        for code in self.stock_codes:
            if search_text in code:
                # 不再为11开头的可转债添加标记
                self.stock_listbox.insert(tk.END, code)
    

    

    

    
    def is_valid_for_trading(self, code, timeout=3):
        """检查股票是否可交易且数据可用，使用线程超时机制"""
        result = [False]
        error_msg = [None]
        
        def check_trading():
            try:
                # 获取当天日期
                today = datetime.now().strftime('%Y%m%d')
                
                # 尝试获取分时数据，使用正确的参数格式
                self.downloader.my_download([code], '1m', today, today)
                
                # 等待数据就绪
                time.sleep(1)
                
                # 获取1分钟K线数据
                minute_data = xtdata.get_market_data(
                    field_list=['time', 'open', 'close', 'high', 'low', 'volume', 'amount'],
                    stock_list=[code],
                    period='1m',
                    start_time=today,
                    end_time=today,
                    count=-1
                )
                
                # 检查返回的数据是否有效
                if minute_data and code in minute_data['time'].index and len(minute_data['time'].loc[code]) > 0:
                    result[0] = True
                else:
                    result[0] = False
            except Exception as e:
                error_msg[0] = str(e)
                result[0] = False
                # 只打印错误，不使用messagebox
                print(f"检查股票{code}交易状态时出错: {str(e)}")
        
        # 创建并启动检查线程
        check_thread = threading.Thread(target=check_trading)
        check_thread.daemon = True
        check_thread.start()
        
        # 等待线程完成，带超时
        check_thread.join(timeout)
        
        # 如果线程仍在运行，说明超时了
        if check_thread.is_alive():
            print(f"检查股票{code}交易状态超时")
            return False
        
        return result[0]
    
    def toggle_monitoring(self):
        """切换监控状态"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_button.config(text="停止监控")
            self.update_realtime_hint("监控已启动...")
            
            # 创建并启动监控线程
            self.monitor_thread = threading.Thread(target=self.monitor_stocks)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
            
            # 创建并启动委托监控线程
            self.order_monitor_thread = threading.Thread(target=self.monitor_orders)
            self.order_monitor_thread.daemon = True
            self.order_monitor_thread.start()
            
            print("开始监控")
        else:
            # 停止监控
            self.monitoring = False
            self.monitor_button.config(text="开始监控")
            self.update_realtime_hint("监控已停止")
            
            # 等待线程结束
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(1.0)
            if self.order_monitor_thread and self.order_monitor_thread.is_alive():
                self.order_monitor_thread.join(1.0)
            
            print("停止监控")
    
    def toggle_trading(self):
        """切换自动交易状态"""
        self.trading_enabled = self.trading_var.get()
        status = "启用" if self.trading_enabled else "禁用"
        self.add_record(f"自动交易已{status}")

    def on_period_changed(self, event):
        """周期选择变化时的回调函数"""
        self.current_period = self.period_var.get()
        self.add_record(f"周期已切换为: {self.current_period}")

        # 如果正在监控，提示用户重新开始监控以应用新周期
        if self.monitoring:
            self.add_record("提示: 请重新开始监控以应用新的周期设置")

    def get_period_minutes(self):
        """根据选择的周期返回对应的分钟数"""
        period_map = {
            "1m": 1,
            "5m": 5,
            "15m": 15
        }
        # 安全获取周期，如果属性不存在则使用默认值
        current_period = getattr(self, 'current_period', '5m')
        return period_map.get(current_period, 5)  # 默认5分钟
    
    def export_trade_records(self):
        """导出交易记录到CSV文件"""
        if not self.trade_records:
            messagebox.showinfo("提示", "没有交易记录可导出")
            return
            
        try:
            # 获取当前日期时间作为文件名
            now = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"交易记录_{now}.csv"
            
            # 创建DataFrame并导出
            df = pd.DataFrame(self.trade_records)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            messagebox.showinfo("成功", f"交易记录已导出至 {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"导出交易记录失败: {str(e)}")
    
    def monitor_stocks(self):
        """监控股票"""
        try:
            self.monitoring = True
            self.add_record("开始监控股票...")
            
            # 移除强制清仓标记（已取消14:50强制清仓）
            # 添加上一次检查的分钟标记
            self.last_check_minute = -1
            # 添加收市提示标记
            self.market_closed_hint_shown = False
            
            # 启动时立即进行一次信号检查
            print("执行初始检查...")
            self.add_record("进行初始买卖点判定...")
            self.check_trading_signals()
            
            while self.monitoring:
                try:
                    # 获取当前时间
                    now = datetime.now()
                    current_time = now.time()
                    
                    # 检查是否在开市前
                    market_open_time = datetime.strptime('09:30:00', '%H:%M:%S').time()
                    pre_market_time = datetime.strptime('09:15:00', '%H:%M:%S').time()
                    
                    # 检查是否已收市（15:00后）
                    if current_time >= datetime.strptime('15:00:00', '%H:%M:%S').time():
                        if not self.market_closed_hint_shown:
                            self.add_record("交易日结束，市场已收市")
                            self.update_realtime_hint("市场已收市")
                            self.market_closed_hint_shown = True
                        time.sleep(60)  # 收市后每分钟检查一次
                        continue
                    
                    if current_time < market_open_time:
                        # 计算距离开市的时间
                        current_datetime = datetime.combine(datetime.now().date(), current_time)
                        market_open_datetime = datetime.combine(datetime.now().date(), market_open_time)
                        time_delta = market_open_datetime - current_datetime
                        
                        # 计算倒计时信息
                        minutes = time_delta.seconds // 60
                        seconds = time_delta.seconds % 60
                        countdown_msg = f"距离开市还有: {minutes}分{seconds}秒"
                        self.update_realtime_hint(countdown_msg)
                        
                        # 9:15前每分钟更新一次，之后每秒更新
                        if current_time < pre_market_time:
                            time.sleep(60)  # 每分钟更新
                        else:
                            time.sleep(1)   # 每秒更新
                        continue
                    
                    # 原有的监控逻辑
                    current_minute = now.minute
                    current_second = now.second
                    
                    # 移除14:50强制清仓逻辑，允许持仓过夜
                    
                    # 检查是否在交易时间
                    is_trading_time = ('09:30:00' <= current_time.strftime('%H:%M:%S') <= '11:30:00') or \
                                    ('13:00:00' <= current_time.strftime('%H:%M:%S') <= '15:00:00')
                    
                    # 移除14:30限制，全天可以买入
                    
                    if not is_trading_time:
                        # 如果在午休时间，显示下午开市倒计时
                        if ('11:30:00' <= current_time.strftime('%H:%M:%S') <= '13:00:00'):
                            afternoon_open_time = datetime.strptime('13:00:00', '%H:%M:%S').time()
                            current_datetime = datetime.combine(datetime.now().date(), current_time)
                            afternoon_open_datetime = datetime.combine(datetime.now().date(), afternoon_open_time)
                            time_delta = afternoon_open_datetime - current_datetime
                            
                            minutes = time_delta.seconds // 60
                            seconds = time_delta.seconds % 60
                            countdown_msg = f"距离下午开市还有: {minutes}分{seconds}秒"
                            self.update_realtime_hint(countdown_msg)
                            
                            # 12:45前每分钟更新一次，之后每秒更新
                            if current_time < datetime.strptime('12:45:00', '%H:%M:%S').time():
                                time.sleep(60)  # 每分钟更新
                            else:
                                time.sleep(1)   # 每秒更新
                            continue
                        
                        time.sleep(1)  # 非交易时间每秒检查一次
                        continue
                    
                    # 在每分钟的4分和9分进行检查（如9:34、9:39、10:04、10:09等）
                    should_check = False

                    # 检查是否在4分或9分，且在05秒时触发
                    if (current_second == 5 and
                        (current_minute % 10 == 4 or current_minute % 10 == 9) and
                        current_minute != self.last_check_minute):
                        should_check = True

                    if should_check:
                        print(f"执行检查 - 当前时间: {current_time.strftime('%H:%M:%S')}")  # 调试输出
                        self.add_record(f"进行买卖点判定... 时间: {current_time.strftime('%H:%M:%S')} (4分/9分检查)")
                        self.check_trading_signals()
                        # 更新上次检查时间
                        self.last_check_minute = current_minute
                    
                    time.sleep(0.2)  # 缩短检查间隔，提高精确度
                    
                except Exception as e:
                    self.add_record(f"监控股票时出错: {str(e)}")
                    time.sleep(10)
                
        except Exception as e:
            self.add_record(f"监控股票时出错: {str(e)}")
            time.sleep(10)

    def download_with_progress(self, stock_list, period, start_date='', end_date=''):
        """
        带详细进度显示的数据下载方法
        """
        try:
            import time
            from xtquant import xtdata

            total = len(stock_list)

            # 记录开始时间
            start_time = time.time()

            for i, code in enumerate(stock_list, 1):
                try:
                    # 显示当前进度
                    progress_msg = f"当前正在下载 5m {i}/{total} - {code}"
                    print(progress_msg)  # 控制台输出
                    #self.add_record(progress_msg)  # 界面显示

                    # 更新实时提示
                    self.root.after(0, lambda msg=progress_msg: self.update_realtime_hint(msg))

                    # 下载数据
                    xtdata.download_history_data(code, period, start_date, end_date)

                    # 短暂延迟，避免请求过快
                    time.sleep(0.02)

                except Exception as e:
                    error_msg = f"下载 {code} 数据失败: {str(e)}"
                    self.add_record(error_msg)
                    print(error_msg)

            # 下载完成
            total_time = time.time() - start_time
            completion_msg = f"数据下载完成! 共下载 {total} 只股票，耗时 {total_time:.1f} 秒"
            self.add_record(completion_msg)
            print(completion_msg)

        except Exception as e:
            error_msg = f"批量下载数据失败: {str(e)}"
            self.add_record(error_msg)
            print(error_msg)

    def check_trading_signals(self):
        try:
            # 获取当前时间
            now = datetime.now()
            today = now.strftime('%Y%m%d')
            current_time = now.time()

            # 判断是否在开盘前
            is_before_open = current_time < datetime.strptime('09:30:00', '%H:%M:%S').time()

            # 注释：现在始终使用最新K线，不再区分交易时间

            # 合并可转债和ETF代码作为监控列表
            monitor_codes = self.bond_codes + self.etf_codes
            total_codes = len(monitor_codes)
            self.add_record(f"开始检查 {total_codes} 只股票的买卖信号")

            # 获取最新交易日（如非交易日，获取最近交易日）
            try:
                # 获取交易日历，往前取10天确保有数据
                start_time = (now - timedelta(days=10)).strftime('%Y%m%d')
                end_time = today

                trading_days_sh = xtdata.get_trading_dates(
                    market='SH',
                    start_time=start_time,
                    end_time=end_time
                )
                trading_days_sz = xtdata.get_trading_dates(
                    market='SZ',
                    start_time=start_time,
                    end_time=end_time
                )

                # 合并两个市场的交易日历并排序
                trading_days = sorted(set(trading_days_sh + trading_days_sz))

                if trading_days:
                    # 根据开盘时间和交易日历确定使用的日期
                    latest_trading_date = None

                    # 检查今天是否是交易日
                    today_is_trading_day = False
                    for ts in trading_days:
                        day = datetime.fromtimestamp(ts/1000).strftime('%Y%m%d')
                        if day == today:
                            today_is_trading_day = True
                            break

                    if today_is_trading_day:
                        # 今天是交易日
                        if is_before_open:
                            # 开盘前，使用上一个交易日
                            #self.add_record(f"今天{today}是交易日，但当前时间{current_time.strftime('%H:%M:%S')}在开盘前，使用上一交易日")
                            for ts in reversed(trading_days):
                                day = datetime.fromtimestamp(ts/1000).strftime('%Y%m%d')
                                if day < today:
                                    latest_trading_date = day
                                    break
                        else:
                            # 开盘后，使用今天
                            #self.add_record(f"今天{today}是交易日，当前时间{current_time.strftime('%H:%M:%S')}在开盘后，使用今天")
                            latest_trading_date = today
                    else:
                        # 今天不是交易日，使用最近的交易日
                        self.add_record(f"今天{today}不是交易日，使用最近的交易日")
                        for ts in reversed(trading_days):
                            day = datetime.fromtimestamp(ts/1000).strftime('%Y%m%d')
                            if day < today:
                                latest_trading_date = day
                                break

                    if not latest_trading_date:
                        # 如果没找到，使用最后一个交易日
                        latest_trading_date = datetime.fromtimestamp(trading_days[-1]/1000).strftime('%Y%m%d')

                    # 获取上一交易日作为下载起始日期
                    previous_trading_date = None
                    for ts in reversed(trading_days):
                        day = datetime.fromtimestamp(ts/1000).strftime('%Y%m%d')
                        if day < latest_trading_date:
                            previous_trading_date = day
                            break

                    # 如果没找到上一交易日，使用当前交易日前一天
                    if not previous_trading_date:
                        prev_date = datetime.strptime(latest_trading_date, '%Y%m%d') - timedelta(days=1)
                        previous_trading_date = prev_date.strftime('%Y%m%d')

                else:
                    latest_trading_date = today
                    # 简单计算上一交易日
                    prev_date = datetime.strptime(today, '%Y%m%d') - timedelta(days=1)
                    previous_trading_date = prev_date.strftime('%Y%m%d')

            except Exception as e:
                self.add_record(f"获取交易日历失败: {str(e)}, 使用当前日期")
                latest_trading_date = today
                prev_date = datetime.strptime(today, '%Y%m%d') - timedelta(days=1)
                previous_trading_date = prev_date.strftime('%Y%m%d')

            # 下载从上一交易日到当前交易日的5分钟K线数据
            self.add_record(f"开始下载5分钟数据，从 {previous_trading_date} 到 {latest_trading_date}")
            self.add_record(f"共需下载 {total_codes} 只股票的数据")

            # 使用带进度显示的下载方法，从上一交易日开始下载
            self.download_with_progress(monitor_codes, '5m', previous_trading_date, latest_trading_date)
            
            # 记录成功获取数据的股票
            processed_codes = []
            failed_codes = []
            
            # 遍历所有监控的股票
            for i, code in enumerate(monitor_codes):
                try:
                    # 获取最新5分钟数据
                    minute_data = xtdata.get_market_data(
                        field_list=['time', 'open', 'close', 'high', 'low', 'volume', 'amount'],
                        stock_list=[code],
                        period='5m',  # 固定使用5分钟数据
                        start_time='',
                        end_time='',
                        count=10  # 获取最近10根K线
                    )
                    
                    # 检查数据是否有效
                    if not minute_data or 'time' not in minute_data or code not in minute_data['time'].index or len(minute_data['time'].loc[code]) == 0:
                        if i < 3:  # 只为前3只股票显示调试信息
                            self.add_record(f"[调试] {code} 数据获取失败")
                        failed_codes.append(code)
                        continue

                    # 创建DataFrame
                    df = pd.DataFrame({
                        'time': pd.to_datetime(minute_data['time'].loc[code], unit='ms').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai'),
                        'open': minute_data['open'].loc[code],
                        'close': minute_data['close'].loc[code],
                        'high': minute_data['high'].loc[code],
                        'low': minute_data['low'].loc[code],
                        'volume': minute_data['volume'].loc[code],
                        'amount': minute_data['amount'].loc[code]
                    })

                    # 检查是否有数据
                    if len(df) == 0:
                        if i < 3:  # 只为前3只股票显示调试信息
                            self.add_record(f"[调试] {code} 无数据")
                        failed_codes.append(code)
                        continue

                    # 获取最新的5分钟K线数据 - 始终使用最新K线
                    if len(df) < 1:
                        if i < 3:
                            self.add_record(f"[调试] {code} 数据不足，无K线数据")
                        failed_codes.append(code)
                        continue
                    latest_kline = df.iloc[-1]  # 始终使用最后一根K线（包括正在形成的K线）

                    # 获取最新价格和成交额
                    current_price = latest_kline['close']
                    current_amount = latest_kline['amount']

                    # 使用工具类检查综合买入信号
                    current_volume = latest_kline['volume']
                    has_buy_signal, signal_details = self.signal_checker.check_comprehensive_buy_signal(
                        current_amount, current_volume, current_price, df)

                    # 提取详细信息以保持兼容性
                    amount_condition = signal_details['amount_condition']
                    volume_ratio_condition = signal_details['volume_ratio_condition']
                    volume_ratio_value = signal_details['volume_ratio_value']
                    price_change_condition = signal_details['price_change_condition']
                    price_change_value = signal_details['price_change_value']
                    bullish_condition = signal_details['bullish_condition']
                    bullish_desc = signal_details['bullish_desc']

                    # 处理买入信号
                    if has_buy_signal:
                        # 检查是否已经持有该股票
                        if code in self.position_records:
                            self.add_record(f"❌ {code} 已有持仓，不能重复买入")
                        else:
                            signal_text = f"🚀 {code} 综合买入信号: 价格({current_price:.3f})"
                            detail_text = f"   ✅ 成交额: {current_amount/10000:.0f}万 > 5000万"
                            detail_text += f" | ✅ 量比: {volume_ratio_value:.2f} > 4.0"
                            detail_text += f" | ✅ 3周期涨幅: {price_change_value:.2f}% < 4%"
                            detail_text += f" | ✅ {bullish_desc}"

                            # 开盘前只记录信号，不执行买入
                            if is_before_open:
                                self.add_record(f"[开盘前] {signal_text} - 暂不执行买入")
                                self.add_record(f"[开盘前] {detail_text}")
                            else:
                                # 执行买入操作
                                self.execute_buy(code, current_price, datetime.now().strftime('%H:%M:%S'))
                            self.add_record(signal_text)
                            self.add_record(detail_text)
                            self.root.after(0, lambda t=signal_text: self.update_realtime_hint(t))
                    else:
                        # 优先显示成交额满足条件的股票判定结果（因为数量较少，更有价值）
                        if amount_condition:
                            reasons = []
                            status_parts = [f"成交额{current_amount/10000:.0f}万✅"]

                            if not volume_ratio_condition:
                                reasons.append(f"量比{volume_ratio_value:.2f}≤4.0")
                                status_parts.append(f"量比{volume_ratio_value:.2f}❌")
                            else:
                                status_parts.append(f"量比{volume_ratio_value:.2f}✅")

                            if not price_change_condition:
                                reasons.append(f"3周期涨幅{price_change_value:.2f}%≥4%")
                                status_parts.append(f"涨幅{price_change_value:.2f}%❌")
                            else:
                                status_parts.append(f"涨幅{price_change_value:.2f}%✅")

                            if not bullish_condition:
                                reasons.append(f"非阳线({bullish_desc})")
                                status_parts.append(f"{bullish_desc}❌")
                            else:
                                status_parts.append(f"{bullish_desc}✅")

                            if reasons:
                                self.add_record(f"⚠️ {code}  {' | '.join(status_parts)}")

                        # 调试模式：显示所有股票的详细信息
                        elif hasattr(self, 'debug_buy_conditions') and self.debug_buy_conditions:
                            reasons = []
                            if not amount_condition:
                                reasons.append(f"成交额{current_amount/10000:.0f}万≤5000万")
                            if not volume_ratio_condition:
                                reasons.append(f"量比{volume_ratio_value:.2f}≤3.0")
                            if not price_change_condition:
                                reasons.append(f"3周期涨幅{price_change_value:.2f}%≥5%")
                            if reasons:
                                self.add_record(f"❌ {code} 买入条件不满足: {' | '.join(reasons)}")

                    # 检查卖出信号（只在持仓时）
                    if code in self.position_records:
                        position_info = self.position_records[code]

                        # 兼容新旧格式获取买入价格和数量
                        if 'buy_queue' in position_info and position_info['buy_queue']:
                            buy_price = position_info['buy_queue'][0]['buy_price']
                            quantity = position_info.get('total_quantity', 10)
                        else:
                            buy_price = position_info.get('buy_price', 0)
                            quantity = position_info.get('quantity', 10)
                        current_profit = (current_price - buy_price) * quantity

                        # 强制止损
                        if current_profit <= -200:
                            self.add_record(f"{code} 触发强制止损: 当前价格{current_price:.3f}，买入价格{buy_price:.3f}，亏损{current_profit:.2f}元")
                            # 开盘前只记录信号，不执行卖出
                            if is_before_open:
                                self.add_record(f"[开盘前] {code} 触发强制止损 - 暂不执行卖出")
                            else:
                                self.execute_sell(code, current_price, datetime.now().strftime('%H:%M:%S'))
                            continue

                        # 主要卖出条件：连续2周期阴线
                        if self.check_consecutive_bearish_candles(df, 2):
                            self.add_record(f"{code} 触发主要卖出条件: 连续2周期阴线，当前盈亏{current_profit:.2f}元")
                            # 开盘前只记录信号，不执行卖出
                            if is_before_open:
                                self.add_record(f"[开盘前] {code} 触发主要卖出条件 - 暂不执行卖出")
                            else:
                                self.execute_sell(code, current_price, datetime.now().strftime('%H:%M:%S'))
                            continue

                        # 强制止盈
                        if current_profit >= 1000:
                            self.add_record(f"{code} 触发强制止盈: 当前价格{current_price:.3f}，买入价格{buy_price:.3f}，盈利{current_profit:.2f}元")
                            # 开盘前只记录信号，不执行卖出
                            if is_before_open:
                                self.add_record(f"[开盘前] {code} 触发强制止盈 - 暂不执行卖出")
                            else:
                                self.execute_sell(code, current_price, datetime.now().strftime('%H:%M:%S'))
                            continue
                    
                    processed_codes.append(code)
                
                except Exception as e:
                    print(f"[调试] 检查 {code} 信号时出错: {str(e)}")
                    self.add_record(f"检查 {code} 信号时出错: {str(e)}")
                    failed_codes.append(code)
            
            # 输出处理结果统计
            processed_count = len(processed_codes)
            failed_count = len(failed_codes)

            self.add_record(f"信号检查完成: 成功处理 {processed_count}/{total_codes} 只股票")

            if failed_count > 0:
                # 保存失败的股票代码到类属性，供整理板块功能使用
                if not hasattr(self, 'failed_codes'):
                    self.failed_codes = []

                # 合并新的失败代码，避免重复
                for code in failed_codes:
                    if code not in self.failed_codes:
                        self.failed_codes.append(code)

                self.add_record(f"处理失败的股票: {', '.join(failed_codes)}")
                self.add_record(f"累计失败股票数量: {len(self.failed_codes)}")

                # 如果失败数量超过10%，记录警告
                if failed_count / total_codes > 0.1:
                    self.add_record("警告: 处理失败的股票数量超过10%，请检查数据下载和计算过程")
                    self.add_record("建议使用'整理板块'功能清理失败的股票代码")
        
        except Exception as e:
            print(f"[调试] 检查交易信号时出错: {str(e)}")
            self.add_record(f"检查交易信号时出错: {str(e)}")

    def send_request(self, request_data):
        """发送请求到委托查询撤单程序"""
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            client.connect(('localhost', 9876))
            client.sendall(json.dumps(request_data).encode('utf-8'))
            client.shutdown(socket.SHUT_WR)
            
            data = b""
            while True:
                chunk = client.recv(4096)
                if not chunk:
                    break
                data += chunk
            
            response = json.loads(data.decode('utf-8'))
            return response
            
        except Exception as e:
            if self.is_connected:
                # 先更新状态标签
                if self.connection_status_label:
                    self.connection_status_label.config(text="连接状态: 未连接", foreground="red")
                    self.root.update()
                # 再添加记录
                self.add_record(f"与服务器断开连接: {str(e)}")
            self.is_connected = False
            return {"status": "error", "message": str(e)}
        finally:
            client.close()

    def execute_buy(self, code, price, time_str):
        """执行买入操作"""
        try:
            # 获取当前时间
            now = datetime.now()
            current_time = now.time()

            # 计算可以买入的股数（10股为一手）
            target_amount = 6000  # 目标金额
            quantity = int((target_amount / price) // 10) * 10
            
            # 确保至少买入10股
            if quantity < 10:
                quantity = 10
            
            # 根据股票类型调整买入价格加价幅度
            if code.startswith(('15', '5')):  # ETF
                price_adjust = 0.004
            else:  # 可转债
                price_adjust = 0.20
            
            # 记录原始价格（未加价）用于计算盈亏
            original_price = price
            
            # 买入价格加价（仅用于委托）
            buy_price = price + price_adjust
            
            # 规范化股票代码格式
            code = code.upper()
            
            # 成交额策略，直接记录买入信息
            
            # 获取当前5分钟周期
            current_time = datetime.now()
            current_minute = current_time.minute
            current_period = current_minute // 5
            
            if self.trading_enabled:
                request = {
                    'type': 'place_order',
                    'params': {
                        'stock_code': code,
                        'direction': STOCK_BUY,
                        'volume': quantity,
                        'price_type': FIX_PRICE,
                        'price': buy_price  # 委托时使用加价后的价格
                    }
                }
                
                response = self.send_request(request)
                if response['status'] != 'success':
                    self.add_record(f"买入委托失败 {code}: {response.get('message', '未知错误')}")
                    return
                
                order_id = response['data']['order_id']
                self.add_record(f"买入委托已提交 {code} 数量:{quantity}股 委托号:{order_id} 价格:{buy_price:.2f}")
                
                # 存储买入指令
                self.trade_instructions[code] = {
                    'type': 'buy',
                    'order_id': order_id,
                    'time': time_str,
                    'price': buy_price,
                    'original_price': original_price  # 记录原始价格
                }
            else:
                order_id = f"VIRTUAL_{int(time.time())}"
                self.add_record(f"[虚拟]买入委托 {code} 数量:{quantity}股 价格:{buy_price:.2f}")
            
            # 记录买入信息，使用原始价格计算成本和盈亏 - 使用新格式
            buy_record = {
                'buy_price': float(original_price),  # 使用原始价格
                'buy_time': time_str,
                'quantity': int(quantity),
                'fee': float(original_price * quantity * 0.0001),  # 使用原始价格计算手续费
                'actual_amount': float(original_price * quantity),  # 使用原始价格计算实际金额
                'order_id': order_id,
                'virtual': bool(not self.trading_enabled),
                'below_exp3_at_buy': False,
                'crossed_exp3': False
            }

            self.position_records[code] = {
                'buy_queue': [buy_record],
                'total_quantity': int(quantity),
                'total_cost': float(original_price * quantity),
                'total_fee': float(original_price * quantity * 0.0001),
                'order_price': float(buy_price),     # 记录委托价格
                'buy_period': current_period,
                'buy_hour': now.hour,
                'periods_since_buy': 0,
                'profit_check_done': False
            }
            
            # 更新持仓列表显示
            self.update_position_list()
            
            # 保存交易数据
            self.save_trading_data()
            
        except Exception as e:
            self.add_record(f"买入 {code} 失败: {str(e)}")
    
    def execute_sell(self, code, price, time_str):
        """执行卖出操作
        Args:
            code: 股票代码
            price: 卖出价格
            time_str: 时间字符串
        """
        try:
            if code not in self.position_records:
                return

            # 获取持仓信息
            position_info = self.position_records[code]
            buy_price = position_info['buy_price']

            # 根据股票类型调整卖出价格降价幅度
            if code.startswith(('15', '5')):  # ETF
                price_adjust = 0.004
            else:  # 可转债
                price_adjust = 0.40
            
            # 获取持仓信息
            position_info = self.position_records[code]
            quantity = position_info.get('quantity', 10)
            buy_price = position_info['buy_price']
            buy_fee = position_info.get('fee', 0)
            is_virtual = position_info.get('virtual', False)
            
            if self.trading_enabled and not is_virtual:
                # 先查询实际持仓
                request = {'type': 'query_positions'}
                response = self.send_request(request)
                
                if response['status'] != 'success':
                    self.add_record(f"查询持仓失败: {response.get('message', '未知错误')}")
                    return
                
                # 检查是否有足够的持仓
                position_found = False
                actual_volume = 0
                for position in response['data']:
                    if position['stock_code'] == code:
                        position_found = True
                        actual_volume = position['volume']
                        break
                        
                if not position_found:
                    self.add_record(f"未找到 {code} 的实际持仓,取消卖出")
                    # 清理所有相关记录
                    if code in self.position_records:
                        del self.position_records[code]
                    if code in self.trade_instructions:
                        del self.trade_instructions[code]
                    self.update_position_list()
                    self.save_trading_data()
                    return
                
                # 如果实际持仓小于要卖出的数量,调整卖出数量
                if actual_volume < quantity:
                    self.add_record(f"{code} 实际持仓({actual_volume})小于记录持仓({quantity}),调整卖出数量")
                    quantity = actual_volume
                    if quantity == 0:
                        self.add_record(f"{code} 无可卖出持仓,取消卖出")
                        # 清理所有相关记录
                        if code in self.position_records:
                            del self.position_records[code]
                        if code in self.trade_instructions:
                            del self.trade_instructions[code]
                        self.update_position_list()
                        self.save_trading_data()
                        return
                
                # 发送卖出委托请求
                request = {
                    'type': 'place_order',
                    'params': {
                        'stock_code': code,
                        'direction': STOCK_SELL,
                        'volume': quantity,
                        'price_type': FIX_PRICE,
                        'price': price - price_adjust
                    }
                }
                
                # 发送委托请求
                response = self.send_request(request)
                if response['status'] != 'success':
                    self.add_record(f"卖出委托失败 {code}: {response.get('message', '未知错误')}")
                    return
                
                # 获取委托号
                order_id = response['data']['order_id']
                self.add_record(f"卖出委托已提交 {code} 委托号: {order_id}")
                
                # 记录卖出委托号到持仓记录
                self.position_records[code]['sell_order_id'] = order_id
                
                # 尝试获取实际成交价格（一段时间后再查询）
                def check_sell_trade_report():
                    # 延迟5秒后查询成交回报
                    time.sleep(5)
                    try:
                        # 查询该委托的成交情况
                        real_sell_price = None
                        
                        # 查询当日成交
                        request = {
                            'type': 'query_trades',
                            'params': {
                                'start_date': datetime.now().strftime('%Y%m%d'),
                                'end_date': datetime.now().strftime('%Y%m%d'),
                                'order_id': order_id
                            }
                        }
                        response = self.send_request(request)
                        
                        if response['status'] == 'success' and response['data']:
                            for trade in response['data']:
                                if trade['direction'] == STOCK_SELL and trade['order_id'] == order_id:
                                    real_sell_price = trade['price']
                                    self.add_record(f"获取到 {code} 的实际卖出价格: {real_sell_price:.3f}")
                                    break
                        
                        # 如果找到实际成交价格，更新交易记录
                        if real_sell_price:
                            for record in self.trade_records:
                                if record.get('sell_order_id') == order_id:
                                    original_price = record['sell_price']
                                    record['sell_price'] = real_sell_price
                                    record['profit'] = round((real_sell_price * quantity) - 
                                                          record.get('actual_amount', buy_price * quantity) - 
                                                          record.get('total_fee', 0), 2)
                                    record['profit_percent'] = round(record['profit'] / 
                                                                  record.get('actual_amount', buy_price * quantity) * 100, 2)
                                    
                                    # 更新当日总盈亏
                                    self.daily_total_profit = round(self.daily_total_profit + record['profit'], 2)
                                    
                                    self.add_record(f"已更新 {code} 的卖出交易记录价格: {original_price:.3f} -> {real_sell_price:.3f}")
                                    self.save_trading_data()
                                    break
                    except Exception as e:
                        self.add_record(f"查询卖出成交回报失败: {str(e)}")
                
                # 启动线程等待并查询成交回报
                threading.Thread(target=check_sell_trade_report, daemon=True).start()
                
            else:
                # 虚拟卖出
                order_id = f"VIRTUAL_SELL_{int(time.time())}"
                self.add_record(f"[虚拟]卖出委托 {code} 价格: {price:.2f}")
            
            # 计算卖出金额和手续费
            sell_amount = price * quantity
            sell_fee = sell_amount * 0.0001
            total_fee = buy_fee + sell_fee
            
            # 计算盈亏
            profit = sell_amount - position_info.get('actual_amount', buy_price * quantity) - total_fee
            profit_percent = (profit / position_info.get('actual_amount', buy_price * quantity)) * 100
            
            # 只在非实盘交易时更新当日总盈亏（实盘交易在成交回报中更新）
            if not self.trading_enabled or is_virtual:
                self.daily_total_profit = round(self.daily_total_profit + profit, 2)
            
            # 显示卖出信息
            virtual_tag = "[虚拟]" if is_virtual else ""
            sell_record = f"{virtual_tag}卖出 {code} 价格:{price:.2f} 数量:{quantity} 盈亏:{profit:.2f}元({profit_percent:.2f}%)"
            self.add_record(sell_record)
            
            # 记录完整交易信息到交易历史
            self.trade_records.append({
                'code': code,
                'buy_price': buy_price,
                'buy_time': position_info['buy_time'],
                'sell_price': price,
                'sell_time': time_str,
                'quantity': quantity,
                'buy_fee': round(buy_fee, 2),
                'sell_fee': round(sell_fee, 2),
                'total_fee': round(total_fee, 2),
                'profit': round(profit, 2),
                'profit_percent': round(profit_percent, 2),
                'buy_order_id': position_info.get('order_id'),
                'sell_order_id': order_id,
                'virtual': is_virtual,
                'actual_amount': position_info.get('actual_amount', buy_price * quantity)
            })

            # 更新当日最大盈利和最大亏损
            if profit > self.max_profit_today:
                self.max_profit_today = profit
            if profit < 0 and abs(profit) > self.max_loss_today:
                self.max_loss_today = abs(profit)

            # 清理所有相关记录
            if code in self.position_records:
                del self.position_records[code]
            if code in self.trade_instructions:
                del self.trade_instructions[code]

            # 更新界面和保存数据
            self.update_position_list()
            self.update_trade_summary()
            self.save_trading_data()
            
        except Exception as e:
            self.add_record(f"卖出 {code} 失败: {str(e)}")
            try:
                if code in self.position_records:
                    del self.position_records[code]
                if code in self.trade_instructions:
                    del self.trade_instructions[code]
                self.update_position_list()
                self.save_trading_data()
            except:
                pass
    
    def execute_protective_sell(self, code, price, time_str, avg_price):
        """执行保护性卖出操作"""
        try:
            if code not in self.position_records:
                return
            
            # 根据股票类型调整卖出价格降价幅度
            if code.startswith(('15', '5')):  # ETF
                price_adjust = 0.004
            else:  # 可转债
                price_adjust = 0.40
            
            # 先查询实际持仓
            request = {'type': 'query_positions'}
            response = self.send_request(request)
            
            if response['status'] != 'success':
                self.add_record(f"查询持仓失败: {response.get('message', '未知错误')}")
                return
            
            # 检查是否有足够的持仓
            position_found = False
            actual_volume = 0
            for position in response['data']:
                if position['stock_code'] == code:
                    position_found = True
                    actual_volume = position['volume']
                    break
            
            if not position_found:
                self.add_record(f"未找到 {code} 的实际持仓,取消保护性卖出")
                # 从持仓记录中移除
                if code in self.position_records:
                    del self.position_records[code]
                self.update_position_list()
                self.save_trading_data()
                return
            
            # 获取本地记录的持仓信息
            buy_info = self.position_records[code]
            quantity = buy_info.get('quantity', 10)
            
            # 如果实际持仓小于要卖出的数量,调整卖出数量
            if actual_volume < quantity:
                self.add_record(f"{code} 实际持仓({actual_volume})小于记录持仓({quantity}),调整保护性卖出数量")
                quantity = actual_volume
                if quantity == 0:
                    self.add_record(f"{code} 无可卖出持仓,取消保护性卖出")
                    # 从持仓记录中移除
                    if code in self.position_records:
                        del self.position_records[code]
                    self.update_position_list()
                    self.save_trading_data()
                    return

            # 其他保护性卖出逻辑保持不变...
            buy_price = buy_info['buy_price']
            buy_fee = buy_info.get('fee', 0)
            
            # 发送卖出委托请求
            request = {
                'type': 'place_order',
                'params': {
                    'stock_code': code,
                    'direction': STOCK_SELL,  # 卖出指令
                    'volume': quantity,
                    'price_type': FIX_PRICE,  # 限价委托
                    'price': price - price_adjust  # 卖出价格降低0.40元提高成交概率
                }
            }
            
            # 发送委托请求
            response = self.send_request(request)
            if response['status'] != 'success':
                self.add_record(f"保护性卖出委托失败 {code}: {response.get('message', '未知错误')}")
                return
            
            # 获取委托号
            order_id = response['data']['order_id']
            self.add_record(f"保护性卖出委托已提交 {code} 委托号: {order_id}")
            
            # 记录卖出委托号到持仓记录
            self.position_records[code]['sell_order_id'] = order_id
            
            # 计算卖出金额和手续费
            sell_amount = price * quantity
            sell_fee = sell_amount * 0.0001
            total_fee = buy_fee + sell_fee
            
            # 计算盈亏
            profit = sell_amount - buy_info.get('actual_amount', buy_price * quantity) - total_fee
            profit_percent = (profit / buy_info.get('actual_amount', buy_price * quantity)) * 100
            
            # 显示卖出信息（包含保护性卖出原因）
            sell_record = (f"卖出 {code} 价格:{price:.2f} 数量:{quantity} 盈亏:{profit:.2f}元({profit_percent:.2f}%) "
                          f"[保护性卖出: 价格{price:.2f} < 均价{avg_price:.2f}]")
            self.add_record(sell_record)
            
            # 记录完整交易信息到交易历史
            self.trade_records.append({
                'code': code,
                'buy_price': buy_price,
                'buy_time': buy_info['buy_time'],
                'sell_price': price,
                'sell_time': time_str,
                'quantity': quantity,
                'buy_fee': round(buy_fee, 2),
                'sell_fee': round(sell_fee, 2),
                'total_fee': round(total_fee, 2),
                'profit': round(profit, 2),
                'profit_percent': round(profit_percent, 2),
                'buy_order_id': buy_info.get('order_id'),
                'sell_order_id': order_id,
                'reason': f"保护性卖出: 价格{price:.2f} < 均价{avg_price:.2f}"
            })
            
            # 更新界面和保存数据
            self.update_position_list()
            self.update_trade_summary()
            self.save_trading_data()
            
        except Exception as e:
            self.add_record(f"保护性卖出 {code} 失败: {str(e)}")
    
    def add_record(self, text):
        """添加记录到记录列表"""
        try:
            if hasattr(self, 'record_listbox'):
                self._add_record_impl(text)
            else:
                print(f"记录列表未初始化: {text}")
        except Exception as e:
            print(f"添加记录失败: {str(e)}")
    
    def _add_record_impl(self, text):
        """实际添加记录的实现"""
        try:
            # 添加时间戳
            timestamp = datetime.now().strftime('%H:%M:%S')
            text_with_time = f"{timestamp} {text}"
            
            # 添加到记录列表框
            self.record_listbox.insert(0, text_with_time)  # 新记录添加到顶部
        except Exception as e:
            print(f"添加记录失败: {str(e)}")
    
    def add_trade_record(self, text):
        """添加交易记录"""
        # 添加到通用记录
        self.add_record(text)
    

    
    def update_position_list(self):
        """更新持仓列表显示"""
        try:
            # 清空列表框
            self.position_listbox.delete(0, tk.END)

            # 初始化当前持仓计数和浮动盈亏
            current_positions = 0
            total_floating_profit = 0.0

            # 存储当前价格数据，用于与交易汇总同步
            self.current_price_data = {}

            # 记录价格获取失败的股票
            failed_price_codes = []

            if self.trading_enabled:
                # 查询实际持仓
                request = {'type': 'query_positions'}
                response = self.send_request(request)

                if response['status'] != 'success':
                    self.add_record(f"查询持仓失败: {response.get('message', '未知错误')}")
                    return

                # 创建一个新的字典来存储更新后的持仓记录
                updated_positions = {}

                # 遍历服务器返回的持仓数据
                for position in response['data']:
                    code = position['stock_code']
                    volume = position['volume']

                    # 只处理可转债(11或12开头)和ETF(15或5开头)
                    if not (code.startswith(('11', '12', '15', '5'))):
                        continue

                    if volume <= 0:  # 跳过零持仓
                        continue

                    # 跳过用户手动清除的持仓
                    if code in self.manually_cleared_positions:
                        self.add_record(f"[调试] 跳过用户手动清除的持仓: {code}")
                        continue

                    current_positions += 1  # 增加持仓计数

                    # 获取当前价格并缓存
                    current_price = self.get_current_price(code)
                    if not current_price:
                        failed_price_codes.append(code)
                        continue

                    # 缓存价格数据供交易汇总使用
                    self.current_price_data[code] = current_price

                    # 从本地记录中获取买入信息，如果没有则创建新记录
                    buy_info = self.position_records.get(code, {})
                    if not buy_info:
                        # 尝试从成交回报获取真实成交价格
                        self.add_record(f"发现新持仓 {code}，尝试从成交回报获取实际成交价格")

                        # 临时创建一个基础记录以便显示 - 使用新格式
                        buy_record = {
                            'buy_price': current_price,  # 临时使用当前价格，后续会尝试更新
                            'buy_time': datetime.now().strftime('%H:%M:%S'),
                            'quantity': volume,
                            'fee': current_price * volume * 0.0001,  # 手续费万分之一
                            'actual_amount': current_price * volume,
                            'order_id': f"NEW_POSITION_{int(time.time())}",
                            'virtual': False,
                            'below_exp3_at_buy': False,
                            'crossed_exp3': False
                        }

                        buy_info = {
                            'buy_queue': [buy_record],
                            'total_quantity': volume,
                            'total_cost': current_price * volume,
                            'total_fee': current_price * volume * 0.0001
                        }

                        # 添加到持仓记录
                        self.position_records[code] = buy_info

                        # 尝试从成交回报获取真实成交价格并更新记录
                        self.process_trade_reports(code)

                        # 重新获取已更新的记录
                        buy_info = self.position_records.get(code, buy_info)

                    # 使用工具类计算浮动盈亏
                    buy_price = buy_info['buy_price']
                    trade_profit = self.price_calculator.calculate_trade_profit(
                        buy_price, current_price, volume)

                    floating_profit = trade_profit['net_profit']
                    total_floating_profit += floating_profit

                    # 更新单只股票的最大浮盈和最大浮亏
                    if floating_profit > 0:
                        self.single_stock_max_profit = max(self.single_stock_max_profit, floating_profit)
                    elif floating_profit < 0:
                        # 对于亏损，我们记录最大的亏损绝对值
                        self.single_stock_max_loss = max(self.single_stock_max_loss, abs(floating_profit))

                    # 更新持仓记录
                    updated_positions[code] = buy_info

                    # 显示持仓信息
                    self.display_position(code, volume, current_price, buy_info)

                # 更新本地持仓记录
                self.position_records = updated_positions

            else:
                # 显示虚拟持仓
                for code, info in self.position_records.items():
                    if info.get('virtual', False):  # 只显示虚拟持仓
                        current_positions += 1  # 增加持仓计数
                        volume = info.get('quantity', 0)
                        current_price = self.get_current_price(code)
                        if current_price:
                            # 缓存价格数据供交易汇总使用
                            self.current_price_data[code] = current_price

                            # 计算该持仓的浮动盈亏（与持仓显示保持一致，扣除手续费）
                            buy_price = info['buy_price']
                            gross_profit = (current_price - buy_price) * volume

                            # 计算手续费
                            buy_fee = buy_price * volume * 0.0003
                            sell_fee = current_price * volume * 0.0003
                            total_fee = buy_fee + sell_fee

                            # 净浮动盈亏（扣除手续费）
                            floating_profit = gross_profit - total_fee
                            total_floating_profit += floating_profit

                            # 更新单只股票的最大浮盈和最大浮亏
                            if floating_profit > 0:
                                self.single_stock_max_profit = max(self.single_stock_max_profit, floating_profit)
                            elif floating_profit < 0:
                                # 对于亏损，我们记录最大的亏损绝对值
                                self.single_stock_max_loss = max(self.single_stock_max_loss, abs(floating_profit))
                        else:
                            failed_price_codes.append(code)

                        self.display_position(code, volume, current_price, info, virtual=True)

            # 存储计算出的总浮盈，供交易汇总使用
            self.current_floating_profit = total_floating_profit



            # 更新最大持仓数
            self.max_positions_today = max(self.max_positions_today, current_positions)

            # 更新持仓统计显示
            stats_text = (
                f"当前持仓: {current_positions} | "
                f"今日最大持仓: {self.max_positions_today} | "
                f"单股最大浮盈: {self.single_stock_max_profit:,.2f} | "
                f"单股最大浮亏: {self.single_stock_max_loss:,.2f}"
            )
            self.position_stats_label.config(text=stats_text)

            # 保存更新后的持仓数据
            self.save_trading_data()

            # 更新交易汇总（使用缓存的价格数据）
            self.update_trade_summary()

            # 静默完成更新（移除性能警告）
            pass

        except Exception as e:
            self.add_record(f"更新持仓列表失败: {str(e)}")
            print(f"更新持仓列表失败: {str(e)}")  # 添加控制台输出以便调试
    
    def display_position(self, code, volume, current_price, buy_info, virtual=False):
        """显示持仓信息"""
        if not buy_info:
            return
            
        buy_price = buy_info.get('buy_price', 0)  # 使用原始买入价格
        order_price = buy_info.get('order_price', buy_price)  # 获取委托价格，如果没有则使用买入价格
        buy_time = buy_info.get('buy_time', '')
        
        # 计算盈亏（使用原始价格）
        profit = (current_price - buy_price) * volume
        profit_percent = (current_price - buy_price) / buy_price * 100

        # 计算手续费（使用原始价格）
        buy_fee = buy_price * volume * 0.0003
        sell_fee = current_price * volume * 0.0003
        total_fee = buy_fee + sell_fee
        
        # 计算总盈亏（含手续费）
        total_profit = profit - total_fee
        total_profit_percent = total_profit / (buy_price * volume) * 100
        
        # 显示持仓信息，包含数量、原始成本和委托价格
        position_text = (
            f"{'[虚拟]' if virtual else ''}{code} "
            f"数量:{volume}股 "
            f"成本:{buy_price:.3f}(委托:{order_price:.3f}) "
            f"现价:{current_price:.3f} "
            f"盈亏:{total_profit:.2f}({total_profit_percent:.2f}%) "
            f"买入:{buy_time}"
        )

        # 静默显示持仓（移除调试信息）
        
        # 根据盈亏情况设置颜色
        if total_profit > 0:
            color = 'red'
        elif total_profit < 0:
            color = 'green'
        else:
            color = 'black'
        
        # 将持仓信息添加到列表框中
        self.position_listbox.insert(tk.END, position_text)
        last_index = self.position_listbox.size() - 1
        self.position_listbox.itemconfig(last_index, {'fg': color})
    
    def get_current_price(self, code):
        """获取股票当前价格 - 使用实时tick数据"""
        try:
            # 优先使用实时tick数据获取最新价格
            tick = xtdata.get_full_tick([code])
            if tick and code in tick and 'lastPrice' in tick[code]:
                last_price = tick[code]["lastPrice"]
                if last_price and last_price > 0:
                    return round(last_price, 3)

            # 如果实时数据获取失败，回退到K线数据

            # 获取当前时间
            now = datetime.now()
            current_time = now.time()
            current_hour = now.hour
            current_minute = now.minute

            # 判断是否在交易时间内
            is_trading_time = ('09:30:00' <= current_time.strftime('%H:%M:%S') <= '11:30:00') or \
                            ('13:00:00' <= current_time.strftime('%H:%M:%S') <= '15:00:00')

            # 如果是收盘后（15:00之后），使用15:00的数据
            if current_hour > 15 or (current_hour == 15 and current_minute > 0):
                # 获取分钟级别数据
                today = datetime.now().strftime('%Y%m%d')
                minute_data = xtdata.get_market_data(
                    field_list=['time', 'close'],
                    stock_list=[code],
                    period='1m',
                    start_time=today,
                    end_time=today,
                    count=-1
                )

                # 检查数据是否有效
                if not minute_data or code not in minute_data['time'].index:
                    return None

                # 找到15:00:00的数据
                df = pd.DataFrame({
                    'time': pd.to_datetime(minute_data['time'].loc[code], unit='ms').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai'),
                    'close': minute_data['close'].loc[code]
                })

                # 查找15:00:00的数据
                closing_data = df[df['time'].dt.strftime('%H:%M:%S') == '15:00:00']
                if not closing_data.empty:
                    return round(closing_data['close'].iloc[-1], 3)

                # 如果找不到15:00:00的数据，使用最后一条数据
                if not df.empty:
                    return round(df['close'].iloc[-1], 3)

                return None

            # 交易时段内或非交易时段，都尝试使用最新的K线数据
            today = datetime.now().strftime('%Y%m%d')

            # 安全获取周期，如果属性不存在则使用默认值
            period = getattr(self, 'current_period', '5m')

            minute_data = xtdata.get_market_data(
                field_list=['time', 'close'],
                stock_list=[code],
                period=period,
                start_time=today,
                end_time=today,
                count=-1
            )

            # 检查数据是否有效
            if not minute_data or code not in minute_data['time'].index or len(minute_data['time'].loc[code]) == 0:
                return None

            # 创建DataFrame
            df = pd.DataFrame({
                'time': pd.to_datetime(minute_data['time'].loc[code], unit='ms').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai'),
                'close': minute_data['close'].loc[code]
            })

            if not df.empty:
                return round(df['close'].iloc[-1], 3)

            return None

        except Exception as e:
            print(f"获取 {code} 当前价格时出错: {str(e)}")
            return None
        finally:
            # 静默完成（移除性能警告）
            pass
    
    def update_trade_summary(self):
        """更新交易汇总信息"""
        try:
            # 使用缓存的浮盈数据，如果没有则重新计算
            if hasattr(self, 'current_floating_profit'):
                total_float_profit = self.current_floating_profit
            else:
                # 重新计算（兼容性处理）
                total_float_profit = 0.0
                for code in list(self.position_records.keys()):
                    # 优先使用缓存的价格数据
                    if hasattr(self, 'current_price_data') and code in self.current_price_data:
                        current_price = self.current_price_data[code]
                    else:
                        current_price = self.get_current_price(code)

                    if current_price is None:
                        continue

                    position_info = self.position_records[code]
                    volume = position_info.get('quantity', 0)
                    buy_price = float(position_info['buy_price'])

                    # 计算浮动盈亏（与持仓显示保持一致，扣除手续费）
                    gross_profit = (current_price - buy_price) * volume

                    # 计算手续费
                    buy_fee = buy_price * volume * 0.0003
                    sell_fee = current_price * volume * 0.0003
                    total_fee = buy_fee + sell_fee

                    # 净浮动盈亏（扣除手续费）
                    float_profit = gross_profit - total_fee
                    total_float_profit += float_profit

            # 计算持仓成本
            total_cost = 0.0
            current_positions = len(self.position_records)

            for code, position_info in self.position_records.items():
                volume = position_info.get('quantity', 0)
                buy_price = float(position_info['buy_price'])
                cost = buy_price * volume
                total_cost += cost

            # 更新当日最大浮盈和最大浮亏
            if total_float_profit > self.daily_max_float_profit:
                self.daily_max_float_profit = total_float_profit
            if total_float_profit < self.daily_max_float_loss:
                self.daily_max_float_loss = total_float_profit

            # 更新持仓数量统计（已在上面的update_position_list中更新，这里移除重复逻辑）

            # 更新当日最大盈利和最大亏损（基于已完成的交易）
            if self.trade_records:
                for record in self.trade_records:
                    profit = record.get('profit', 0)
                    if profit > self.max_profit_today:
                        self.max_profit_today = profit
                    if profit < 0 and abs(profit) > self.max_loss_today:
                        self.max_loss_today = abs(profit)

            # 计算交易统计
            total_trades = len(self.trade_records)
            profit_trades = sum(1 for record in self.trade_records if record['profit'] > 0)
            loss_trades = sum(1 for record in self.trade_records if record['profit'] <= 0)

            # 添加调试信息：检查浮盈一致性
            debug_info = ""
            if hasattr(self, 'current_floating_profit'):
                cached_profit = self.current_floating_profit
                if abs(total_float_profit - cached_profit) > 0.01:
                    debug_info = f" [不一致警告: 缓存={cached_profit:.2f}]"

            # 构建显示文本
            summary_text = (
                f"总交易: {total_trades}次 盈利: {profit_trades}次 亏损: {loss_trades}次\n"
                f"当前浮盈: {total_float_profit:,.2f}元{debug_info}\n"
                f"当日最大浮盈: {self.daily_max_float_profit:,.2f}元\n"
                f"当日最大浮亏: {self.daily_max_float_loss:,.2f}元\n"
                f"持仓成本: {total_cost:,.2f}元\n"
                f"当日总盈亏: {self.daily_total_profit:,.2f}元"
            )

            # 更新显示
            self.trade_summary_label.config(text=summary_text)

            # 保存更新后的状态信息
            self.save_state()

        except Exception as e:
            self.add_record(f"更新交易汇总失败: {str(e)}")
            print(f"更新交易汇总失败: {str(e)}")  # 添加控制台输出以便调试
    
    def import_trading_data(self):
        """从文件导入交易数据"""
        try:
            # 打开文件选择对话框
            position_file = filedialog.askopenfilename(
                title="选择持仓记录文件",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            
            if not position_file:
                return  # 用户取消了选择
                
            # 加载持仓记录
            with open(position_file, 'r', encoding='utf-8') as f:
                self.position_records = json.load(f)
            
            # 更新持仓列表
            self.update_position_list()
            
            # 提示用户选择交易历史文件
            trade_file = filedialog.askopenfilename(
                title="选择交易历史文件",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            
            if trade_file:
                # 加载交易历史
                with open(trade_file, 'r', encoding='utf-8') as f:
                    self.trade_records = json.load(f)
                
                # 更新交易汇总
                self.update_trade_summary()
            
            # 保存到当日文件
            self.save_trading_data()
            
            messagebox.showinfo("成功", f"已导入 {len(self.position_records)} 条持仓记录和 {len(self.trade_records)} 条交易历史")
            
        except Exception as e:
            messagebox.showerror("错误", f"导入交易数据失败: {str(e)}")

    def export_trading_data(self):
        """导出交易数据到用户指定文件"""
        try:
            # 选择保存持仓记录的位置
            position_file = filedialog.asksaveasfilename(
                title="保存持仓记录",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
                initialfile=f"持仓记录_{self.today}_导出.json"
            )
            
            if not position_file:
                return  # 用户取消了选择
                
            # 保存持仓记录
            with open(position_file, 'w', encoding='utf-8') as f:
                json.dump(self.position_records, f, ensure_ascii=False, indent=2)
            
            # 选择保存交易历史的位置
            trade_file = filedialog.asksaveasfilename(
                title="保存交易历史",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
                initialfile=f"交易历史_{self.today}_导出.json"
            )
            
            if trade_file:
                # 保存交易历史
                with open(trade_file, 'w', encoding='utf-8') as f:
                    json.dump(self.trade_records, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("成功", "交易数据导出成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"导出交易数据失败: {str(e)}")

    def monitor_orders(self):
        """监控委托状态的线程函数"""
        # 添加委托状态缓存
        order_status_cache = {}
        
        while self.monitoring:
            try:
                # 检查是否在交易时间
                current_time = datetime.now().time()
                if not (('09:30:00' <= current_time.strftime('%H:%M:%S') <= '11:30:00') or 
                       ('13:00:00' <= current_time.strftime('%H:%M:%S') <= '14:57:00')):
                    time.sleep(60)
                    continue
                
                # 如果没有未完成的买卖指令，直接跳过
                if not self.trade_instructions:
                    time.sleep(10)
                    continue
                
                # 获取当天日期
                today = datetime.now().strftime('%Y%m%d')
                
                # 获取需要查询的股票列表
                monitor_codes = list(self.trade_instructions.keys())
                
                # 查询当日委托
                request = {
                    'type': 'query_orders',
                    'params': {
                        'start_date': today,
                        'end_date': today,
                        'stock_codes': monitor_codes
                    }
                }
                response = self.send_request(request)
                
                if response['status'] != 'success':
                    self.add_record(f"查询委托失败: {response.get('message', '未知错误')}")
                    time.sleep(10)
                    continue
                
                # 检查每个委托的状态
                for order in response['data']:
                    code = order['stock_code']
                    order_id = order['order_id']
                    status = order['order_status']
                    volume = order['traded_volume']
                    
                    # 检查委托状态是否发生变化
                    cache_key = f"{code}_{order_id}"
                    if cache_key in order_status_cache:
                        cached_status = order_status_cache[cache_key]
                        if cached_status['status'] == status and cached_status['volume'] == volume:
                            continue  # 状态未变化，跳过输出
                    
                    # 更新缓存
                    order_status_cache[cache_key] = {
                        'status': status,
                        'volume': volume
                    }
                    
                    # 输出委托详细信息
                    self.add_record(f"检查委托: {code} 委托号: {order_id} 状态: {status} 成交量: {volume}")
                    
                    # 检查委托号是否匹配
                    if code in self.trade_instructions:
                        instruction = self.trade_instructions[code]
                        if order_id != instruction['order_id']:
                            self.add_record(f"跳过不匹配的委托: {code} 期望: {instruction['order_id']} 实际: {order_id}")
                            continue
                        
                        # 检查委托状态
                        if status in [56, 57]:  # 已成、废单
                            self.add_record(f"委托已完成: {code} 状态: {status}")
                            
                            # 如果已成交，立即更新成交价格
                            if status == 56 and volume > 0:
                                self.process_trade_reports(code, order_id)
                                
                            del self.trade_instructions[code]  # 删除已完成的指令
                            if cache_key in order_status_cache:
                                del order_status_cache[cache_key]  # 删除缓存
                            continue
                        
                        # 检查成交量
                        if volume == 0:
                            # 获取委托时长(秒)
                            order_time = datetime.fromtimestamp(order['order_time']/1000)
                            elapsed_seconds = (datetime.now() - order_time).total_seconds()
                            
                            # 如果委托超过10秒未成交，执行撤单重试
                            if elapsed_seconds >= 10:
                                self.add_record(f"开始重试委托: {code} 委托号: {order_id} 已等待: {elapsed_seconds}秒")
                                self.retry_failed_order(order)
                        elif volume > 0 and status == 55:  # 部分成交
                            # 有部分成交，立即处理成交回报
                            self.process_trade_reports(code, order_id)

                # 定时处理所有持仓的成交回报，确保价格更新
                self.process_all_trade_reports()
                
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                self.add_record(f"监控委托出错: {str(e)}")
                time.sleep(10)

    def process_trade_reports(self, code=None, order_id=None):
        """处理成交回报"""
        try:
            # 查询成交回报
            request = {'type': 'query_trade_reports'}
            response = self.send_request(request)
            
            if response['status'] != 'success':
                self.add_record(f"查询成交回报失败: {response.get('message', '未知错误')}")
                return
            
            # 处理成交回报
            for trade in response['data']:
                trade_code = trade['stock_code']
                trade_direction = trade['direction']
                trade_volume = trade['volume']
                trade_price = trade['price']
                trade_order_id = trade['order_id']
                trade_amount = trade_price * trade_volume
                
                # 如果指定了code和order_id，只处理匹配的成交回报
                if code and order_id:
                    if trade_code != code or trade_order_id != order_id:
                        continue
                
                # 记录成交信息
                self.add_record(f"成交回报: {trade_code} 委托号:{trade_order_id} 方向:{'买入' if trade_direction == STOCK_BUY else '卖出'} "
                              f"数量:{trade_volume} 价格:{trade_price:.3f} 金额:{trade_amount:.2f}")
                
                # 处理买入成交
                if trade_direction == STOCK_BUY:
                    # 更新持仓记录的买入价格
                    if trade_code in self.position_records:
                        position_info = self.position_records[trade_code]

                        # 检查是否是新格式
                        if 'buy_queue' in position_info and position_info['buy_queue']:
                            original_price = position_info['buy_queue'][0]['buy_price']
                            position_info['buy_queue'][0]['buy_price'] = trade_price
                            position_info['buy_queue'][0]['actual_amount'] = trade_price * position_info['buy_queue'][0]['quantity']
                            # 更新总成本
                            position_info['total_cost'] = trade_price * position_info['total_quantity']
                            self.add_record(f"已更新 {trade_code} 的买入价格: {original_price:.3f} -> {trade_price:.3f}")
                        else:
                            # 旧格式兼容（但应该转换为新格式）
                            original_price = position_info.get('buy_price', trade_price)
                            self.add_record(f"发现旧格式持仓记录 {trade_code}，建议重启系统完成格式转换")
                
                # 处理卖出成交
                elif trade_direction == STOCK_SELL:
                    # 计算卖出盈亏
                    if trade_code in self.position_records:
                        position_info = self.position_records[trade_code]

                        # 检查是否是新格式
                        if 'buy_queue' in position_info and position_info['buy_queue']:
                            buy_price = position_info['buy_queue'][0]['buy_price']
                            buy_volume = position_info['total_quantity']
                        else:
                            # 旧格式兼容
                            buy_price = position_info.get('buy_price', 0)
                            buy_volume = position_info.get('quantity', 0)

                        # 计算盈亏
                        profit = (trade_price - buy_price) * trade_volume

                        # 更新当日总盈亏
                        self.daily_total_profit += profit

                        # 如果全部卖出，清理持仓记录
                        if trade_volume >= buy_volume:
                            if trade_code in self.position_records:
                                del self.position_records[trade_code]
                            if trade_code in self.trade_instructions:
                                del self.trade_instructions[trade_code]
                        else:
                            # 部分卖出，更新剩余持仓
                            if 'buy_queue' in position_info:
                                # 新格式：更新总数量
                                position_info['total_quantity'] = buy_volume - trade_volume
                                position_info['total_cost'] = buy_price * (buy_volume - trade_volume)
                            else:
                                # 旧格式兼容（但建议转换）
                                self.add_record(f"发现旧格式持仓记录 {trade_code}，建议重启系统完成格式转换")
                
                # 更新界面显示
                self.update_position_list()
                self.save_trading_data()
            
        except Exception as e:
            self.add_record(f"处理成交回报失败: {str(e)}")

    def process_all_trade_reports(self):
        """处理所有持仓的成交回报，确保价格更新"""
        try:
            # 只有在启用实际交易时才进行处理
            if not self.trading_enabled or not self.position_records:
                return
                
            # 处理所有持仓
            self.process_trade_reports()
            
        except Exception as e:
            self.add_record(f"处理所有成交回报时出错: {str(e)}")

    def retry_failed_order(self, failed_order):
        """重试失效的委托"""
        try:
            code = failed_order['stock_code']
            
            # 检查重试次数
            if not hasattr(self, 'retry_counts'):
                self.retry_counts = {}
            
            # 如果已经取消过交易，直接返回
            if code not in self.retry_counts:
                self.retry_counts[code] = 1
            else:
                # 如果已经达到3次，说明已经取消过交易，直接返回
                if self.retry_counts[code] >= 3:
                    self.add_record(f"{code} 委托连续失败3次，取消交易")
                    if code in self.trade_instructions:
                        del self.trade_instructions[code]  # 删除指令
                    if code in self.position_records:
                        del self.position_records[code]
                    return
                self.retry_counts[code] += 1
            
            # 重新获取最新价格
            current_price = self.get_current_price(code)
            if current_price:
                time_str = datetime.now().strftime('%H:%M:%S')
                if code in self.trade_instructions:
                    instruction = self.trade_instructions[code]
                    if instruction['type'] == 'sell':
                        # 卖出重试，降价重试
                        new_price = current_price - 0.40
                        self.execute_sell(code, new_price, time_str)
                    else:
                        # 买入重试，加价重试
                        new_price = current_price + 0.40
                        self.execute_buy(code, new_price, time_str)
                        
        except Exception as e:
            self.add_record(f"重试委托失败 {code}: {str(e)}")

    def test_order_functions(self):
        """测试下单查询功能"""
        try:
            self.add_record("开始测试下单查询功能...")
            test_code = "127055.SZ"  # 使用大写的SZ
            test_records = []  # 记录测试过程中产生的委托号
            
            # 先测试连接
            test_request = {'type': 'query_asset'}
            test_response = self.send_request(test_request)
            if test_response['status'] != 'success':
                self.add_record("无法连接到委托服务器，请确保委托查询撤单程序已启动")
                return
            
            try:
                # 1. 测试买入委托
                price = 163.0
                time_str = datetime.now().strftime('%H:%M:%S')
                
                self.add_record(f"测试买入委托 {test_code}")
                request = {
                    'type': 'place_order',
                    'params': {
                        'stock_code': test_code,
                        'direction': STOCK_BUY,
                        'volume': 10,  # 测试用小数量
                        'price_type': FIX_PRICE,
                        'price': price
                    }
                }
                response = self.send_request(request)
                
                if response['status'] == 'success':
                    order_id = response['data']['order_id']
                    test_records.append(order_id)
                    self.add_record(f"测试买入委托已提交，委托号: {order_id}")
                
                # 等待3秒以确保买入委托请求完成
                self.add_record("等待3秒...")
                time.sleep(3)
                
                # 2. 查询委托状态
                request = {'type': 'query_orders'}
                response = self.send_request(request)
                
                if response['status'] == 'success':
                    self.add_record("委托查询成功:")
                    for order in response['data']:
                        status_map = {
                            48: "未报", 49: "待报", 50: "已报", 51: "已报待撤",
                            52: "部成待撤", 53: "部撤", 54: "已撤", 55: "部成",
                            56: "已成", 57: "废单", 255: "未知"
                        }
                        status = status_map.get(order['order_status'], "未知")
                        self.add_record(f"委托号: {order['order_id']}, 状态: {status}")
                
                # 3. 测试查询资产
                asset_request = {'type': 'query_asset'}
                asset_response = self.send_request(asset_request)
                
                if asset_response['status'] == 'success':
                    self.add_record("资产查询成功:")
                    self.add_record(f"可用资金: {asset_response['data']['cash']:,.2f}")
                
                # 4. 测试查询持仓
                position_request = {'type': 'query_positions'}
                position_response = self.send_request(position_request)
                
                if position_response['status'] == 'success':
                    self.add_record("持仓查询成功:")
                    for pos in position_response['data']:
                        self.add_record(f"股票: {pos['stock_code']}, 数量: {pos['volume']}")
                
            finally:
                # 清理测试产生的委托
                self.add_record("开始清理测试委托...")
                
                # 撤销所有测试委托
                for order_id in test_records:
                    cancel_request = {
                        'type': 'cancel_order',
                        'params': {
                            'order_id': order_id
                        }
                    }
                    cancel_response = self.send_request(cancel_request)
                    if cancel_response['status'] == 'success':
                        self.add_record(f"已撤销测试委托 {order_id}")
                
                # 从持仓记录中移除测试股票
                if test_code in self.position_records:
                    del self.position_records[test_code]
                    self.update_position_list()
                    self.save_trading_data()
                
                self.add_record("测试清理完成")
            
            self.add_record("测试功能执行完成")
            
        except Exception as e:
            self.add_record(f"测试过程出错: {str(e)}")
            # 确保即使出错也尝试清理
            if test_code in self.position_records:
                del self.position_records[test_code]
                self.update_position_list()
                self.save_trading_data()

    def force_check_signals(self):
        """强制检查买卖点"""
        try:
            if not hasattr(self, 'stock_codes') or not self.stock_codes:
                messagebox.showwarning("警告", "请先加载股票代码")
                return

            # 在后台线程中执行检查，避免阻塞UI
            def check_thread():
                try:
                    # 获取当前时间
                    now = datetime.now()
                    current_time = now.time()

                    # 移除14:30限制，全天可以买入

                    # 判断当前时间状态
                    is_trading_time = ('09:30:00' <= current_time.strftime('%H:%M:%S') <= '11:30:00') or \
                                    ('13:00:00' <= current_time.strftime('%H:%M:%S') <= '15:00:00')

                    time_status = "交易时间" if is_trading_time else "非交易时间"
                    kline_processing = "使用最新K线（包括正在形成的K线）"

                    # 更新UI提示
                    self.root.after(0, lambda: self.update_realtime_hint("正在强制检查买卖点..."))
                    self.root.after(0, lambda: self.add_record(f"🔍 手动触发买卖点检查"))
                    self.root.after(0, lambda: self.add_record(f"当前时间: {current_time.strftime('%H:%M:%S')} ({time_status})"))
                    self.root.after(0, lambda: self.add_record(f"K线处理方式: {kline_processing}"))

                    # 执行买卖点检查
                    self.root.after(0, lambda: self.add_record("开始检查所有股票的买卖信号..."))

                    # 调用现有的信号检查函数
                    self.check_trading_signals()

                    # 检查完成提示
                    total_codes = len(self.bond_codes) + len(self.etf_codes) if hasattr(self, 'bond_codes') and hasattr(self, 'etf_codes') else len(self.stock_codes)

                    success_msg = f"买卖点检查完成！\n"
                    success_msg += f"检查股票数量: {total_codes}\n"
                    success_msg += f"当前时间: {current_time.strftime('%H:%M:%S')}\n"
                    success_msg += f"时间状态: {time_status}\n"
                    success_msg += f"K线处理: {kline_processing}"

                    # 移除14:30限制提示

                    self.root.after(0, lambda: self.add_record("✅ 强制检查买卖点完成"))
                    self.root.after(0, lambda: self.update_realtime_hint("买卖点检查完成"))
                    self.root.after(0, lambda: messagebox.showinfo("检查完成", success_msg))

                except Exception as e:
                    error_msg = f"强制检查买卖点失败: {str(e)}"
                    self.root.after(0, lambda: self.add_record(f"❌ {error_msg}"))
                    self.root.after(0, lambda: self.update_realtime_hint("买卖点检查失败"))
                    self.root.after(0, lambda: messagebox.showerror("检查失败", error_msg))

            # 启动检查线程
            check_thread_obj = threading.Thread(target=check_thread)
            check_thread_obj.daemon = True
            check_thread_obj.start()

        except Exception as e:
            messagebox.showerror("错误", f"启动买卖点检查失败: {str(e)}")
            self.add_record(f"启动买卖点检查失败: {str(e)}")

    def run(self):
        """运行程序"""
        try:
            self.root.mainloop()
        finally:
            # 程序关闭时保存交易数据
            self.save_trading_data()

    def cancel_order(self, code, order_id):
        """撤单操作"""
        try:
            request = {
                'type': 'cancel_order',
                'params': {
                    'order_id': order_id
                }
            }
            
            response = self.send_request(request)
            if response['status'] == 'success':
                self.add_record(f"撤单请求已提交 - {code} 委托号: {order_id}")
                self.add_record(f"撤单序号: {response['data'].get('cancel_seq')}")
                self.add_record(f"柜台合同编号: {response['data'].get('order_sysid')}")
                return True
            else:
                self.add_record(f"撤单失败 - {code} 委托号: {order_id}: {response.get('message', '未知错误')}")
                return False
            
        except Exception as e:
            self.add_record(f"撤单异常 - {code} 委托号: {order_id}: {str(e)}")
            return False

    def show_position_menu(self, event):
        """显示持仓右键菜单"""
        try:
            # 获取点击位置对应的项
            index = self.position_listbox.nearest(event.y)
            if index >= 0:
                # 选中该项
                self.position_listbox.selection_clear(0, tk.END)
                self.position_listbox.selection_set(index)
                # 显示菜单
                self.position_menu.post(event.x_root, event.y_root)
                #self.add_record(f"[调试] 显示持仓右键菜单，选中项目索引: {index}")
            else:
                self.add_record(f"[调试] 右键点击位置无效，索引: {index}")
        except Exception as e:
            self.add_record(f"显示菜单失败: {str(e)}")



    def clear_selected_position(self):
        """清除选中的持仓记录"""
        self.add_record(f"[调试] 开始执行清除持仓操作")

        selection = self.position_listbox.curselection()
        if not selection:
            self.add_record(f"[调试] 未选择任何持仓项目")
            messagebox.showinfo("提示", "请先选择一个持仓")
            return

        self.add_record(f"[调试] 选中的项目索引: {selection}")
        position_text = self.position_listbox.get(selection[0])
        self.add_record(f"[调试] 选中的持仓文本: {position_text}")

        try:
            # 解析股票代码，处理[虚拟]前缀
            first_part = position_text.split()[0]
            if first_part.startswith('[虚拟]'):
                code = first_part[4:]  # 移除"[虚拟]"前缀
            else:
                code = first_part
            self.add_record(f"[调试] 解析出的股票代码: {code} (原始文本: {first_part})")
        except Exception as e:
            self.add_record(f"[调试] 解析股票代码失败: {str(e)}")
            return

        self.add_record(f"[调试] 当前持仓记录中的股票: {list(self.position_records.keys())}")

        # 确认是否清除
        if messagebox.askyesno("确认", f"确定要清除 {code} 的持仓记录吗？\n注意：这只是清除记录，不会执行实际的卖出操作。"):
            self.add_record(f"[调试] 用户确认清除 {code}")
            if code in self.position_records:
                del self.position_records[code]
                # 记录用户手动清除的股票，防止重新加载
                self.manually_cleared_positions.add(code)
                self.add_record(f"✅ 已清除 {code} 的持仓记录")
                self.add_record(f"[调试] 清除后剩余持仓: {list(self.position_records.keys())}")
                self.add_record(f"[调试] 已记录手动清除的股票: {code}")
                # 更新界面和保存数据
                self.update_position_list()
                self.update_trade_summary()
                self.save_trading_data()
                self.add_record(f"[调试] 已保存数据并更新界面")
            else:
                self.add_record(f"[调试] 错误：{code} 不在持仓记录中")
        else:
            self.add_record(f"[调试] 用户取消清除操作")

    def clear_pending_orders(self):
        """清理所有未完成的委托"""
        try:
            # 获取当天日期
            today = datetime.now().strftime('%Y%m%d')
            
            # 获取所有委托，添加日期参数
            request = {
                'type': 'query_orders',
                'params': {
                    'start_date': today,
                    'end_date': today
                }
            }
            response = self.send_request(request)
            
            if response['status'] != 'success':
                self.add_record(f"查询委托失败: {response.get('message', '未知错误')}")
                return
            
            # 统计需要清理的委托
            pending_orders = [order for order in response['data']
                            if order['order_status'] in [48, 49, 50, 51, 52, 55]]  # 只处理未完成的委托
            
            if not pending_orders:
                self.add_record("没有需要清理的未完成委托")
                return
            
            self.add_record(f"开始清理 {len(pending_orders)} 个未完成委托...")
            
            # 撤销未完成的委托
            for order in pending_orders:
                cancel_request = {
                    'type': 'cancel_order',
                    'params': {
                        'order_id': order['order_id']
                    }
                }
                cancel_response = self.send_request(cancel_request)
                
                if cancel_response['status'] == 'success':
                    self.add_record(f"已撤销委托 - {order['stock_code']} 委托号:{order['order_id']}")
            
            # 清空重试计数器
            if hasattr(self, 'retry_counts'):
                self.retry_counts.clear()
            
            # 清空当日的持仓记录
            self.position_records.clear()
            self.update_position_list()
            self.save_trading_data()
            self.add_record("已清空当日持仓记录")
            
            self.add_record("清理完成")
            
        except Exception as e:
            self.add_record(f"清理委托时出错: {str(e)}")

    def check_connection(self):
        """检查与服务器的连接状态"""
        try:
            test_request = {'type': 'query_asset'}
            response = self.send_request(test_request)
            
            if response['status'] == 'success':
                if not self.is_connected:
                    # 先更新状态标签
                    if self.connection_status_label:
                        self.connection_status_label.config(text="连接状态: 已连接", foreground="green")
                        self.root.update()
                    # 再添加记录
                    self.add_record("已连接到服务器")
                self.is_connected = True
            else:
                if self.is_connected:
                    # 先更新状态标签
                    if self.connection_status_label:
                        self.connection_status_label.config(text="连接状态: 未连接", foreground="red")
                        self.root.update()
                    # 再添加记录
                    self.add_record("与服务器断开连接")
                self.is_connected = False
                
        except Exception as e:
            if self.is_connected:
                # 先更新状态标签
                if self.connection_status_label:
                    self.connection_status_label.config(text="连接状态: 未连接", foreground="red")
                    self.root.update()
                # 再添加记录
                self.add_record(f"与服务器断开连接: {str(e)}")
            self.is_connected = False
        
        # 每5秒检查一次
        self.root.after(5000, self.check_connection)

    def update_realtime_hint(self, text):
        """更新实时提示标签"""
        try:
            # 更新标签文本
            self.realtime_hint_label.config(text=f"实时提示: {text}")
            
            # 根据提示类型设置颜色
            if "警告" in text:
                self.realtime_hint_label.config(foreground="red")
            elif "卖出信号" in text:  # 卖出信号改为绿色
                self.realtime_hint_label.config(foreground="green")
            elif "买入信号" in text:  # 买入信号改为橙色
                self.realtime_hint_label.config(foreground="orange")
            else:
                self.realtime_hint_label.config(foreground="black")
        except Exception as e:
            print(f"更新实时提示失败: {str(e)}")



    def check_consecutive_bearish_candles(self, df, periods=2):
        """
        检查连续阴线
        Args:
            df: K线数据DataFrame
            periods: 连续周期数，默认2
        Returns:
            bool: 是否连续出现指定周期数的阴线
        """
        try:
            if df is None or len(df) < periods:
                return False

            # 获取最近的periods个K线
            recent_candles = df.tail(periods)

            # 检查每个K线是否为阴线（收盘价 < 开盘价）
            bearish_count = 0
            for _, candle in recent_candles.iterrows():
                if candle['close'] < candle['open']:
                    bearish_count += 1
                else:
                    # 如果有一个不是阴线，就不满足连续条件
                    break

            # 检查是否连续periods个都是阴线
            is_consecutive_bearish = bearish_count == periods

            return is_consecutive_bearish

        except Exception as e:
            print(f"检查连续阴线失败: {str(e)}")
            return False

    def check_cross_signals(self, df, current_price, code):
        """
        检查交叉信号 - 当前策略已改为成交额策略，此函数保留但简化
        现在主要用于基础数据检查
        """
        try:
            # 基础数据检查
            if df is None or len(df) < 2:
                return False, None, None, None

            # 当前策略使用成交额判断，不再依赖技术指标交叉
            # 返回基础信息供其他逻辑使用
            return False, None, current_price, 'amount_strategy'

        except Exception as e:
            print(f"检查 {code} 信号失败: {str(e)}")
            return False, None, None, None












    def start_price_refresh(self):
        """启动持仓价格定时刷新"""
        try:
            # 静默刷新持仓价格（移除调试信息）
            pass

            # 更新持仓列表
            self.update_position_list()

            # 检查是否需要保存每日统计数据（15:10）
            current_time = datetime.now()
            current_time_obj = current_time.time()
            if (current_time_obj >= datetime.strptime('15:10:00', '%H:%M:%S').time() and
                current_time_obj <= datetime.strptime('15:11:00', '%H:%M:%S').time() and
                not self.daily_stats_saved):
                self.save_daily_stats()
            
            # 检查持仓盈利情况
            if self.position_records:
                for code, info in list(self.position_records.items()):
                    current_price = self.get_current_price(code)
                    if current_price:
                        buy_price = info['buy_price']

            # 保存交易数据（如果有变化）
            self.save_trading_data()

            # 更新交易汇总
            self.update_trade_summary()

            # 每10秒刷新一次
            self.root.after(10000, self.start_price_refresh)
        except Exception as e:
            self.add_record(f"刷新持仓价格时出错: {str(e)}")
            # 出错后仍然继续定时刷新
            self.root.after(10000, self.start_price_refresh)

    def save_daily_stats(self):
        """保存每日统计数据"""
        try:
            # 获取当前日期
            current_date = datetime.now().strftime('%Y%m%d')
            
            # 如果日期变化，重置统计数据和保存状态
            if current_date != self.current_date:
                self.max_positions_today = 0
                self.max_profit_today = 0.0
                self.max_loss_today = 0.0
                self.daily_max_float_profit = 0.0
                self.daily_max_float_loss = 0.0
                self.daily_total_profit = 0.0
                self.single_stock_max_profit = 0.0  # 重置单只股票最大浮盈
                self.single_stock_max_loss = 0.0    # 重置单只股票最大浮亏
                self.daily_stats_saved = False
                self.current_date = current_date
                return
            
            # 如果已经保存过今天的统计，则不重复保存
            if self.daily_stats_saved:
                return
            
            # 准备统计数据
            daily_stats = {
                'date': current_date,
                'max_positions': self.max_positions_today,
                'max_profit': round(self.max_profit_today, 2),
                'max_loss': round(self.max_loss_today, 2),
                'single_stock_max_profit': round(self.single_stock_max_profit, 2),
                'single_stock_max_loss': round(self.single_stock_max_loss, 2),
                'timestamp': datetime.now().strftime('%H:%M:%S')
            }
            
            # 加载现有的交易历史数据
            trading_data = {}
            if os.path.exists('trading_history.json'):
                try:
                    with open('trading_history.json', 'r', encoding='utf-8') as f:
                        trading_data = json.load(f)
                except:
                    pass
            
            # 确保daily_stats键存在
            if 'daily_stats' not in trading_data:
                trading_data['daily_stats'] = []
            
            # 添加今日统计数据
            trading_data['daily_stats'].append(daily_stats)
            
            # 保存更新后的数据
            with open('trading_history.json', 'w', encoding='utf-8') as f:
                json.dump(trading_data, f, ensure_ascii=False, indent=2)
            
            self.add_record(f"已保存今日统计数据 - 最大持仓:{self.max_positions_today} "
                          f"最大盈利:{self.max_profit_today:.2f} "
                          f"最大亏损:{self.max_loss_today:.2f} "
                          f"单股最大浮盈:{self.single_stock_max_profit:.2f} "
                          f"单股最大浮亏:{self.single_stock_max_loss:.2f}")
            
            # 标记为已保存
            self.daily_stats_saved = True
            
        except Exception as e:
            self.add_record(f"保存每日统计数据失败: {str(e)}")
            print(f"保存每日统计数据失败: {str(e)}")  # 添加控制台输出以便调试

    def load_state(self):
        """加载状态信息"""
        try:
            state_file = f"交易状态_{self.today}.json"
            if os.path.exists(state_file):
                with open(state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                    

                    # 加载止盈监控记录
                    self.profit_taking_monitor = state.get('profit_taking_monitor', {})
                    
                    # 加载统计数据
                    self.max_positions_today = state.get('max_positions_today', 0)
                    self.max_profit_today = state.get('max_profit_today', 0.0)
                    self.max_loss_today = state.get('max_loss_today', 0.0)
                    self.daily_max_float_profit = state.get('daily_max_float_profit', 0.0)
                    self.daily_max_float_loss = state.get('daily_max_float_loss', 0.0)
                    self.daily_total_profit = state.get('daily_total_profit', 0.0)
                    self.single_stock_max_profit = state.get('single_stock_max_profit', 0.0)
                    self.single_stock_max_loss = state.get('single_stock_max_loss', 0.0)
                    
                    print(f"已加载状态信息")
                    print(f"最大持仓: {self.max_positions_today}, 当日最大浮盈: {self.daily_max_float_profit:.2f}")
            else:
                print("未找到状态文件，使用初始值")
        except Exception as e:
            print(f"加载状态信息失败: {str(e)}")
    
    def save_state(self):
        """保存状态信息"""
        try:
            state = {
                # 监控状态
                'profit_taking_monitor': self.profit_taking_monitor,
                
                # 统计数据
                'max_positions_today': self.max_positions_today,
                'max_profit_today': self.max_profit_today,
                'max_loss_today': self.max_loss_today,
                'daily_max_float_profit': self.daily_max_float_profit,
                'daily_max_float_loss': self.daily_max_float_loss,
                'daily_total_profit': self.daily_total_profit,
                'single_stock_max_profit': self.single_stock_max_profit,
                'single_stock_max_loss': self.single_stock_max_loss
            }
            
            with open(f"交易状态_{self.today}.json", 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存状态信息失败: {str(e)}")
            self.add_record(f"保存状态信息失败: {str(e)}")

    def show_trading_statistics(self):
        """显示当日交易统计报表"""
        try:
            # 创建统计窗口
            stats_window = tk.Toplevel(self.root)
            stats_window.title("当日交易统计报表")
            stats_window.geometry("800x600")

            # 创建主框架
            main_frame = ttk.Frame(stats_window)
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # 创建标题
            title_label = ttk.Label(main_frame, text=f"交易统计报表 - {datetime.now().strftime('%Y年%m月%d日')}",
                                  font=("Arial", 14, "bold"))
            title_label.pack(pady=(0, 10))

            # 创建滚动框架
            canvas = tk.Canvas(main_frame)
            scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # 计算统计数据
            stats_data = self.calculate_trading_statistics()

            # 显示总体统计
            self.create_overall_stats_section(scrollable_frame, stats_data)

            # 显示分股票统计
            self.create_stock_stats_section(scrollable_frame, stats_data)

            # 布局滚动组件
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # 绑定鼠标滚轮事件
            def _on_mousewheel(event):
                canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

            # 添加导出按钮
            export_frame = ttk.Frame(main_frame)
            export_frame.pack(fill="x", pady=(10, 0))

            export_button = ttk.Button(export_frame, text="导出统计报表",
                                     command=lambda: self.export_statistics_report(stats_data))
            export_button.pack(side="right")

        except Exception as e:
            messagebox.showerror("错误", f"显示统计报表失败: {str(e)}")
            print(f"显示统计报表失败: {str(e)}")

    def calculate_trading_statistics(self):
        """计算交易统计数据"""
        try:
            # 按股票分组统计
            stock_stats = {}

            for record in self.trade_records:
                code = record['code']
                profit = record['profit']

                if code not in stock_stats:
                    stock_stats[code] = {
                        'total_trades': 0,
                        'profit_trades': 0,
                        'loss_trades': 0,
                        'total_profit': 0.0,
                        'total_loss': 0.0,
                        'max_profit': 0.0,
                        'max_loss': 0.0,
                        'avg_profit': 0.0,
                        'win_rate': 0.0,
                        'trades': []
                    }

                stats = stock_stats[code]
                stats['total_trades'] += 1
                stats['trades'].append(record)

                if profit > 0:
                    stats['profit_trades'] += 1
                    stats['total_profit'] += profit
                    stats['max_profit'] = max(stats['max_profit'], profit)
                else:
                    stats['loss_trades'] += 1
                    stats['total_loss'] += abs(profit)
                    stats['max_loss'] = max(stats['max_loss'], abs(profit))

            # 计算衍生指标
            for code, stats in stock_stats.items():
                if stats['total_trades'] > 0:
                    stats['win_rate'] = (stats['profit_trades'] / stats['total_trades']) * 100
                    net_profit = stats['total_profit'] - stats['total_loss']
                    stats['avg_profit'] = net_profit / stats['total_trades']

            # 计算总体统计
            total_trades = len(self.trade_records)
            total_profit_trades = sum(1 for r in self.trade_records if r['profit'] > 0)
            total_loss_trades = sum(1 for r in self.trade_records if r['profit'] <= 0)
            total_profit = sum(r['profit'] for r in self.trade_records if r['profit'] > 0)
            total_loss = sum(abs(r['profit']) for r in self.trade_records if r['profit'] <= 0)
            net_profit = sum(r['profit'] for r in self.trade_records)

            overall_stats = {
                'total_trades': total_trades,
                'profit_trades': total_profit_trades,
                'loss_trades': total_loss_trades,
                'total_profit': total_profit,
                'total_loss': total_loss,
                'net_profit': net_profit,
                'win_rate': (total_profit_trades / total_trades * 100) if total_trades > 0 else 0,
                'avg_profit_per_trade': net_profit / total_trades if total_trades > 0 else 0,
                'max_profit': max([r['profit'] for r in self.trade_records], default=0),
                'max_loss': max([abs(r['profit']) for r in self.trade_records if r['profit'] < 0], default=0),
                'current_positions': len(self.position_records),
                'max_positions_today': self.max_positions_today,
                'daily_max_float_profit': self.daily_max_float_profit,
                'daily_max_float_loss': self.daily_max_float_loss
            }

            return {
                'overall': overall_stats,
                'by_stock': stock_stats
            }

        except Exception as e:
            print(f"计算统计数据失败: {str(e)}")
            return {'overall': {}, 'by_stock': {}}

    def create_overall_stats_section(self, parent, stats_data):
        """创建总体统计部分"""
        overall = stats_data['overall']

        # 总体统计框架
        overall_frame = ttk.LabelFrame(parent, text="总体统计", padding=10)
        overall_frame.pack(fill="x", pady=(0, 10))

        # 创建两列布局
        left_frame = ttk.Frame(overall_frame)
        left_frame.pack(side="left", fill="both", expand=True)

        right_frame = ttk.Frame(overall_frame)
        right_frame.pack(side="right", fill="both", expand=True)

        # 左列统计
        left_stats = [
            ("总交易次数", f"{overall.get('total_trades', 0)}次"),
            ("盈利次数", f"{overall.get('profit_trades', 0)}次"),
            ("亏损次数", f"{overall.get('loss_trades', 0)}次"),
            ("胜率", f"{overall.get('win_rate', 0):.1f}%"),
            ("平均每笔盈亏", f"{overall.get('avg_profit_per_trade', 0):.2f}元")
        ]

        for i, (label, value) in enumerate(left_stats):
            ttk.Label(left_frame, text=f"{label}:").grid(row=i, column=0, sticky="w", padx=(0, 10))
            ttk.Label(left_frame, text=value, font=("Arial", 9, "bold")).grid(row=i, column=1, sticky="w")

        # 右列统计
        right_stats = [
            ("总盈利", f"{overall.get('total_profit', 0):.2f}元"),
            ("总亏损", f"{overall.get('total_loss', 0):.2f}元"),
            ("净盈亏", f"{overall.get('net_profit', 0):.2f}元"),
            ("单笔最大盈利", f"{overall.get('max_profit', 0):.2f}元"),
            ("单笔最大亏损", f"{overall.get('max_loss', 0):.2f}元")
        ]

        for i, (label, value) in enumerate(right_stats):
            ttk.Label(right_frame, text=f"{label}:").grid(row=i, column=0, sticky="w", padx=(0, 10))
            color = "green" if "盈利" in label or ("净盈亏" in label and overall.get('net_profit', 0) > 0) else "red" if "亏损" in label or ("净盈亏" in label and overall.get('net_profit', 0) < 0) else "black"
            ttk.Label(right_frame, text=value, font=("Arial", 9, "bold"), foreground=color).grid(row=i, column=1, sticky="w")

        # 持仓统计
        position_frame = ttk.LabelFrame(parent, text="持仓统计", padding=10)
        position_frame.pack(fill="x", pady=(0, 10))

        position_stats = [
            ("当前持仓数", f"{overall.get('current_positions', 0)}只"),
            ("今日最大持仓数", f"{overall.get('max_positions_today', 0)}只"),
            ("今日最大浮盈", f"{overall.get('daily_max_float_profit', 0):.2f}元"),
            ("今日最大浮亏", f"{overall.get('daily_max_float_loss', 0):.2f}元")
        ]

        for i, (label, value) in enumerate(position_stats):
            row = i // 2
            col = (i % 2) * 2
            ttk.Label(position_frame, text=f"{label}:").grid(row=row, column=col, sticky="w", padx=(0, 10), pady=2)
            ttk.Label(position_frame, text=value, font=("Arial", 9, "bold")).grid(row=row, column=col+1, sticky="w", padx=(0, 20), pady=2)

    def create_stock_stats_section(self, parent, stats_data):
        """创建分股票统计部分"""
        by_stock = stats_data['by_stock']

        if not by_stock:
            no_data_frame = ttk.LabelFrame(parent, text="分股票统计", padding=10)
            no_data_frame.pack(fill="x", pady=(0, 10))
            ttk.Label(no_data_frame, text="暂无交易记录").pack()
            return

        # 分股票统计框架
        stock_frame = ttk.LabelFrame(parent, text="分股票统计", padding=10)
        stock_frame.pack(fill="both", expand=True, pady=(0, 10))

        # 创建表格
        columns = ("股票代码", "交易次数", "盈利次数", "亏损次数", "胜率", "净盈亏", "平均盈亏", "最大盈利", "最大亏损")

        tree = ttk.Treeview(stock_frame, columns=columns, show="headings", height=15)

        # 设置列标题和宽度
        column_widths = [80, 80, 80, 80, 80, 100, 100, 100, 100]
        for i, (col, width) in enumerate(zip(columns, column_widths)):
            tree.heading(col, text=col)
            tree.column(col, width=width, anchor="center")

        # 添加滚动条
        stock_scrollbar = ttk.Scrollbar(stock_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=stock_scrollbar.set)

        # 按净盈亏排序
        sorted_stocks = sorted(by_stock.items(),
                             key=lambda x: x[1]['total_profit'] - x[1]['total_loss'],
                             reverse=True)

        # 填充数据
        for code, stats in sorted_stocks:
            net_profit = stats['total_profit'] - stats['total_loss']
            values = (
                code,
                f"{stats['total_trades']}次",
                f"{stats['profit_trades']}次",
                f"{stats['loss_trades']}次",
                f"{stats['win_rate']:.1f}%",
                f"{net_profit:.2f}元",
                f"{stats['avg_profit']:.2f}元",
                f"{stats['max_profit']:.2f}元",
                f"{stats['max_loss']:.2f}元"
            )

            # 根据净盈亏设置行颜色
            item = tree.insert("", "end", values=values)
            if net_profit > 0:
                tree.set(item, "净盈亏", f"+{net_profit:.2f}元")
            elif net_profit < 0:
                tree.set(item, "净盈亏", f"{net_profit:.2f}元")

        # 布局
        tree.pack(side="left", fill="both", expand=True)
        stock_scrollbar.pack(side="right", fill="y")

        # 添加双击事件查看详细交易记录
        def on_double_click(event):
            item = tree.selection()[0]
            code = tree.item(item, "values")[0]
            self.show_stock_detail(code, by_stock[code])

        tree.bind("<Double-1>", on_double_click)

    def show_stock_detail(self, code, stock_stats):
        """显示单个股票的详细交易记录"""
        try:
            detail_window = tk.Toplevel(self.root)
            detail_window.title(f"{code} 详细交易记录")
            detail_window.geometry("900x500")

            # 创建主框架
            main_frame = ttk.Frame(detail_window)
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # 股票统计摘要
            summary_frame = ttk.LabelFrame(main_frame, text=f"{code} 交易摘要", padding=10)
            summary_frame.pack(fill="x", pady=(0, 10))

            net_profit = stock_stats['total_profit'] - stock_stats['total_loss']
            summary_text = (
                f"总交易: {stock_stats['total_trades']}次  "
                f"盈利: {stock_stats['profit_trades']}次  "
                f"亏损: {stock_stats['loss_trades']}次  "
                f"胜率: {stock_stats['win_rate']:.1f}%  "
                f"净盈亏: {net_profit:.2f}元"
            )
            ttk.Label(summary_frame, text=summary_text, font=("Arial", 10, "bold")).pack()

            # 详细交易记录表格
            detail_frame = ttk.LabelFrame(main_frame, text="详细交易记录", padding=10)
            detail_frame.pack(fill="both", expand=True)

            columns = ("买入时间", "卖出时间", "买入价", "卖出价", "数量", "盈亏", "盈亏率", "手续费")
            detail_tree = ttk.Treeview(detail_frame, columns=columns, show="headings")

            # 设置列标题和宽度
            detail_widths = [80, 80, 80, 80, 60, 80, 80, 80]
            for col, width in zip(columns, detail_widths):
                detail_tree.heading(col, text=col)
                detail_tree.column(col, width=width, anchor="center")

            # 添加滚动条
            detail_scrollbar = ttk.Scrollbar(detail_frame, orient="vertical", command=detail_tree.yview)
            detail_tree.configure(yscrollcommand=detail_scrollbar.set)

            # 填充交易记录
            for trade in stock_stats['trades']:
                values = (
                    trade['buy_time'],
                    trade['sell_time'],
                    f"{trade['buy_price']:.3f}",
                    f"{trade['sell_price']:.3f}",
                    trade['quantity'],
                    f"{trade['profit']:.2f}",
                    f"{trade['profit_percent']:.2f}%",
                    f"{trade['total_fee']:.2f}"
                )
                item = detail_tree.insert("", "end", values=values)

                # 根据盈亏设置颜色标记
                if trade['profit'] > 0:
                    detail_tree.set(item, "盈亏", f"+{trade['profit']:.2f}")

            # 布局
            detail_tree.pack(side="left", fill="both", expand=True)
            detail_scrollbar.pack(side="right", fill="y")

        except Exception as e:
            messagebox.showerror("错误", f"显示股票详细信息失败: {str(e)}")

    def export_statistics_report(self, stats_data):
        """导出统计报表到Excel文件"""
        try:
            from datetime import datetime
            import csv

            # 获取当前时间作为文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"交易统计报表_{timestamp}.csv"

            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)

                # 写入总体统计
                writer.writerow(['=== 总体统计 ==='])
                overall = stats_data['overall']
                writer.writerow(['指标', '数值'])
                writer.writerow(['总交易次数', f"{overall.get('total_trades', 0)}次"])
                writer.writerow(['盈利次数', f"{overall.get('profit_trades', 0)}次"])
                writer.writerow(['亏损次数', f"{overall.get('loss_trades', 0)}次"])
                writer.writerow(['胜率', f"{overall.get('win_rate', 0):.1f}%"])
                writer.writerow(['总盈利', f"{overall.get('total_profit', 0):.2f}元"])
                writer.writerow(['总亏损', f"{overall.get('total_loss', 0):.2f}元"])
                writer.writerow(['净盈亏', f"{overall.get('net_profit', 0):.2f}元"])
                writer.writerow(['平均每笔盈亏', f"{overall.get('avg_profit_per_trade', 0):.2f}元"])
                writer.writerow(['单笔最大盈利', f"{overall.get('max_profit', 0):.2f}元"])
                writer.writerow(['单笔最大亏损', f"{overall.get('max_loss', 0):.2f}元"])
                writer.writerow([])

                # 写入分股票统计
                writer.writerow(['=== 分股票统计 ==='])
                writer.writerow(['股票代码', '交易次数', '盈利次数', '亏损次数', '胜率(%)', '净盈亏(元)', '平均盈亏(元)', '最大盈利(元)', '最大亏损(元)'])

                by_stock = stats_data['by_stock']
                sorted_stocks = sorted(by_stock.items(),
                                     key=lambda x: x[1]['total_profit'] - x[1]['total_loss'],
                                     reverse=True)

                for code, stats in sorted_stocks:
                    net_profit = stats['total_profit'] - stats['total_loss']
                    writer.writerow([
                        code,
                        stats['total_trades'],
                        stats['profit_trades'],
                        stats['loss_trades'],
                        f"{stats['win_rate']:.1f}",
                        f"{net_profit:.2f}",
                        f"{stats['avg_profit']:.2f}",
                        f"{stats['max_profit']:.2f}",
                        f"{stats['max_loss']:.2f}"
                    ])

            messagebox.showinfo("成功", f"统计报表已导出至: {filename}")

        except Exception as e:
            messagebox.showerror("错误", f"导出统计报表失败: {str(e)}")

    def convert_xtquant_to_tdx(self, xtquant_code):
        """
        将xtquant格式代码转换为通达信格式

        Args:
            xtquant_code: xtquant格式代码，如 '110801.SH'

        Returns:
            str: 通达信格式代码，如 '1110801'
        """
        if not xtquant_code or '.' not in xtquant_code:
            return None

        code, market = xtquant_code.split('.')

        # 根据市场和代码前缀添加通达信前缀
        if market == 'SH':  # 上海证券交易所
            if code.startswith('6'):  # 上证A股
                return f"1{code}"
            elif code.startswith('5'):  # 上证ETF
                return f"1{code}"
            elif code.startswith('11'):  # 上证可转债
                return f"1{code}"
        elif market == 'SZ':  # 深圳证券交易所
            if code.startswith('0'):  # 深证主板
                return f"0{code}"
            elif code.startswith('3'):  # 创业板
                return f"0{code}"
            elif code.startswith('15'):  # 深证ETF
                return f"0{code}"
            elif code.startswith('12'):  # 深证可转债
                return f"0{code}"

        return None

    def cleanup_sector_files(self):
        """整理板块文件，清除处理失败的股票"""
        try:
            # 确认对话框
            result = messagebox.askyesno(
                "确认整理",
                "此操作将从板块文件中移除处理失败的股票代码。\n\n"
                "建议在执行前备份板块文件。\n\n"
                "是否继续？"
            )

            if not result:
                return

            # 创建进度窗口
            progress_window = tk.Toplevel(self.root)
            progress_window.title("整理板块文件")
            progress_window.geometry("500x300")
            progress_window.transient(self.root)
            progress_window.grab_set()

            # 创建进度显示
            progress_frame = ttk.Frame(progress_window)
            progress_frame.pack(fill="both", expand=True, padx=10, pady=10)

            progress_label = ttk.Label(progress_frame, text="正在整理板块文件...")
            progress_label.pack(pady=5)

            # 创建文本框显示处理过程
            text_frame = ttk.Frame(progress_frame)
            text_frame.pack(fill="both", expand=True, pady=5)

            text_widget = tk.Text(text_frame, wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            def log_message(message):
                text_widget.insert(tk.END, message + "\n")
                text_widget.see(tk.END)
                progress_window.update()

            # 开始整理
            log_message("开始整理板块文件...")

            # 获取失败的股票代码列表
            failed_codes = getattr(self, 'failed_codes', [])
            if not failed_codes:
                log_message("没有发现处理失败的股票代码")
                log_message("整理完成！")

                # 添加关闭按钮
                close_button = ttk.Button(progress_frame, text="关闭",
                                        command=progress_window.destroy)
                close_button.pack(pady=10)
                return

            log_message(f"发现 {len(failed_codes)} 个失败的股票代码:")
            for code in failed_codes:
                log_message(f"  - {code}")

            # 将失败代码转换为通达信格式，用于比较
            failed_tdx_codes = set()
            for code in failed_codes:
                tdx_code = self.convert_xtquant_to_tdx(code)
                if tdx_code:
                    failed_tdx_codes.add(tdx_code)
                    log_message(f"  转换: {code} -> {tdx_code}")

            log_message(f"转换后的通达信格式失败代码: {len(failed_tdx_codes)} 个")

            # 处理板块文件
            sector_files = ['kzz.blk', 'etf.blk']  # 可转债和ETF板块文件
            total_removed = 0

            for sector_file in sector_files:
                try:
                    if not os.path.exists(sector_file):
                        log_message(f"板块文件 {sector_file} 不存在，跳过")
                        continue

                    log_message(f"\n处理板块文件: {sector_file}")

                    # 读取原始文件
                    with open(sector_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()

                    original_count = len(lines)
                    log_message(f"  原始股票数量: {original_count}")

                    # 过滤失败的股票代码
                    filtered_lines = []
                    removed_count = 0

                    for line in lines:
                        code = line.strip()
                        if code and code not in failed_tdx_codes:
                            filtered_lines.append(line)
                        elif code in failed_tdx_codes:
                            removed_count += 1
                            log_message(f"  移除失败代码: {code}")

                    # 备份原文件
                    backup_file = f"{sector_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    import shutil
                    shutil.copy2(sector_file, backup_file)
                    log_message(f"  已备份至: {backup_file}")

                    # 写入过滤后的内容
                    with open(sector_file, 'w', encoding='utf-8') as f:
                        f.writelines(filtered_lines)

                    log_message(f"  移除股票数量: {removed_count}")
                    log_message(f"  剩余股票数量: {len(filtered_lines)}")
                    total_removed += removed_count

                except Exception as e:
                    log_message(f"  处理 {sector_file} 失败: {str(e)}")

            log_message(f"\n整理完成！")
            log_message(f"总共移除 {total_removed} 个失败的股票代码")
            log_message("建议重新加载股票代码以更新列表")

            # 清空失败代码列表
            self.failed_codes = []

            # 添加关闭和重新加载按钮
            button_frame = ttk.Frame(progress_frame)
            button_frame.pack(pady=10)

            reload_button = ttk.Button(button_frame, text="重新加载股票代码",
                                     command=lambda: [self.load_stock_codes(), progress_window.destroy()])
            reload_button.pack(side="left", padx=5)

            close_button = ttk.Button(button_frame, text="关闭",
                                    command=progress_window.destroy)
            close_button.pack(side="left", padx=5)

        except Exception as e:
            messagebox.showerror("错误", f"整理板块文件失败: {str(e)}")
            print(f"整理板块文件失败: {str(e)}")


if __name__ == "__main__":
    try:
        app = TimeSeriesViewer()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {str(e)}") 