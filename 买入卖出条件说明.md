# 可转债做T交易系统 - 买入卖出条件说明

## 更新后的买入卖出条件

### 买入条件

**1. CCI上穿-100买入信号（首次买入）**
- **条件1**: 前一根K线CCI ≤ -100，当前K线CCI > -100
- **条件2**: 收盘价 < EMA50 × 1.01（过滤条件）
- **执行**: 仅创建信号记录，不实际买入，等待EXP3突破信号

**2. 股价上穿EXP3买入信号（加仓买入或重新买回）**
- **条件**: 前一根K线收盘价 ≤ EXP3，当前K线收盘价 > EXP3
- **情况A - 正常加仓**: 已有CCI信号记录，买入2个单位（约12000元）
- **情况B - 重新买回**: 股票在暂时卖出等待重新买回列表中，按原数量重新买回

### 卖出条件

**1. 强制止损**
- **条件**: 亏损达到-200元
- **执行**: 立即全部卖出

**2. 分批止盈**
- **第一次止盈**: 盈利达到400元，卖出一个单位
- **第二次止盈**: 盈利达到700元，全部卖出
- **特殊情况**: 如果股票涨停，暂不执行止盈卖出

**3. 技术指标卖出（仅在盈利时）**
- **前提**: 必须当前盈利（profit_rate > 0）
- **第一次EXP3跌破**: 股价从EXP3之上跌破EXP3，卖出一半
- **收回监控**: 跌破后等待2个周期，如果股价收回EXP3之上则重置等待
- **第二次EXP3跌破**: 收回后再次跌破EXP3，卖出剩余全部
- **超时卖出**: 第二周期未收回EXP3，全部卖出剩余持仓

**4. 新增：暂时卖出机制（仅限亏损时）**
- **条件**: 实际买入后股价跌破EXP3 **且当前处于亏损状态**
- **执行**: 暂时全部卖出，股票进入"暂时卖出等待重新买回"状态
- **重新买回**: 当股价重新上穿EXP3时，按原数量重新买回
- **优先级**: 此机制优先于技术指标卖出条件
- **重要说明**: 盈利时不执行暂时卖出，由技术指标卖出机制处理

### 其他限制条件

**买入限制**
- 每只股票最多买入2次（包括首次买入和加仓买入）
- 已达到买入次数限制的股票不再触发加仓买入信号
- 获利黑名单中的股票不买入
- 9:30第一次检查跳过买入操作

**时间限制**
- 只在交易时间（9:30-11:30, 13:00-15:00）执行实际交易
- 非交易时间只显示信号，不执行操作

**监控频率**
- 每5分钟的第5秒进行一次买卖点判定
- 使用5分钟K线数据进行技术分析

### 新增功能说明

**暂时卖出等待重新买回机制**
1. **触发条件**: 仅当实际持仓股票处于亏损状态且跌破EXP3时触发
2. **盈利保护**: 盈利时不执行暂时卖出，由现有技术指标卖出机制处理
3. **数据一致性**: 使用与买入卖出判断相同的完整K线数据进行判断
4. **执行流程**: 暂时卖出全部持仓，股票信息保存在"暂时卖出等待重新买回"列表中
5. **界面显示**: 持仓列表中显示为"[暂时卖出]"状态，颜色为橙色/紫色/灰色
6. **重新买回**: 当股价重新上穿EXP3时，系统会按原数量重新买回
7. **状态恢复**: 重新买回后，股票恢复正常持仓状态
8. **统计显示**: 交易汇总中显示暂时卖出等待重新买回的股票数量

**显示说明**
- 正常持仓：红色（盈利）、绿色（亏损）、黑色（平衡）
- CCI信号记录：蓝色
- 暂时卖出等待重新买回：橙色（潜在盈利）、紫色（潜在亏损）、灰色（平衡）

这个机制可以帮助在股价短期回调时减少损失，并在股价重新上涨时重新参与。
