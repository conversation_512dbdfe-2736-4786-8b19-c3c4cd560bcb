#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查买入价格读取问题脚本
搜索所有直接读取buy_price字段的代码
"""

import os
import re

def check_file_for_buy_price_reads(file_path):
    """检查单个文件中的买入价格读取问题"""
    if not os.path.exists(file_path):
        return []
    
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # 检查可能的直接读取buy_price的模式
            patterns = [
                # 直接访问buy_price字段（字典方式）
                r"position_info\[['\"']buy_price['\"']\]",
                r"info\[['\"']buy_price['\"']\]",
                r"buy_info\[['\"']buy_price['\"']\]",
                
                # 直接访问quantity字段（字典方式）
                r"position_info\[['\"']quantity['\"']\]",
                r"info\[['\"']quantity['\"']\]",
                r"buy_info\[['\"']quantity['\"']\]",
            ]
            
            for pattern in patterns:
                if re.search(pattern, line_stripped):
                    # 排除一些已知的正常情况
                    if any(exclude in line_stripped for exclude in [
                        "# 兼容新旧格式",
                        "buy_record['buy_price']",  # buy_queue中的记录
                        "buy_record['quantity']",   # buy_queue中的记录
                        "earliest_buy['buy_price']", # 从buy_queue中获取
                        "earliest_buy['quantity']",  # 从buy_queue中获取
                        "trade['buy_price']",        # 交易记录
                        "trade['quantity']",         # 交易记录
                    ]):
                        continue
                    
                    issues.append({
                        'file': file_path,
                        'line': i,
                        'content': line_stripped,
                        'pattern': pattern
                    })
    
    except Exception as e:
        print(f"检查文件 {file_path} 时出错: {str(e)}")
    
    return issues

def main():
    """主函数"""
    print("🔍 检查买入价格读取问题...")
    
    # 需要检查的文件列表
    files_to_check = [
        "可转债做T交易-cci.py",
        "可转债做T交易-成交额.py"
    ]
    
    all_issues = []
    
    for file_path in files_to_check:
        print(f"\n📁 检查文件: {file_path}")
        issues = check_file_for_buy_price_reads(file_path)
        
        if issues:
            print(f"  ❌ 发现 {len(issues)} 个可能的问题:")
            for issue in issues:
                print(f"    行 {issue['line']}: {issue['content']}")
            all_issues.extend(issues)
        else:
            print(f"  ✅ 未发现买入价格读取问题")
    
    print(f"\n🏁 检查完成！")
    
    if all_issues:
        print(f"❌ 总共发现 {len(all_issues)} 个可能的问题需要修复")
        print("\n详细问题列表:")
        for issue in all_issues:
            print(f"  {issue['file']}:{issue['line']} - {issue['content']}")
        return False
    else:
        print("🎉 所有文件都已清理完毕，未发现买入价格读取问题！")
        return True

if __name__ == "__main__":
    main()
