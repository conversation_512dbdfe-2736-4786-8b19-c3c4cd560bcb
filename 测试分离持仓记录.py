#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试分离持仓记录功能
验证实际交易和虚拟交易持仓记录是否正确分离
"""

import json
import os
from datetime import datetime

def test_separated_position_files():
    """测试分离的持仓记录文件"""
    
    print("=== 测试分离持仓记录功能 ===")
    
    # 检查文件是否存在
    real_file = "ETF实际交易持仓记录.json"
    virtual_file = "ETF虚拟交易持仓记录.json"
    old_file = "ETF交易系统持仓记录.json"
    
    print(f"1. 检查文件存在性:")
    print(f"   实际交易持仓记录文件: {'存在' if os.path.exists(real_file) else '不存在'}")
    print(f"   虚拟交易持仓记录文件: {'存在' if os.path.exists(virtual_file) else '不存在'}")
    print(f"   旧统一持仓记录文件: {'存在' if os.path.exists(old_file) else '不存在'}")
    
    # 读取实际交易持仓记录
    if os.path.exists(real_file):
        try:
            with open(real_file, 'r', encoding='utf-8') as f:
                real_positions = json.load(f)
            print(f"\n2. 实际交易持仓记录:")
            print(f"   持仓数量: {len(real_positions)}")
            
            for code, position in real_positions.items():
                if 'buy_queue' in position and position['buy_queue']:
                    first_buy = position['buy_queue'][0]
                    is_real = first_buy.get('real_trade', not first_buy.get('virtual', True))
                    print(f"   {code}: 数量={position.get('total_quantity', 0)}, 实际交易={is_real}")
                    
        except Exception as e:
            print(f"   读取实际交易持仓记录失败: {e}")
    
    # 读取虚拟交易持仓记录
    if os.path.exists(virtual_file):
        try:
            with open(virtual_file, 'r', encoding='utf-8') as f:
                virtual_positions = json.load(f)
            print(f"\n3. 虚拟交易持仓记录:")
            print(f"   持仓数量: {len(virtual_positions)}")
            
            for code, position in virtual_positions.items():
                if 'buy_queue' in position and position['buy_queue']:
                    first_buy = position['buy_queue'][0]
                    is_virtual = first_buy.get('virtual', not first_buy.get('real_trade', False))
                    print(f"   {code}: 数量={position.get('total_quantity', 0)}, 虚拟交易={is_virtual}")
                    
        except Exception as e:
            print(f"   读取虚拟交易持仓记录失败: {e}")
    
    # 检查备份文件
    backup_files = [f for f in os.listdir('.') if f.startswith('ETF交易系统持仓记录.json.backup_')]
    if backup_files:
        print(f"\n4. 备份文件:")
        for backup_file in sorted(backup_files):
            print(f"   {backup_file}")
    
    print(f"\n=== 测试完成 ===")
    print(f"结论: {'成功实现分离持仓记录' if os.path.exists(real_file) and os.path.exists(virtual_file) and not os.path.exists(old_file) else '分离持仓记录未完全实现'}")

if __name__ == "__main__":
    test_separated_position_files()
