#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试15:01持仓盈亏汇总与持仓列表数据一致性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import json

def test_position_summary_consistency():
    """测试持仓盈亏汇总与持仓列表的数据一致性"""
    
    print("=== 15:01持仓盈亏汇总数据一致性测试 ===")
    print()
    
    print("修改内容说明:")
    print("1. 15:01更新现在使用与持仓列表相同的数据源和计算逻辑")
    print("2. 在实盘交易模式下，优先使用服务器返回的盈亏数据")
    print("3. 在虚拟交易模式下，使用本地计算的盈亏数据")
    print("4. 每个股票的盈亏显示都包含数据源标识 [服务器]/[虚拟]/[本地]")
    print("5. 汇总信息标题显示交易模式和数据源说明")
    print()
    
    print("修改后的数据流程:")
    print("┌─────────────────┐")
    print("│   持仓列表显示   │")
    print("└─────────────────┘")
    print("         │")
    print("         ▼")
    print("┌─────────────────┐    ┌─────────────────┐")
    print("│  实盘交易模式    │    │  虚拟交易模式    │")
    print("│ 优先使用服务器   │    │  使用本地计算    │")
    print("│ 数据计算盈亏     │    │  盈亏数据       │")
    print("└─────────────────┘    └─────────────────┘")
    print("         │                       │")
    print("         └───────────┬───────────┘")
    print("                     ▼")
    print("         ┌─────────────────┐")
    print("         │ 15:01盈亏汇总   │")
    print("         │ 使用相同逻辑    │")
    print("         └─────────────────┘")
    print()
    
    print("关键改进点:")
    print("✓ 统一数据源：15:01更新与持仓列表使用相同的数据源")
    print("✓ 统一计算逻辑：使用display_position相同的盈亏计算方法")
    print("✓ 数据源标识：每个股票显示数据来源 [服务器]/[虚拟]/[本地]")
    print("✓ 服务器数据优先：实盘模式下优先使用服务器市值和成本价")
    print("✓ 降级处理：服务器数据不完整时自动使用本地计算")
    print()
    
    print("预期效果:")
    print("- 实盘交易：15:01汇总显示 '[服务器] +123.45元'")
    print("- 持仓列表：显示 '[服务器]+123.45'")
    print("- 数据一致：两处显示的盈亏金额完全相同")
    print()
    
    print("示例输出格式:")
    print("持仓盈亏详情:")
    print("  159516.SZ: 盈利 +123.45元 (+2.34%) [服务器] [买入:100.123 现价:102.456 数量:1000]")
    print("  510880.SH: 亏损 -45.67元 (-1.23%) [服务器] [买入:98.765 现价:97.543 数量:500]")
    print("==================================================")
    print("持仓汇总 - 实际交易 (使用与持仓列表相同的数据源) (截至 15:01:15):")
    print("  总持仓数: 2")
    print("  盈利股票: 1 | 亏损股票: 1")
    print("  总成本: 149,505.00元")
    print("  总市值: 151,027.50元")
    print("  总盈亏: +1,522.50元 (+1.02%)")
    print("==================================================")
    print()
    
    return True

if __name__ == "__main__":
    test_position_summary_consistency()
