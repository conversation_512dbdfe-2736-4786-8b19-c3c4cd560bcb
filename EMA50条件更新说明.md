# EMA50买入条件更新说明

## 更新内容

在可转债做T交易系统中，为首次买入条件增加了一个新的过滤条件：**收盘价必须小于50周期EMA的1.01倍**。

## 修改的文件

- `可转债做T交易-cci.py`

## 具体修改

### 1. 指标计算部分 (calculate_indicators函数)

在原有的CCI和EXP3指标基础上，新增了EMA50指标的计算：

```python
# 计算EMA50指标
df['ema50'] = df['close'].ewm(span=50).mean()
```

### 2. 买入信号检查部分 (check_cross_signals函数)

修改了CCI上穿-100的首次买入条件，增加了EMA50过滤：

**原条件：**
- 前一根K线CCI ≤ -100
- 当前K线CCI > -100

**新条件：**
- 前一根K线CCI ≤ -100
- 当前K线CCI > -100
- **收盘价 < EMA50 × 1.01** (新增)

### 3. 分析输出部分 (print_cci_analysis函数)

在CCI分析结果中增加了EMA50条件的详细分析输出，包括：
- EMA50当前值
- 收盘价与EMA50×1.01的比较
- EMA50条件是否满足
- 综合买入权限判断

## 买入条件逻辑

### 首次买入信号（CCI上穿-100）

现在需要同时满足以下**三个条件**：

1. **CCI技术条件**：
   - 前一根5分钟K线的CCI值 ≤ -100
   - 当前5分钟K线的CCI值 > -100

2. **EMA50过滤条件**（新增）：
   - 当前K线收盘价 < EMA50 × 1.01

3. **持仓限制条件**：
   - 该股票当前没有持仓

### 加仓买入信号（股价上穿EXP3）

加仓买入条件保持不变：
- 前一根5分钟K线的收盘价 ≤ EXP3值
- 当前5分钟K线的收盘价 > EXP3值
- 该股票已有持仓

## 设计目的

增加EMA50过滤条件的目的是：

1. **避免在高位买入**：当股价远高于50周期均线时，可能处于相对高位，增加买入风险
2. **提高买入质量**：只在股价相对接近或略低于中期均线时买入，提高成功率
3. **风险控制**：通过技术面过滤，减少不利位置的买入

## 容错设计

- 如果EMA50数据不足（如新上市股票），系统会记录"EMA50数据不足"并跳过买入
- 1.01的倍数设置允许股价略高于EMA50，避免过于严格的限制

## 测试验证

通过测试脚本验证，新条件能够正确工作：
- 正确计算EMA50指标
- 准确判断收盘价与EMA50×1.01的关系
- 综合判断所有买入条件
- 提供详细的分析输出

## 影响评估

此更新将：
- **减少买入频率**：增加过滤条件后，符合买入条件的机会可能减少
- **提高买入质量**：避免在相对高位买入，可能提高盈利概率
- **保持系统稳定性**：不影响现有的卖出逻辑和其他功能
