#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试EMA50买入条件的脚本
"""

import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def calculate_indicators_test(df):
    """测试版本的指标计算函数"""
    try:
        # 计算CCI指标 (14周期)
        n = 14
        # 计算典型价格 TP = (HIGH + LOW + CLOSE) / 3
        df['tp'] = (df['high'] + df['low'] + df['close']) / 3

        # 计算TP的简单移动平均
        df['sma_tp'] = df['tp'].rolling(window=n).mean()

        # 计算平均绝对偏差 MD
        df['md'] = df['tp'].rolling(window=n).apply(lambda x: abs(x - x.mean()).mean())

        # 计算CCI = (TP - SMA_TP) / (0.015 * MD)，避免除零错误
        md_factor = 0.015 * df['md']
        df['cci'] = np.where(md_factor != 0, (df['tp'] - df['sma_tp']) / md_factor, 0)

        # 计算EXP3指标
        # EXP3 = REF(HHV(CLOSE,P),1) - M*REF(ATR,1)
        # 参数设置
        p = 20  # HHV周期
        m = 1.0  # ATR乘数
        atr_n = 14  # ATR周期

        # 计算ATR
        df['tr'] = np.maximum(
            np.maximum(df['high'] - df['low'],
                      abs(df['high'] - df['close'].shift(1))),
            abs(df['low'] - df['close'].shift(1))
        )
        df['atr'] = df['tr'].rolling(window=atr_n).mean()

        # 计算HHV(CLOSE,P)
        df['hhv_close'] = df['close'].rolling(window=p).max()

        # 计算EXP3 = REF(HHV(CLOSE,P),1) - M*REF(ATR,1)
        df['exp3'] = df['hhv_close'].shift(1) - m * df['atr'].shift(1)

        # 计算EMA50指标
        df['ema50'] = df['close'].ewm(span=50).mean()

        return df
    except Exception as e:
        print(f"计算指标异常: {str(e)}")
        return None

def test_buy_conditions():
    """测试买入条件"""
    print("=== 测试EMA50买入条件 ===\n")
    
    # 创建测试数据
    dates = pd.date_range(start='2024-01-01', periods=100, freq='5T')
    np.random.seed(42)  # 设置随机种子以获得可重复的结果
    
    # 生成模拟的股价数据
    base_price = 120.0
    price_changes = np.random.normal(0, 0.5, 100)
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] + change
        prices.append(max(new_price, 80.0))  # 确保价格不会太低
    
    # 创建OHLC数据
    df = pd.DataFrame({
        'time': dates,
        'open': prices,
        'high': [p + np.random.uniform(0, 1) for p in prices],
        'low': [p - np.random.uniform(0, 1) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000, 10000, 100),
        'amount': [p * v for p, v in zip(prices, np.random.randint(1000, 10000, 100))]
    })
    
    # 计算指标
    df = calculate_indicators_test(df)
    if df is None:
        print("指标计算失败")
        return
    
    # 测试最后几根K线的买入条件
    print("最后5根K线的指标数据:")
    print("-" * 80)
    for i in range(-5, 0):
        row = df.iloc[i]
        prev_row = df.iloc[i-1] if i > -len(df) else None
        
        print(f"K线 {i}: 时间={row['time'].strftime('%H:%M')}")
        print(f"  收盘价: {row['close']:.3f}")
        print(f"  CCI: {row['cci']:.2f}")
        print(f"  EMA50: {row['ema50']:.3f}")
        print(f"  EXP3: {row['exp3']:.3f}")
        
        if prev_row is not None:
            # 检查CCI上穿-100条件
            cci_cross = prev_row['cci'] <= -100 and row['cci'] > -100
            # 检查EMA50条件
            ema50_condition = row['close'] < row['ema50'] * 1.01
            
            print(f"  买入条件检查:")
            print(f"    前一CCI({prev_row['cci']:.2f}) <= -100: {'✅' if prev_row['cci'] <= -100 else '❌'}")
            print(f"    当前CCI({row['cci']:.2f}) > -100: {'✅' if row['cci'] > -100 else '❌'}")
            print(f"    CCI上穿-100: {'✅' if cci_cross else '❌'}")
            print(f"    收盘价({row['close']:.3f}) < EMA50*1.01({row['ema50']*1.01:.3f}): {'✅' if ema50_condition else '❌'}")
            print(f"    综合买入条件: {'✅ 满足' if cci_cross and ema50_condition else '❌ 不满足'}")
        
        print()

if __name__ == "__main__":
    test_buy_conditions()
