# 虚拟交易显示问题修复说明

## 问题描述

用户反映：在启用了交易的情况下，卖出仍然显示为"[虚拟]"，但持仓列表中显示的是实际持仓（服务器数据）。

## 问题根源分析

### 原始逻辑的问题
1. **买入时的标记**：买入记录中的`virtual`字段基于买入时的`trading_enabled`状态
2. **卖出时的判断**：卖出时仍然基于买入记录中的`virtual`字段来判断是否虚拟交易
3. **持仓来源多样化**：持仓可能来自：
   - 当前程序的买入
   - 手动交易
   - 其他交易程序
   - 历史持仓

### 具体问题场景
- 如果某笔买入是在交易禁用时进行的（virtual=true），即使后来启用了交易，卖出时仍会显示"[虚拟]"
- 对于通过其他途径获得的持仓，程序无法正确识别其实际交易状态

## 解决方案

### 核心思路
**智能判断交易模式**：无论本地记录如何，都优先查询服务器持仓来确定实际交易状态。

### 修改内容

#### 1. 普通卖出逻辑修改 (execute_sell_order方法)

**修改前**：
```python
# 基于买入记录中的virtual字段判断
is_virtual = earliest_buy.get('virtual', False)
if self.trading_enabled and not is_virtual and not self.is_virtual_trading():
    # 查询服务器持仓
```

**修改后**：
```python
# 智能判断：无论本地记录如何，都先查询服务器持仓
is_real_trade = False
if self.trading_enabled and not self.is_virtual_trading():
    # 查询服务器持仓
    if 服务器有持仓:
        is_real_trade = True
        self.add_record(f"服务器确认 {code} 实际持仓，按实际交易处理")
    else:
        self.add_record(f"服务器未找到实际持仓，按虚拟交易处理")
```

#### 2. 显示逻辑修改

**修改前**：
```python
virtual_tag = "[虚拟]" if is_virtual else ""  # 基于买入时状态
```

**修改后**：
```python
virtual_tag = "[虚拟]" if not is_real_trade else ""  # 基于服务器查询结果
```

#### 3. 交易记录修改

**修改前**：
```python
'virtual': is_virtual  # 基于买入时状态
```

**修改后**：
```python
'virtual': not is_real_trade  # 基于服务器查询结果
```

#### 4. 保护性卖出逻辑同步修改

对`execute_protective_sell`方法应用相同的智能判断逻辑。

## 修改效果

### 修改前
- 买入时是虚拟的，即使后来启用交易，卖出仍显示"[虚拟]"
- 无法正确处理通过其他途径获得的实际持仓

### 修改后
- 卖出时实时查询服务器持仓状态
- 如果服务器确实有持仓，按实际交易处理，显示无"[虚拟]"标签
- 如果服务器没有持仓，按虚拟交易处理，显示"[虚拟]"标签
- 正确处理各种来源的持仓

## 技术细节

### 查询逻辑
```python
if self.trading_enabled and not self.is_virtual_trading():
    request = {'type': 'query_positions'}
    response = self.send_request(request)
    
    if response['status'] == 'success':
        for position in response['data']:
            if position['stock_code'] == code:
                is_real_trade = True
                actual_volume = position['volume']
                break
```

### 容错处理
- 如果查询服务器失败，按虚拟交易处理
- 如果服务器持仓数量不足，自动调整卖出数量
- 保持原有的错误处理机制

## 预期结果

1. **准确的交易状态显示**：卖出时的"[虚拟]"标签准确反映当前的交易状态
2. **智能持仓识别**：能够正确识别各种来源的实际持仓
3. **一致的用户体验**：持仓列表和卖出记录的状态标识保持一致
4. **向后兼容**：不影响现有的虚拟交易功能

## 测试建议

1. 测试启用交易后的卖出操作
2. 测试手动买入后的程序卖出
3. 测试服务器查询失败的容错处理
4. 测试虚拟交易模式的正常功能
