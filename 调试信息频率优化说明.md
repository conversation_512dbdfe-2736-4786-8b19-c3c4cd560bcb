# 调试信息频率优化说明

## 问题描述

用户反馈调试信息刷新太频繁：
```
09:43:41 [159516.SZ] 使用服务器数据: 市值10137.60 - 成本9950.60 = 187.00
09:43:56 [159516.SZ] 服务器计算盈亏: 市值10128.00 - 成本9950.60 = 177.40
```

## 问题原因

虽然添加了缓存机制，但由于市值在实时变化（每15秒刷新一次），系统认为每次都是新数据，导致调试信息过于频繁。

## 解决方案

### 智能阈值缓存机制

将简单的数据变化检测改为**智能阈值缓存**，只有在满足以下条件之一时才记录调试信息：

1. **盈亏变化阈值**：盈亏变化超过 **10元**
2. **时间间隔阈值**：距离上次记录超过 **5分钟**
3. **首次记录**：该股票首次出现调试信息

### 实现逻辑

```python
# 检查是否需要记录日志
profit_change = abs(total_profit - last_profit)
time_elapsed = current_time - last_log_time

# 只有满足条件才记录
if profit_change >= 10 or time_elapsed >= 300 or code not in cache:
    # 记录调试信息
    self.add_record(f"[{code}] 使用服务器数据: 市值{market_value:.2f} - 成本{cost_value:.2f} = {total_profit:.2f}")
```

### 缓存数据结构

```python
self._last_server_data[code] = {
    'profit': total_profit,      # 上次记录的盈亏金额
    'log_time': current_time     # 上次记录的时间戳
}
```

## 优化效果

### 优化前：
```
09:43:41 [159516.SZ] 使用服务器数据: 市值10137.60 - 成本9950.60 = 187.00
09:43:56 [159516.SZ] 使用服务器数据: 市值10128.00 - 成本9950.60 = 177.40
09:44:11 [159516.SZ] 使用服务器数据: 市值10145.20 - 成本9950.60 = 194.60
09:44:26 [159516.SZ] 使用服务器数据: 市值10132.40 - 成本9950.60 = 181.80
...（每15秒一次）
```

### 优化后：
```
09:43:41 [159516.SZ] 使用服务器数据: 市值10137.60 - 成本9950.60 = 187.00
（盈亏变化小于10元，不显示）
（盈亏变化小于10元，不显示）
09:48:41 [159516.SZ] 使用服务器数据: 市值10245.20 - 成本9950.60 = 294.60  # 5分钟后自动显示
10:15:26 [159516.SZ] 使用服务器数据: 市值10050.60 - 成本9950.60 = 100.00  # 盈亏变化超过10元
```

## 应用范围

此优化应用于所有调试信息：

1. **服务器数据计算**：`[{code}] 使用服务器数据: 市值xxx - 成本xxx = xxx`
2. **服务器盈亏数据**：`[{code}] 使用服务器盈亏: xxx`
3. **本地计算数据**：`[{code}] 使用本地计算: xxx`
4. **虚拟交易数据**：`[{code}] 使用虚拟计算: xxx`

## 参数设置

- **盈亏变化阈值**：10元（可根据需要调整）
- **时间间隔阈值**：5分钟（300秒）
- **适用范围**：所有持仓的盈亏计算调试信息

## 优势

1. **减少信息冗余**：避免频繁显示相似的调试信息
2. **突出重要变化**：只在盈亏有显著变化时提醒用户
3. **保持信息完整性**：定期显示确保信息不会完全丢失
4. **提升用户体验**：减少日志噪音，便于查看重要信息

## 注意事项

- 首次启动或新增持仓时会立即显示调试信息
- 盈亏变化超过10元时会立即显示
- 即使变化很小，每5分钟也会显示一次确保信息更新
- 不影响实际的持仓计算和显示，只是减少调试日志的频率

这样既保留了调试信息的价值，又避免了过于频繁的刷新干扰。
