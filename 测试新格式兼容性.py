#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os

def test_position_format():
    """测试持仓记录格式兼容性"""
    print("🔍 测试持仓记录格式兼容性...")
    
    # 加载持仓记录
    position_file = "可转债放量方法持仓记录.json"
    if not os.path.exists(position_file):
        print("❌ 持仓记录文件不存在")
        return False
    
    try:
        with open(position_file, 'r', encoding='utf-8') as f:
            position_records = json.load(f)
        
        print(f"📊 总共 {len(position_records)} 条持仓记录")
        
        # 测试每条记录的格式
        all_new_format = True
        for code, info in position_records.items():
            if 'buy_queue' in info and info['buy_queue']:
                # 新格式
                buy_price = info['buy_queue'][0]['buy_price']
                total_quantity = info.get('total_quantity', 0)
                print(f"✅ {code}: 新格式 - 价格:{buy_price:.2f}, 数量:{total_quantity}")
                
                # 测试display_position函数需要的字段
                if 'buy_queue' in info and info['buy_queue']:
                    print(f"   ✓ buy_queue存在，长度: {len(info['buy_queue'])}")
                else:
                    print(f"   ❌ buy_queue缺失")
                    all_new_format = False
                    
            else:
                print(f"❌ {code}: 旧格式或格式异常")
                all_new_format = False
        
        if all_new_format:
            print("🎉 所有记录都是新格式！")
            return True
        else:
            print("⚠️ 存在旧格式记录")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def simulate_display_position(code, position_info):
    """模拟display_position函数的关键逻辑"""
    print(f"🧪 模拟 display_position({code})...")
    
    try:
        # 这是修复后的逻辑
        if 'buy_queue' in position_info and position_info['buy_queue']:
            buy_price = position_info['buy_queue'][0]['buy_price']
            print(f"   ✅ 成功获取买入价格: {buy_price}")
            return True
        else:
            print(f"   ❌ 错误: {code} 持仓记录格式异常，缺少buy_queue")
            return False
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🚀 开始测试新格式兼容性")
    print("=" * 50)
    
    # 测试1: 检查文件格式
    format_ok = test_position_format()
    
    if format_ok:
        print("\n" + "=" * 50)
        print("🧪 测试display_position函数兼容性")
        print("=" * 50)
        
        # 测试2: 模拟display_position函数
        with open("可转债放量方法持仓记录.json", 'r', encoding='utf-8') as f:
            position_records = json.load(f)
        
        success_count = 0
        for code, info in position_records.items():
            if simulate_display_position(code, info):
                success_count += 1
        
        print(f"\n📊 测试结果: {success_count}/{len(position_records)} 成功")
        
        if success_count == len(position_records):
            print("🎉 所有测试通过！新格式兼容性良好")
        else:
            print("⚠️ 部分测试失败，需要进一步修复")
    
    print("\n" + "=" * 50)
    print("✅ 测试完成")
    print("=" * 50)
