#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查旧格式写入代码脚本
搜索所有可能直接写入旧格式持仓记录的代码
"""

import os
import re

def check_file_for_old_format_writes(file_path):
    """检查单个文件中的旧格式写入代码"""
    if not os.path.exists(file_path):
        return []
    
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for i, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # 检查可能的旧格式写入模式
            patterns = [
                # 直接设置buy_price和quantity的字典创建
                r"position_records\[.*\]\s*=\s*\{[^}]*['\"]buy_price['\"].*['\"]quantity['\"]",
                r"position_records\[.*\]\s*=\s*\{[^}]*['\"]quantity['\"].*['\"]buy_price['\"]",
                
                # 直接设置buy_price字段
                r"position_records\[.*\]\[['\"]buy_price['\"]\]\s*=",
                r"position_info\[['\"]buy_price['\"]\]\s*=",
                r"buy_info\[['\"]buy_price['\"]\]\s*=",
                
                # 直接设置quantity字段
                r"position_records\[.*\]\[['\"]quantity['\"]\]\s*=",
                r"position_info\[['\"]quantity['\"]\]\s*=",
                r"buy_info\[['\"]quantity['\"]\]\s*=",
                
                # 创建包含buy_price和quantity但没有buy_queue的字典
                r"\{\s*[^}]*['\"]buy_price['\"].*['\"]quantity['\"][^}]*\}(?![^}]*['\"]buy_queue['\"])",
                r"\{\s*[^}]*['\"]quantity['\"].*['\"]buy_price['\"][^}]*\}(?![^}]*['\"]buy_queue['\"])",
            ]
            
            for pattern in patterns:
                if re.search(pattern, line_stripped, re.IGNORECASE):
                    # 排除一些已知的正常情况
                    if any(exclude in line_stripped for exclude in [
                        "# 旧格式兼容",
                        "# 兼容旧的结构", 
                        "trade['buy_price']",  # 交易记录中的字段
                        "buy_record['buy_price']",  # buy_queue中的记录
                        "buy_record['quantity']",   # buy_queue中的记录
                        "earliest_buy['buy_price']", # 从buy_queue中获取
                        "earliest_buy['quantity']",  # 从buy_queue中获取
                        "info.get('buy_price')",     # 读取操作
                        "position_info.get('buy_price')", # 读取操作
                        "buy_info.get('buy_price')",      # 读取操作
                    ]):
                        continue
                    
                    issues.append({
                        'file': file_path,
                        'line': i,
                        'content': line_stripped,
                        'pattern': pattern
                    })
    
    except Exception as e:
        print(f"检查文件 {file_path} 时出错: {str(e)}")
    
    return issues

def main():
    """主函数"""
    print("🔍 检查旧格式写入代码...")
    
    # 需要检查的文件列表
    files_to_check = [
        "自选股交易.py",
        "可转债做T交易-cci.py", 
        "可转债做T交易-成交额.py"
    ]
    
    all_issues = []
    
    for file_path in files_to_check:
        print(f"\n📁 检查文件: {file_path}")
        issues = check_file_for_old_format_writes(file_path)
        
        if issues:
            print(f"  ❌ 发现 {len(issues)} 个可能的问题:")
            for issue in issues:
                print(f"    行 {issue['line']}: {issue['content']}")
            all_issues.extend(issues)
        else:
            print(f"  ✅ 未发现旧格式写入代码")
    
    print(f"\n🏁 检查完成！")
    
    if all_issues:
        print(f"❌ 总共发现 {len(all_issues)} 个可能的问题需要修复")
        print("\n详细问题列表:")
        for issue in all_issues:
            print(f"  {issue['file']}:{issue['line']} - {issue['content']}")
        return False
    else:
        print("🎉 所有文件都已清理完毕，未发现旧格式写入代码！")
        return True

if __name__ == "__main__":
    main()
