import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QPushButton, QLabel, QFileDialog, 
                           QComboBox, QDateEdit, QMessageBox, QTextEdit, QProgressBar, QLineEdit)
from PyQt5.QtGui import QDoubleValidator
from PyQt5.QtCore import Qt, QTimer
from stock_utils import StockCodeParser
from xtquant import xtdata
import time


class VolumeStockFilter(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.stock_parser = StockCodeParser()
        self.filtered_stocks = []  # 存储筛选结果
        self.data_path = r"D:\国金证券QMT交易端\userdata_mini\datadir"  # 设置默认数据路径

        # 历史筛选结果存储
        self.history_results = []  # 存储历史筛选结果
        self.current_result_index = -1  # 当前显示的结果索引

        # 定时运行设置
        self.auto_run_times = [
            (10, 32),  # 10:32
            (11, 32),  # 11:32
            (14, 2),   # 14:02
            (15, 2)    # 15:02
        ]
        self.time_tolerance = 2  # 时间容错范围（分钟）
        self.last_run_time = None  # 记录上次运行时间，避免重复运行

        # 设置定时器，每分钟检查一次
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_auto_run)
        self.timer.start(60000)  # 60秒检查一次
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('放量可转债筛选工具')
        self.setGeometry(300, 300, 800, 600)  # 调整窗口大小
        
        # 创建中心部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 文件选择区域
        file_layout = QHBoxLayout()
        self.file_label = QLabel('通达信板块文件:')
        self.file_path = QLabel('未选择文件')
        self.select_file_btn = QPushButton('选择文件')
        self.select_file_btn.clicked.connect(self.select_file)
        file_layout.addWidget(self.file_label)
        file_layout.addWidget(self.file_path)
        file_layout.addWidget(self.select_file_btn)
        layout.addLayout(file_layout)
        
        # 设置默认的通达信板块文件路径
        default_blk_file = r"F:\work\股票\回测与交易\t.blk"
        self.file_path.setText(default_blk_file)
        
        # 周期选择区域
        period_layout = QHBoxLayout()
        self.period_label = QLabel('选择周期:')
        self.period_combo = QComboBox()
        self.period_combo.addItems(['日线', '30分钟', '15分钟', '5分钟'])
        self.period_combo.setCurrentText('5分钟')  # 设置默认为5分钟
        period_layout.addWidget(self.period_label)
        period_layout.addWidget(self.period_combo)
        layout.addLayout(period_layout)
        
        # 日期选择区域
        date_layout = QHBoxLayout()
        self.date_label = QLabel('选择日期:')
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(datetime.now().date())
        date_layout.addWidget(self.date_label)
        date_layout.addWidget(self.date_edit)
        layout.addLayout(date_layout)
        
        # 筛选条件区域
        filter_layout = QHBoxLayout()

        # 量比条件
        volume_ratio_layout = QHBoxLayout()
        self.volume_ratio_label = QLabel('5周期量比 >')
        self.volume_ratio_input = QLineEdit()
        self.volume_ratio_input.setText('4.0')  # 默认值为4.0
        self.volume_ratio_input.setValidator(QDoubleValidator(0.0, 999.0, 2))  # 允许输入0-999之间的数字，保留2位小数
        volume_ratio_layout.addWidget(self.volume_ratio_label)
        volume_ratio_layout.addWidget(self.volume_ratio_input)
        filter_layout.addLayout(volume_ratio_layout)

        # 成交额条件
        amount_layout = QHBoxLayout()
        self.amount_label = QLabel('成交额(万) >')
        self.amount_input = QLineEdit()
        self.amount_input.setText('5000')  # 默认值改为5000万
        self.amount_input.setValidator(QDoubleValidator(0.0, 999999.0, 2))  # 允许输入0-999999之间的数字，保留2位小数
        amount_layout.addWidget(self.amount_label)
        amount_layout.addWidget(self.amount_input)
        filter_layout.addLayout(amount_layout)
        
        layout.addLayout(filter_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 下载历史数据按钮
        self.download_btn = QPushButton('下载历史数据')
        self.download_btn.clicked.connect(self.download_history_data)
        button_layout.addWidget(self.download_btn)
        
        # 运行按钮
        self.run_btn = QPushButton('运行筛选')
        self.run_btn.clicked.connect(self.run_filter)
        button_layout.addWidget(self.run_btn)
        
        layout.addLayout(button_layout)
        
        # 添加进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 添加结果显示区域
        result_header_layout = QHBoxLayout()
        result_header_layout.addWidget(QLabel('筛选结果:'))

        # 添加结果切换按钮
        self.prev_result_btn = QPushButton('◀ 上一次')
        self.prev_result_btn.clicked.connect(self.show_previous_result)
        self.prev_result_btn.setEnabled(False)

        self.next_result_btn = QPushButton('下一次 ▶')
        self.next_result_btn.clicked.connect(self.show_next_result)
        self.next_result_btn.setEnabled(False)

        self.result_info_label = QLabel('暂无结果')

        result_header_layout.addStretch()
        result_header_layout.addWidget(self.prev_result_btn)
        result_header_layout.addWidget(self.result_info_label)
        result_header_layout.addWidget(self.next_result_btn)

        layout.addLayout(result_header_layout)

        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        layout.addWidget(self.result_text)
        
        # 添加保存按钮
        self.save_btn = QPushButton('保存到文件')
        self.save_btn.clicked.connect(self.save_to_file)
        self.save_btn.setEnabled(False)  # 初始状态禁用
        layout.addWidget(self.save_btn)

        # 添加调试按钮
        debug_layout = QHBoxLayout()
        self.debug_code_input = QLineEdit()
        self.debug_code_input.setPlaceholderText('输入股票代码进行调试分析，如：118004.SH')
        self.debug_btn = QPushButton('调试分析')
        self.debug_btn.clicked.connect(self.debug_stock)
        debug_layout.addWidget(QLabel('调试股票:'))
        debug_layout.addWidget(self.debug_code_input)
        debug_layout.addWidget(self.debug_btn)
        layout.addLayout(debug_layout)

        # 添加自动运行状态显示
        auto_run_layout = QHBoxLayout()
        self.auto_run_label = QLabel('自动运行时间: 10:32, 11:32, 14:02, 15:02 (±2分钟容错)')
        self.auto_run_status = QLabel('状态: 等待中...')
        auto_run_layout.addWidget(self.auto_run_label)
        auto_run_layout.addWidget(self.auto_run_status)
        layout.addLayout(auto_run_layout)
        
    def select_file(self):
        """选择通达信板块文件"""
        file_name, _ = QFileDialog.getOpenFileName(
            self, '选择通达信板块文件', '',
            'BLK Files (*.blk);;All Files (*)'
        )
        if file_name:
            self.file_path.setText(file_name)

    def check_auto_run(self):
        """检查是否到了自动运行时间"""
        try:
            now = datetime.now()
            current_time = (now.hour, now.minute)
            current_time_str = f"{now.hour:02d}:{now.minute:02d}"

            # 更新状态显示
            self.auto_run_status.setText(f'状态: 当前时间 {current_time_str}，等待运行时间...')

            # 检查是否在自动运行时间范围内
            for target_hour, target_minute in self.auto_run_times:
                # 计算时间差（分钟）
                target_total_minutes = target_hour * 60 + target_minute
                current_total_minutes = now.hour * 60 + now.minute
                time_diff = abs(current_total_minutes - target_total_minutes)

                # 在容错范围内且未在此时间段运行过
                if time_diff <= self.time_tolerance:
                    current_run_key = f"{now.year}-{now.month:02d}-{now.day:02d}-{target_hour:02d}:{target_minute:02d}"

                    if self.last_run_time != current_run_key:
                        self.last_run_time = current_run_key
                        self.auto_run_status.setText(f'状态: 触发自动运行 ({target_hour:02d}:{target_minute:02d})')
                        print(f"\n=== 自动运行触发 ===")
                        print(f"目标时间: {target_hour:02d}:{target_minute:02d}")
                        print(f"当前时间: {current_time_str}")
                        print(f"时间差: {time_diff}分钟")

                        # 执行自动运行
                        self.auto_run_filter()
                        return

        except Exception as e:
            print(f"检查自动运行时出错: {str(e)}")

    def auto_run_filter(self):
        """自动运行筛选并保存结果"""
        try:
            print("开始自动运行筛选...")
            self.auto_run_status.setText('状态: 正在自动运行筛选...')

            # 检查文件是否已选择
            if self.file_path.text() == '未选择文件':
                print("错误：未选择通达信板块文件")
                self.auto_run_status.setText('状态: 错误 - 未选择板块文件')
                return

            # 设置为5分钟周期（适合自动运行）
            self.period_combo.setCurrentText('5分钟')

            # 设置当前日期
            self.date_edit.setDate(datetime.now().date())

            # 执行筛选
            self.run_filter_internal()

            # 保存结果到指定文件和历史记录
            if self.filtered_stocks:
                self.save_results_to_txt()

                # 创建自动运行结果的显示文本
                auto_result_text = (
                    f"=== 自动运行筛选结果 ===\n"
                    f"运行时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                    f"筛选条件：5周期量比 > {self.volume_ratio_input.text()}, 成交额 > {self.amount_input.text()}万, 阳线, 收盘价 > EMA50\n"
                    f"符合条件数量：{len(self.filtered_stocks)}\n\n"
                    f"符合条件的股票代码：\n"
                )

                # 添加股票代码
                for code in sorted(self.filtered_stocks):
                    code_number = code.split('.')[0]
                    auto_result_text += f"{code_number}\n"

                # 保存到历史记录
                self.save_current_result(auto_result_text, self.filtered_stocks)

                print(f"自动运行完成，找到 {len(self.filtered_stocks)} 只符合条件的股票")
                self.auto_run_status.setText(f'状态: 自动运行完成，找到 {len(self.filtered_stocks)} 只股票')
            else:
                # 即使没有结果也保存到历史记录
                auto_result_text = (
                    f"=== 自动运行筛选结果 ===\n"
                    f"运行时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                    f"筛选条件：5周期量比 > {self.volume_ratio_input.text()}, 成交额 > {self.amount_input.text()}万, 阳线, 收盘价 > EMA50\n"
                    f"符合条件数量：0\n\n"
                    f"未找到符合条件的股票"
                )
                self.save_current_result(auto_result_text, [])

                print("自动运行完成，未找到符合条件的股票")
                self.auto_run_status.setText('状态: 自动运行完成，未找到符合条件的股票')

        except Exception as e:
            print(f"自动运行时出错: {str(e)}")
            self.auto_run_status.setText(f'状态: 自动运行出错 - {str(e)}')

    def save_results_to_txt(self):
        """保存筛选结果到指定格式的txt文件"""
        try:
            if not self.filtered_stocks:
                return

            # 生成文件名（包含时间戳）
            now = datetime.now()
            filename = "放量可转债.txt"

            # 准备结果内容
            result_lines = []
            result_lines.append(f"# 放量可转债筛选结果")
            result_lines.append(f"# 生成时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
            result_lines.append(f"# 筛选条件: 5周期量比 > {self.volume_ratio_input.text()}, 成交额 > {self.amount_input.text()}万, 阳线, 收盘价 > EMA50")
            result_lines.append(f"# 符合条件数量: {len(self.filtered_stocks)}")
            result_lines.append("")

            # 转换股票代码格式
            for code in sorted(self.filtered_stocks):
                # 转换为指定格式：118054.SH, 128097.SZ
                if code.endswith('.SH'):
                    formatted_code = code  # 保持.SH大写
                elif code.endswith('.SZ'):
                    formatted_code = code  # 保持.SZ大写
                else:
                    formatted_code = code

                result_lines.append(formatted_code)

            # 写入文件
            with open(filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(result_lines))

            print(f"筛选结果已保存到: {filename}")

        except Exception as e:
            print(f"保存筛选结果时出错: {str(e)}")

    def show_previous_result(self):
        """显示上一次筛选结果"""
        if self.current_result_index > 0:
            self.current_result_index -= 1
            self.display_result_by_index(self.current_result_index)
            self.update_result_navigation()

    def show_next_result(self):
        """显示下一次筛选结果"""
        if self.current_result_index < len(self.history_results) - 1:
            self.current_result_index += 1
            self.display_result_by_index(self.current_result_index)
            self.update_result_navigation()

    def display_result_by_index(self, index):
        """根据索引显示筛选结果"""
        if 0 <= index < len(self.history_results):
            result_data = self.history_results[index]
            self.result_text.setText(result_data['display_text'])
            self.filtered_stocks = result_data['filtered_stocks'].copy()
            self.save_btn.setEnabled(bool(self.filtered_stocks))

    def update_result_navigation(self):
        """更新结果导航按钮状态"""
        total_results = len(self.history_results)
        if total_results == 0:
            self.result_info_label.setText('暂无结果')
            self.prev_result_btn.setEnabled(False)
            self.next_result_btn.setEnabled(False)
        else:
            current_num = self.current_result_index + 1
            self.result_info_label.setText(f'{current_num}/{total_results}')
            self.prev_result_btn.setEnabled(self.current_result_index > 0)
            self.next_result_btn.setEnabled(self.current_result_index < total_results - 1)

    def save_current_result(self, display_text, filtered_stocks, timestamp=None):
        """保存当前筛选结果到历史记录"""
        if timestamp is None:
            timestamp = datetime.now()

        result_data = {
            'timestamp': timestamp,
            'display_text': display_text,
            'filtered_stocks': filtered_stocks.copy(),
            'time_str': timestamp.strftime('%Y-%m-%d %H:%M:%S')
        }

        # 添加到历史记录
        self.history_results.append(result_data)

        # 限制历史记录数量（保留最近20次）
        if len(self.history_results) > 20:
            self.history_results.pop(0)
            if self.current_result_index > 0:
                self.current_result_index -= 1

        # 设置当前索引为最新结果
        self.current_result_index = len(self.history_results) - 1

        # 更新导航状态
        self.update_result_navigation()



    def calculate_volume_ratio(self, df, periods=5):
        """
        计算量比指标
        公式：量比 = VOL / MA(VOL, M)
        即：当前成交量 / 前M个周期的移动平均成交量
        M默认为5
        """
        try:
            if len(df) < periods:
                return pd.Series(index=df.index)

            # 使用pandas的rolling方法计算移动平均，更高效且准确
            df_copy = df.copy()

            # 计算前M个周期的移动平均成交量
            ma_volume = df_copy['volume'].rolling(window=periods, min_periods=periods).mean()

            # 计算量比：当前成交量 / 前M个周期的移动平均成交量
            # 注意：这里不使用shift，因为标准量比公式是包含当前周期的移动平均
            volume_ratio = df_copy['volume'] / ma_volume

            return volume_ratio

        except Exception as e:
            print(f"计算量比时出错: {str(e)}")
            return pd.Series(index=df.index)

    def calculate_ema(self, df, periods=50):
        """
        计算EMA指数移动平均线
        公式：EMA = (2 / (periods + 1)) * (当前价格 - 前一日EMA) + 前一日EMA
        """
        try:
            if len(df) < periods:
                return pd.Series(index=df.index)

            # 使用pandas的ewm方法计算EMA
            ema = df['close'].ewm(span=periods, adjust=False).mean()

            return ema

        except Exception as e:
            print(f"计算EMA时出错: {str(e)}")
            return pd.Series(index=df.index)

    def check_volume_ratio_condition(self, df, threshold=4.0, periods=5, amount_threshold=50000000):
        """
        检查在最近一个交易日的某个时间点是否曾有量比大于阈值
        同时检查该K线的成交额、阳线条件和EMA50条件

        Returns:
            (是否满足所有条件, 最大量比值, 满足条件的K线信息)
        """
        try:
            if df.empty or len(df) < max(periods, 50):  # 确保有足够数据计算EMA50
                return False, 0.0, None

            # 计算量比
            df['volume_ratio'] = self.calculate_volume_ratio(df, periods)

            # 计算EMA50
            df['ema50'] = self.calculate_ema(df, periods=50)

            # 获取最后一个交易日的数据
            last_date = pd.to_datetime(df['time'].iloc[-1]/1000, unit='s').date()

            # 筛选最后一个交易日的数据
            last_day_data = df[pd.to_datetime(df['time']/1000, unit='s').dt.date == last_date]

            if last_day_data.empty:
                return False, 0.0, None

            # 找到量比大于阈值的K线
            high_ratio_data = last_day_data[last_day_data['volume_ratio'] > threshold]

            if high_ratio_data.empty:
                max_volume_ratio = last_day_data['volume_ratio'].max()
                return False, max_volume_ratio, None

            # 在量比满足条件的K线中，检查成交额、阳线条件和EMA50条件
            qualified_candles = []
            for _, row in high_ratio_data.iterrows():
                # 检查成交额条件
                amount_condition = row['amount'] > amount_threshold
                # 检查阳线条件
                bullish_condition = row['close'] > row['open']
                # 检查EMA50条件
                ema50_condition = row['close'] > row['ema50'] if not pd.isna(row['ema50']) else False

                if amount_condition and bullish_condition and ema50_condition:
                    qualified_candles.append({
                        'time': row['time'],
                        'volume_ratio': row['volume_ratio'],
                        'amount': row['amount'],
                        'open': row['open'],
                        'close': row['close'],
                        'volume': row['volume'],
                        'ema50': row['ema50']
                    })

            # 获取最大量比值
            max_volume_ratio = high_ratio_data['volume_ratio'].max()

            # 如果有满足所有条件的K线，返回True
            if qualified_candles:
                # 返回量比最大的那根K线信息
                best_candle = max(qualified_candles, key=lambda x: x['volume_ratio'])
                return True, max_volume_ratio, best_candle
            else:
                return False, max_volume_ratio, None

        except Exception as e:
            print(f"检查量比条件时出错: {str(e)}")
            return False, 0.0, None
            
    def run_filter(self):
        """运行股票筛选（UI调用）"""
        self.run_filter_internal()

    def run_filter_internal(self):
        """运行股票筛选（内部实现）"""
        try:
            # 检查数据路径是否已选择
            if not self.data_path:
                QMessageBox.warning(self, '警告', '请先选择数据路径！')
                return
                
            # 清空之前的结果
            self.result_text.clear()
            self.filtered_stocks = []
            self.save_btn.setEnabled(False)
            
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(0)
            
            # 禁用按钮
            self.run_btn.setEnabled(False)
            self.download_btn.setEnabled(False)
            
            # 获取筛选条件
            try:
                volume_ratio_threshold = float(self.volume_ratio_input.text())
                amount_threshold = float(self.amount_input.text()) * 10000  # 转换为元（万元转元）
                self.result_text.append(f"筛选条件：5周期量比 > {volume_ratio_threshold:.2f}, 成交额 > {amount_threshold/10000:.0f}万, 阳线, 收盘价 > EMA50")
            except ValueError:
                QMessageBox.warning(self, '警告', '请输入有效的筛选条件！')
                return
            
            # 检查文件是否已选择
            if self.file_path.text() == '未选择文件':
                QMessageBox.warning(self, '警告', '请先选择通达信板块文件！')
                return
                
            # 读取股票代码
            self.result_text.append("\n步骤1/5: 正在读取股票代码...")
            self.progress_bar.setValue(10)
            QApplication.processEvents()
            
            stock_codes = self.stock_parser.read_stock_codes(self.file_path.text())
            if not stock_codes:
                QMessageBox.warning(self, '警告', '未能从文件中读取到有效的股票代码！')
                return
            self.result_text.append(f"成功读取 {len(stock_codes)} 只股票代码")
            
            # 获取选择的日期
            selected_date = self.date_edit.date().toPyDate()
            self.result_text.append(f"选择日期：{selected_date.strftime('%Y-%m-%d')}")
            
            # 获取交易日历
            self.result_text.append("\n步骤2/5: 正在获取交易日历...")
            self.progress_bar.setValue(20)
            QApplication.processEvents()
            
            # 获取选择的日期的前30天到当天的交易日历
            end_time = selected_date.strftime('%Y%m%d')
            start_time = (selected_date - timedelta(days=30)).strftime('%Y%m%d')
            
            self.result_text.append(f"获取交易日历范围：{start_time} 至 {end_time}")
            QApplication.processEvents()
            
            # 将日期字符串转换为datetime对象，设置为当天结束时间
            end_datetime = datetime.strptime(end_time, '%Y%m%d').replace(hour=23, minute=59, second=59)
            start_datetime = datetime.strptime(start_time, '%Y%m%d')
            
            # 转换为毫秒时间戳
            end_timestamp = int(end_datetime.timestamp() * 1000)
            start_timestamp = int(start_datetime.timestamp() * 1000)
            
            trading_days_sh = xtdata.get_trading_dates(
                market='SH',
                start_time=start_time,
                end_time=end_time
            )
            
            trading_days_sz = xtdata.get_trading_dates(
                market='SZ',
                start_time=start_time,
                end_time=end_time
            )
            
            # 合并两个市场的交易日历并排序
            trading_days = sorted(set(trading_days_sh + trading_days_sz))
            
            if not trading_days:
                QMessageBox.warning(self, '警告', '无法获取交易日历！')
                return
                
            # 找到不大于选择日期的最大交易日
            valid_trading_days = [ts for ts in trading_days if ts <= end_timestamp]
            if not valid_trading_days:
                QMessageBox.warning(self, '警告', '所选日期之前没有有效的交易日！')
                return

            # 获取最近的交易日（无论是否在交易时段都使用最近的交易日数据）
            end_date_ts = max(valid_trading_days)
            self.result_text.append("使用最近的交易日数据")
            
            # 转换为日期字符串并显示
            end_date = datetime.fromtimestamp(end_date_ts/1000).strftime('%Y%m%d')
            self.result_text.append(f"最近交易日：{end_date}")
            
            # 找到结束日期在交易日历中的位置
            end_date_index = trading_days.index(end_date_ts)
            
            # 根据周期选择不同的数据范围
            period_text = self.period_combo.currentText()
            if period_text == '5分钟' or period_text == '15分钟':
                # 对于分钟级别数据，只需要往前推3个交易日
                days_back = 2  # 实际获取3天数据（包含当天）
                self.result_text.append(f"使用短期数据范围（适用于{period_text}数据）")
            elif period_text == '30分钟':
                # 对于30分钟数据，往前推5个交易日
                days_back = 4  # 实际获取5天数据（包含当天）
                self.result_text.append(f"使用中期数据范围（适用于{period_text}数据）")
            else:
                # 对于日线数据，保持原来的18个交易日
                days_back = 17  # 实际获取18天数据（包含当天）
                self.result_text.append(f"使用标准数据范围（适用于{period_text}数据）")
                
            start_date_index = max(0, end_date_index - days_back)
            start_date_ts = trading_days[start_date_index]
            start_date = datetime.fromtimestamp(start_date_ts/1000).strftime('%Y%m%d')
            
            self.result_text.append(f"\n计算量比所需数据范围：{start_date} 至 {end_date}")
            self.result_text.append(f"（需要{end_date_index - start_date_index + 1}个交易日数据用于计算量比）")
            QApplication.processEvents()
            
            # 获取周期
            period_map = {
                '日线': '1d',
                '30分钟': '30m',
                '15分钟': '15m',
                '5分钟': '5m'
            }
            period = period_map[self.period_combo.currentText()]
            self.result_text.append(f"数据周期：{self.period_combo.currentText()}")
            
            # 下载数据
            self.result_text.append("\n步骤3/5: 正在获取股票数据...")
            self.progress_bar.setValue(30)
            QApplication.processEvents()
            
            data_dict = self.download_data(
                stock_list=stock_codes,
                period=period,
                start_date=start_date,
                end_date=end_date
            )
            
            if not data_dict:
                QMessageBox.warning(self, '警告', '未能获取到股票数据！')
                return
                
            # 筛选符合条件的股票
            total_stocks = len(data_dict) - 1  # 减去_used_local_data键
            processed_count = 0
            download_count = 0  # 所有数据都是从服务器下载的
            match_count = 0  # 符合条件的股票数量

            self.result_text.append("\n步骤4/5: 开始筛选股票...")
            self.result_text.append(f"总股票数：{total_stocks}")

            for code, df in data_dict.items():
                if code == '_used_local_data':
                    continue

                if df is not None and not df.empty:
                    processed_count += 1
                    download_count += 1  # 所有数据都是从服务器下载的

                    # 显示当前处理的股票
                    self.result_text.append(f"\n正在处理：{code}")

                    # 所有数据都是从服务器下载的最新数据
                    self.result_text.append(f"数据来源：服务器最新数据")
                    # 显示数据的实际时间范围
                    data_start = datetime.fromtimestamp(df['time'].min()/1000).strftime('%Y%m%d')
                    data_end = datetime.fromtimestamp(df['time'].max()/1000).strftime('%Y%m%d')
                    self.result_text.append(f"数据范围：{data_start} 至 {data_end}")
                    
                    # 检查量比、成交额和阳线条件（一次性检查所有条件）
                    all_conditions, max_volume_ratio, best_candle = self.check_volume_ratio_condition(
                        df,
                        threshold=volume_ratio_threshold,
                        amount_threshold=amount_threshold
                    )

                    self.result_text.append(f"最大量比值：{max_volume_ratio:.2f}")

                    # 获取最后一天的数据（用于显示当前价格）
                    last_day = df.iloc[-1]
                    price_condition = last_day['close'] <= 300  # 添加价格条件

                    # 显示条件检查结果
                    if best_candle:
                        # 有满足量比条件的K线
                        candle_time = datetime.fromtimestamp(best_candle['time']/1000).strftime('%H:%M')
                        self.result_text.append(f"满足条件的K线时间：{candle_time}")
                        self.result_text.append(f"该K线量比：{best_candle['volume_ratio']:.2f}")
                        self.result_text.append(f"该K线成交额：{best_candle['amount']/10000:.0f}万")
                        candle_type = "阳线" if best_candle['close'] > best_candle['open'] else "阴线"
                        self.result_text.append(f"该K线类型：{candle_type}")
                    else:
                        self.result_text.append("没有找到同时满足量比、成交额和阳线条件的K线")

                    self.result_text.append(f"当前价格：{last_day['close']:.2f}")
                    self.result_text.append(f"条件检查：量比 > {volume_ratio_threshold:.2f} 且 成交额 > {amount_threshold/10000:.0f}万 且 为阳线 且 收盘价 > EMA50 ({all_conditions}), 价格 <= 300 ({price_condition})")

                    if all_conditions and price_condition:
                        self.filtered_stocks.append(code)
                        match_count += 1
                        self.result_text.append(f"符合条件！")
                    else:
                        self.result_text.append(f"不符合条件")
                    
                    # 更新进度
                    progress = 30 + int((processed_count / total_stocks) * 60)
                    self.progress_bar.setValue(progress)
                    
                    # 每处理完一只股票就更新进度
                    self.result_text.append(f"处理进度：{processed_count}/{total_stocks} ({progress}%)")
                    self.result_text.append(f"当前统计：服务器下载 {download_count}只，符合条件 {match_count}只")
                    if processed_count > 0:
                        self.result_text.append(f"当前符合率：{(match_count/processed_count*100):.2f}%")
                    QApplication.processEvents()
            
            # 显示最终结果
            self.result_text.append("\n步骤5/5: 生成筛选结果...")
            self.progress_bar.setValue(90)
            QApplication.processEvents()
            
            result_text = []
            result_text.append(f"筛选日期：{selected_date.strftime('%Y-%m-%d')}")
            result_text.append(f"最近交易日：{end_date[:4]}-{end_date[4:6]}-{end_date[6:]}")
            result_text.append(f"选择周期：{self.period_combo.currentText()}")
            result_text.append(f"筛选条件：5周期量比 > {volume_ratio_threshold:.2f}, 成交额 > {amount_threshold/10000:.0f}万, 阳线, 收盘价 > EMA50")
            result_text.append(f"计算量比所需数据范围：{start_date[:4]}-{start_date[4:6]}-{start_date[6:]} 至 {end_date[:4]}-{end_date[4:6]}-{end_date[6:]}")
            result_text.append(f"\n处理统计：")
            result_text.append(f"总股票数：{total_stocks}")
            result_text.append(f"服务器下载：{download_count}只 ({(download_count/total_stocks*100):.2f}%)")
            result_text.append(f"符合条件数：{match_count}只 ({(match_count/total_stocks*100):.2f}%)")
            result_text.append("\n符合条件的股票代码：")
            
            if self.filtered_stocks:
                # 按代码排序
                sorted_stocks = sorted(self.filtered_stocks)
                for code in sorted_stocks:
                    # 只显示数字部分
                    code_number = code.split('.')[0]
                    result_text.append(code_number)
            else:
                result_text.append("未找到符合条件的股票")
            
            # 保存当前结果到历史记录
            display_text = '\n'.join(result_text)
            self.save_current_result(display_text, self.filtered_stocks)

            # 显示结果
            self.result_text.setText(display_text)

            # 如果有结果，启用保存按钮并自动保存
            self.save_btn.setEnabled(bool(self.filtered_stocks))

            # 自动保存结果到txt文件
            if self.filtered_stocks:
                self.save_results_to_txt()
                print(f"筛选结果已自动保存到: 放量可转债.txt")

            # 完成进度
            self.progress_bar.setValue(100)

            # 恢复按钮状态
            self.run_btn.setEnabled(True)
            self.download_btn.setEnabled(True)

            # 取消弹窗，改为在结果区域显示完成信息
            completion_info = (
                f'\n=== 筛选完成 ===\n'
                f'处理统计：\n'
                f'总股票数：{total_stocks}\n'
                f'服务器下载：{download_count}只 ({(download_count/total_stocks*100):.2f}%)\n'
                f'符合条件数：{match_count}只 ({(match_count/total_stocks*100):.2f}%)\n'
                f'符合率：{(match_count/total_stocks*100):.2f}%\n'
                f'完成时间：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
            )

            # 将完成信息添加到结果显示中
            updated_display_text = display_text + completion_info
            self.result_text.setText(updated_display_text)

            # 更新历史记录中的显示文本
            if self.history_results:
                self.history_results[-1]['display_text'] = updated_display_text
                
        except Exception as e:
            # 恢复按钮状态
            self.run_btn.setEnabled(True)
            self.download_btn.setEnabled(True)
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, '错误', f'运行过程中出错：{str(e)}')
            
    def save_to_file(self):
        """保存结果到文件"""
        try:
            if not self.filtered_stocks:
                QMessageBox.warning(self, '警告', '没有可保存的结果！')
                return

            # 使用选择的日期生成文件名
            selected_date_str = self.date_edit.date().toPyDate().strftime('%Y%m%d')
            output_filename = f'kzz{selected_date_str}.blk'

            with open(output_filename, 'w', encoding='gbk') as f:
                for code in self.filtered_stocks:
                    # 获取原始代码（去掉.SH或.SZ后缀）
                    raw_code = code.split('.')[0]
                    # 根据市场添加前缀
                    if code.endswith('.SH'):
                        f.write(f'1{raw_code}\n')
                    else:  # .SZ
                        f.write(f'0{raw_code}\n')

            QMessageBox.information(self, '成功', f'结果已保存至：{output_filename}')

        except Exception as e:
            QMessageBox.critical(self, '错误', f'保存文件时出错：{str(e)}')

    def debug_stock(self):
        """调试分析指定股票的数据"""
        try:
            # 获取输入的股票代码
            stock_code = self.debug_code_input.text().strip()
            if not stock_code:
                QMessageBox.warning(self, '警告', '请输入股票代码！')
                return

            # 清空结果显示
            self.result_text.clear()
            self.result_text.append(f"=== 调试分析股票: {stock_code} ===\n")

            # 获取选择的日期
            selected_date = self.date_edit.date().toPyDate()
            self.result_text.append(f"选择日期：{selected_date.strftime('%Y-%m-%d')}")

            # 获取交易日历
            self.result_text.append("\n正在获取交易日历...")

            # 获取选择的日期的前30天到当天的交易日历
            end_time = selected_date.strftime('%Y%m%d')
            start_time = (selected_date - timedelta(days=30)).strftime('%Y%m%d')

            # 将日期字符串转换为datetime对象，设置为当天结束时间
            end_datetime = datetime.strptime(end_time, '%Y%m%d').replace(hour=23, minute=59, second=59)
            start_datetime = datetime.strptime(start_time, '%Y%m%d')

            # 转换为毫秒时间戳
            end_timestamp = int(end_datetime.timestamp() * 1000)
            start_timestamp = int(start_datetime.timestamp() * 1000)

            trading_days = self.get_trading_days(start_time, end_time)

            if not trading_days:
                QMessageBox.warning(self, '警告', '无法获取交易日历！')
                return

            # 找到不大于选择日期的最大交易日
            valid_trading_days = [ts for ts in trading_days if ts <= end_timestamp]
            if not valid_trading_days:
                QMessageBox.warning(self, '警告', '所选日期之前没有有效的交易日！')
                return

            # 获取最近的交易日
            end_date_ts = max(valid_trading_days)

            # 转换为日期字符串并显示
            end_date = datetime.fromtimestamp(end_date_ts/1000).strftime('%Y%m%d')
            self.result_text.append(f"最近交易日：{end_date}")

            # 找到结束日期在交易日历中的位置
            end_date_index = trading_days.index(end_date_ts)

            # 获取足够的历史数据用于计算量比
            days_back = 4  # 获取5天数据（包含当天）
            start_date_index = max(0, end_date_index - days_back)
            start_date_ts = trading_days[start_date_index]
            start_date = datetime.fromtimestamp(start_date_ts/1000).strftime('%Y%m%d')

            self.result_text.append(f"\n数据范围：{start_date} 至 {end_date}")

            # 获取周期
            period_map = {
                '日线': '1d',
                '30分钟': '30m',
                '15分钟': '15m',
                '5分钟': '5m'
            }
            period = period_map[self.period_combo.currentText()]
            self.result_text.append(f"数据周期：{self.period_combo.currentText()}")

            # 下载数据
            self.result_text.append("\n正在获取股票数据...")

            data_dict = self.download_data(
                stock_list=[stock_code],
                period=period,
                start_date=start_date,
                end_date=end_date
            )

            if not data_dict or stock_code not in data_dict:
                QMessageBox.warning(self, '警告', f'未能获取到股票 {stock_code} 的数据！')
                return

            df = data_dict[stock_code]
            if df is None or df.empty:
                QMessageBox.warning(self, '警告', f'股票 {stock_code} 的数据为空！')
                return

            # 显示数据基本信息
            self.result_text.append(f"\n数据基本信息：")
            self.result_text.append(f"总K线数量：{len(df)}")
            self.result_text.append(f"数据时间范围：{datetime.fromtimestamp(df['time'].min()/1000).strftime('%Y-%m-%d %H:%M')} 至 {datetime.fromtimestamp(df['time'].max()/1000).strftime('%Y-%m-%d %H:%M')}")

            # 计算量比和EMA50
            df['volume_ratio'] = self.calculate_volume_ratio(df, periods=5)
            df['ema50'] = self.calculate_ema(df, periods=50)

            # 获取最近交易日的数据
            target_date = datetime.strptime(end_date, '%Y%m%d').date()
            last_day_data = df[pd.to_datetime(df['time']/1000, unit='s').dt.date == target_date]

            if last_day_data.empty:
                self.result_text.append(f"\n警告：{end_date} 当天没有数据！")
                return

            self.result_text.append(f"\n{end_date} 当天数据详情：")
            self.result_text.append(f"当天K线数量：{len(last_day_data)}")

            # 显示当天所有K线的详细信息
            self.result_text.append(f"\n当天所有K线数据：")
            self.result_text.append("时间\t\t开盘\t最高\t最低\t收盘\t成交量\t成交额(万)\t量比\tEMA50\t类型")
            self.result_text.append("-" * 120)

            for _, row in last_day_data.iterrows():
                time_str = datetime.fromtimestamp(row['time']/1000).strftime('%H:%M')
                volume_ratio_str = f"{row['volume_ratio']:.2f}" if not pd.isna(row['volume_ratio']) else "N/A"
                ema50_str = f"{row['ema50']:.3f}" if not pd.isna(row['ema50']) else "N/A"
                candle_type = "阳" if row['close'] > row['open'] else "阴"

                self.result_text.append(f"{time_str}\t\t{row['open']:.3f}\t{row['high']:.3f}\t{row['low']:.3f}\t{row['close']:.3f}\t{row['volume']:.0f}\t{row['amount']/10000:.0f}\t{volume_ratio_str}\t{ema50_str}\t{candle_type}")

            # 分析选股条件
            self.result_text.append(f"\n选股条件分析：")

            # 获取筛选条件
            try:
                volume_ratio_threshold = float(self.volume_ratio_input.text())
                amount_threshold = float(self.amount_input.text()) * 10000  # 转换为元（万元转元）
            except ValueError:
                volume_ratio_threshold = 4.0
                amount_threshold = 50000000

            # 使用新的综合条件检查
            all_conditions, max_volume_ratio, best_candle = self.check_volume_ratio_condition(
                df,
                threshold=volume_ratio_threshold,
                amount_threshold=amount_threshold
            )

            self.result_text.append(f"1. 综合条件检查：量比 > {volume_ratio_threshold:.2f} 且 成交额 > {amount_threshold/10000:.0f}万 且 为阳线 且 收盘价 > EMA50")
            self.result_text.append(f"   当天最大量比：{max_volume_ratio:.2f}")

            if best_candle:
                candle_time = datetime.fromtimestamp(best_candle['time']/1000).strftime('%H:%M')
                self.result_text.append(f"   满足条件的K线时间：{candle_time}")
                self.result_text.append(f"   该K线量比：{best_candle['volume_ratio']:.2f}")
                self.result_text.append(f"   该K线成交额：{best_candle['amount']/10000:.0f}万")
                self.result_text.append(f"   该K线类型：阳线（开盘={best_candle['open']:.3f}, 收盘={best_candle['close']:.3f}）")
                self.result_text.append(f"   该K线EMA50：{best_candle['ema50']:.3f}，收盘价 > EMA50：{'✅' if best_candle['close'] > best_candle['ema50'] else '❌'}")
                self.result_text.append(f"   是否满足：✅")
            else:
                self.result_text.append(f"   没有找到同时满足所有条件的K线")
                self.result_text.append(f"   是否满足：❌")

            # 显示最后一根K线信息（用于参考）
            last_candle = last_day_data.iloc[-1]
            self.result_text.append(f"\n2. 最后一根K线信息（仅供参考）：")
            self.result_text.append(f"   时间：{datetime.fromtimestamp(last_candle['time']/1000).strftime('%H:%M')}")
            self.result_text.append(f"   开盘={last_candle['open']:.3f}, 收盘={last_candle['close']:.3f}")
            self.result_text.append(f"   成交额：{last_candle['amount']/10000:.0f}万")

            # 综合判断
            self.result_text.append(f"\n综合判断：{'✅ 符合选股条件' if all_conditions else '❌ 不符合选股条件'}")

            # 显示量比超过阈值的时间点
            high_ratio_data = last_day_data[last_day_data['volume_ratio'] > volume_ratio_threshold]
            if not high_ratio_data.empty:
                self.result_text.append(f"\n量比超过{volume_ratio_threshold:.2f}的时间点详情：")
                for _, row in high_ratio_data.iterrows():
                    time_str = datetime.fromtimestamp(row['time']/1000).strftime('%H:%M')
                    candle_type = "阳线" if row['close'] > row['open'] else "阴线"
                    amount_ok = "✅" if row['amount'] > amount_threshold else "❌"
                    ema50_ok = "✅" if not pd.isna(row['ema50']) and row['close'] > row['ema50'] else "❌"
                    ema50_str = f"{row['ema50']:.3f}" if not pd.isna(row['ema50']) else "N/A"
                    self.result_text.append(f"  {time_str}: 量比={row['volume_ratio']:.2f}, 成交量={row['volume']:.0f}, 成交额={row['amount']/10000:.0f}万{amount_ok}, {candle_type}, EMA50={ema50_str}{ema50_ok}")

        except Exception as e:
            QMessageBox.critical(self, '错误', f'调试分析时出错：{str(e)}')

    def get_trading_days(self, start_time, end_time):
        """获取交易日历"""
        try:
            trading_days_sh = xtdata.get_trading_dates(
                market='SH',
                start_time=start_time,
                end_time=end_time
            )

            trading_days_sz = xtdata.get_trading_dates(
                market='SZ',
                start_time=start_time,
                end_time=end_time
            )

            # 合并两个市场的交易日历并排序
            trading_days = sorted(set(trading_days_sh + trading_days_sz))
            return trading_days
        except Exception as e:
            print(f"获取交易日历时出错: {str(e)}")
            return []
            
    def download_history_data(self):
        """下载历史数据"""
        try:
            # 检查文件是否已选择
            if self.file_path.text() == '未选择文件':
                QMessageBox.warning(self, '警告', '请先选择通达信板块文件！')
                return
                
            # 读取股票代码
            stock_codes = self.stock_parser.read_stock_codes(self.file_path.text())
            if not stock_codes:
                QMessageBox.warning(self, '警告', '未能从文件中读取到有效的股票代码！')
                return
            
            # 计算日期范围（3天）
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=3)
            
            # 获取周期
            period_map = {
                '日线': '1d',
                '30分钟': '30m',
                '15分钟': '15m',
                '5分钟': '5m'
            }
            period = period_map[self.period_combo.currentText()]
            
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, len(stock_codes))
            self.progress_bar.setValue(0)
            self.download_btn.setEnabled(False)
            self.run_btn.setEnabled(False)
            
            # 清空结果显示
            self.result_text.clear()
            self.result_text.append("开始检查及下载历史数据...")
            
            # 下载数据
            success_count = 0
            failed_stocks = []
            download_count = 0  # 所有数据都是从服务器下载的

            for i, code in enumerate(stock_codes):
                try:
                    # 下载单个股票数据
                    data = self.download_data(
                        stock_list=[code],
                        period=period,
                        start_date=start_date.strftime('%Y%m%d'),
                        end_date=end_date.strftime('%Y%m%d')
                    )

                    if data and code in data:
                        success_count += 1
                        download_count += 1  # 所有数据都是从服务器下载的
                    else:
                        failed_stocks.append(code)

                except Exception as e:
                    print(f"处理 {code} 时出错: {str(e)}")
                    failed_stocks.append(code)

                # 更新进度条
                self.progress_bar.setValue(i + 1)
                QApplication.processEvents()

            # 显示下载结果
            self.result_text.append("\n处理完成！")
            self.result_text.append(f"成功处理：{success_count}只股票")
            self.result_text.append(f"服务器下载：{download_count}只")
            if failed_stocks:
                self.result_text.append(f"处理失败：{len(failed_stocks)}只股票")

            # 恢复按钮状态
            self.download_btn.setEnabled(True)
            self.run_btn.setEnabled(True)
            self.progress_bar.setVisible(False)

            QMessageBox.information(self, '完成',
                f'历史数据处理完成！\n成功：{success_count}只\n服务器下载：{download_count}只\n失败：{len(failed_stocks)}只')
            
        except Exception as e:
            self.download_btn.setEnabled(True)
            self.run_btn.setEnabled(True)
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, '错误', f'处理过程中出错：{str(e)}')

    def download_data(self, stock_list, period, start_date='', end_date=''):
        """
        下载股票数据，完全从服务器下载最新数据，不再判断本地数据时效性
        """
        try:
            if not stock_list:
                return {}

            print(f"\n开始从服务器下载 {len(stock_list)} 只股票的最新数据...")
            QApplication.processEvents()

            valid_data = {}

            # 直接下载所有股票的数据
            for code in stock_list:
                try:
                    print(f"\n正在下载 {code} 的数据...")
                    QApplication.processEvents()

                    # 强制从服务器下载最新数据
                    xtdata.download_history_data(
                        code,
                        period=period,
                        start_time=start_date,
                        end_time=end_date,
                        incrementally=True
                    )
                    print(f"下载完成")
                    time.sleep(0.1)  # 添加小延迟避免请求过快
                except Exception as e:
                    print(f"下载失败：{str(e)}")

            # 下载完成后获取数据
            print("\n正在获取下载后的数据...")
            QApplication.processEvents()

            downloaded_data = xtdata.get_market_data_ex(
                [],
                stock_list,
                period=period,
                start_time=start_date,
                end_time=end_date
            )

            # 验证下载后的数据
            if downloaded_data is not None:
                for code in stock_list:
                    if code in downloaded_data and not downloaded_data[code].empty:
                        valid_data[code] = downloaded_data[code].copy()
                        print(f"成功获取 {code} 的数据")
                    else:
                        print(f"获取 {code} 的数据失败")

            # 不再记录使用本地数据的股票，因为全部从服务器下载
            valid_data['_used_local_data'] = set()

            # 检查是否有有效数据
            if len(valid_data) > 1:  # 大于1是因为包含了_used_local_data
                return valid_data
            return {}

        except Exception as e:
            self.result_text.append(f"下载数据时出错：{str(e)}")
            return {}
            


def main():
    app = QApplication(sys.argv)
    window = VolumeStockFilter()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main() 