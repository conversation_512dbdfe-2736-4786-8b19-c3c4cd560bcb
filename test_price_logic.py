#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试价格获取逻辑的脚本
"""

from datetime import datetime, time

def test_trading_time_logic():
    """测试交易时间判断逻辑"""
    print("=== 测试交易时间判断逻辑 ===\n")
    
    # 测试不同时间点的判断
    test_times = [
        "09:00:00",  # 开盘前
        "09:30:00",  # 开盘
        "10:30:00",  # 上午交易时间
        "11:30:00",  # 上午收盘
        "12:00:00",  # 中午休市
        "13:00:00",  # 下午开盘
        "14:30:00",  # 下午交易时间
        "15:00:00",  # 收盘
        "15:30:00",  # 收盘后
        "20:00:00",  # 晚上
    ]
    
    for time_str in test_times:
        current_time = datetime.strptime(time_str, '%H:%M:%S').time()
        
        # 判断是否在交易时间内
        is_trading_time = (
            (datetime.strptime('09:30:00', '%H:%M:%S').time() <= current_time < datetime.strptime('11:30:00', '%H:%M:%S').time()) or
            (datetime.strptime('13:00:00', '%H:%M:%S').time() <= current_time < datetime.strptime('15:00:00', '%H:%M:%S').time())
        )
        
        # 判断时间段
        current_hour = datetime.strptime(time_str, '%H:%M:%S').hour
        current_minute = datetime.strptime(time_str, '%H:%M:%S').minute
        
        time_period = ""
        price_strategy = ""
        
        if is_trading_time:
            time_period = "交易时间"
            price_strategy = "使用实时行情或最新K线"
        elif current_hour > 15 or (current_hour == 15 and current_minute >= 0):
            time_period = "收盘后"
            price_strategy = "查找15:00收盘价"
        elif not is_trading_time and (11 <= current_hour < 13):
            time_period = "中午休市"
            price_strategy = "查找11:30价格"
        else:
            time_period = "其他时间"
            price_strategy = "使用最后一根K线"
        
        print(f"时间: {time_str} | 时间段: {time_period:8} | 策略: {price_strategy}")
    
    print("\n=== 修复前后对比 ===")
    print("修复前问题：中午12:00时被误判为'收盘后'，尝试查找15:00价格")
    print("修复后逻辑：中午12:00时正确判断为'中午休市'，查找11:30价格")
    print("\n修复前问题：中午时间查找15:00价格必然失败，因为15:00还没到")
    print("修复后逻辑：中午时间查找11:30价格，这是合理的上午收盘价")

def test_price_search_logic():
    """测试价格查找逻辑"""
    print("\n=== 测试价格查找逻辑 ===\n")
    
    # 模拟不同时间段的价格查找策略
    scenarios = [
        {
            "current_time": "12:00:00",
            "description": "中午休市时间",
            "expected_search": "11:30:00",
            "reason": "查找上午收盘价"
        },
        {
            "current_time": "15:30:00", 
            "description": "收盘后时间",
            "expected_search": "15:00:00",
            "reason": "查找当日收盘价"
        },
        {
            "current_time": "10:30:00",
            "description": "上午交易时间",
            "expected_search": "实时数据",
            "reason": "使用实时行情"
        },
        {
            "current_time": "14:00:00",
            "description": "下午交易时间", 
            "expected_search": "实时数据",
            "reason": "使用实时行情"
        }
    ]
    
    for scenario in scenarios:
        print(f"场景: {scenario['description']} ({scenario['current_time']})")
        print(f"  查找策略: {scenario['expected_search']}")
        print(f"  原因: {scenario['reason']}")
        print()

if __name__ == "__main__":
    test_trading_time_logic()
    test_price_search_logic()
