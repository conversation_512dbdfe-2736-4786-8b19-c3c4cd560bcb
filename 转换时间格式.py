#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
转换持仓记录中的时间格式
将旧的 'HH:MM:SS' 格式转换为 'YYYY-MM-DD HH:MM:SS' 格式
"""

import json
import os
from datetime import datetime

def convert_time_format(time_str, default_date="2025-01-29"):
    """
    转换时间格式
    Args:
        time_str: 原始时间字符串
        default_date: 默认日期（如果原始时间没有日期）
    Returns:
        转换后的时间字符串
    """
    if not time_str:
        return time_str
    
    # 如果已经包含日期，直接返回
    if len(time_str) > 8 and '-' in time_str:
        return time_str
    
    # 如果只有时间，添加默认日期
    if len(time_str) == 8 and ':' in time_str:
        return f"{default_date} {time_str}"
    
    return time_str

def convert_position_file(file_path, backup=True):
    """
    转换持仓记录文件中的时间格式
    Args:
        file_path: 文件路径
        backup: 是否创建备份
    """
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    print(f"正在处理文件: {file_path}")
    
    try:
        # 读取原始数据
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 创建备份
        if backup:
            backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"已创建备份文件: {backup_path}")
        
        # 转换时间格式
        converted_count = 0
        for code, position_info in data.items():
            # 转换买入时间
            if 'buy_time' in position_info:
                old_time = position_info['buy_time']
                new_time = convert_time_format(old_time)
                if old_time != new_time:
                    position_info['buy_time'] = new_time
                    converted_count += 1
                    print(f"  {code}: {old_time} -> {new_time}")
            
            # 转换其他可能的时间字段
            time_fields = ['sell_time', 'rebuy_time', 'last_add_time', 'temp_sell_time']
            for field in time_fields:
                if field in position_info:
                    old_time = position_info[field]
                    new_time = convert_time_format(old_time)
                    if old_time != new_time:
                        position_info[field] = new_time
                        print(f"  {code}.{field}: {old_time} -> {new_time}")
        
        # 保存转换后的数据
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"转换完成！共转换了 {converted_count} 条记录")
        return True
        
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return False

def convert_tech_sell_files():
    """转换技术指标卖出等待重新买回文件"""
    import glob
    
    # 查找所有技术指标卖出文件
    pattern = "技术指标卖出等待重新买回_*.json"
    files = glob.glob(pattern)
    
    for file_path in files:
        print(f"\n处理技术指标卖出文件: {file_path}")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            converted_count = 0
            for code, sell_info in data.items():
                # 转换卖出时间
                if 'sell_time' in sell_info:
                    old_time = sell_info['sell_time']
                    new_time = convert_time_format(old_time)
                    if old_time != new_time:
                        sell_info['sell_time'] = new_time
                        converted_count += 1
                        print(f"  {code}: {old_time} -> {new_time}")
                
                # 转换原始买入时间
                if 'original_buy_time' in sell_info:
                    old_time = sell_info['original_buy_time']
                    new_time = convert_time_format(old_time)
                    if old_time != new_time:
                        sell_info['original_buy_time'] = new_time
                        print(f"  {code}.original_buy_time: {old_time} -> {new_time}")
            
            # 保存转换后的数据
            if converted_count > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                print(f"  转换完成！共转换了 {converted_count} 条记录")
            else:
                print(f"  无需转换")
                
        except Exception as e:
            print(f"  转换失败: {str(e)}")

def main():
    """主函数"""
    print("=== 持仓记录时间格式转换工具 ===")
    print("将 'HH:MM:SS' 格式转换为 'YYYY-MM-DD HH:MM:SS' 格式")
    print()
    
    # 转换主要持仓记录文件
    position_files = [
        "可转债放量方法持仓记录.json",
        "自选股30m系统持仓记录.json",
        "自选股交易持仓记录.json"
    ]
    
    for file_path in position_files:
        if os.path.exists(file_path):
            print(f"\n{'='*50}")
            convert_position_file(file_path)
        else:
            print(f"文件不存在，跳过: {file_path}")
    
    # 转换技术指标卖出文件
    print(f"\n{'='*50}")
    print("转换技术指标卖出等待重新买回文件...")
    convert_tech_sell_files()
    
    print(f"\n{'='*50}")
    print("所有转换完成！")
    print("注意：备份文件已创建，如有问题可以恢复")

if __name__ == "__main__":
    main()
