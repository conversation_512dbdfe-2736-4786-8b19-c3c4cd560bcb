#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
涨停检查功能示例
演示如何使用日线数据准确判断涨停状态
"""

from datetime import datetime
from xtquant import xtdata

def check_limit_up_with_daily_data(code):
    """
    使用日线数据检查涨停状态
    
    Args:
        code: 股票代码
        
    Returns:
        tuple: (是否涨停, 涨停状态描述, 详细信息)
    """
    try:
        print(f"\n=== 检查 {code} 涨停状态 ===")
        
        # 获取最近2个交易日的日线数据
        daily_data = xtdata.get_market_data(
            field_list=['time', 'open', 'close', 'high', 'low'],
            stock_list=[code],
            period='1d',
            start_time='',
            end_time='',
            count=2  # 获取最近2天数据
        )

        if daily_data and 'close' in daily_data and code in daily_data['close'].index:
            closes = daily_data['close'].loc[code]
            
            if len(closes) >= 2:
                # 获取今日和昨日收盘价
                today_close = closes.iloc[-1]
                yesterday_close = closes.iloc[-2]
                
                print(f"昨日收盘价: {yesterday_close:.3f}")
                print(f"今日价格: {today_close:.3f}")

                if yesterday_close > 0:
                    # 计算涨幅
                    price_change_ratio = (today_close / yesterday_close - 1) * 100
                    price_change_amount = today_close - yesterday_close
                    
                    print(f"涨跌幅: {price_change_ratio:.2f}%")
                    print(f"涨跌额: {price_change_amount:.3f}")

                    # 判断是否涨停（涨幅在20%上下0.5%范围内）
                    if 19.5 <= price_change_ratio <= 20.5:
                        status = f"🔴 涨停 - 涨幅{price_change_ratio:.2f}%"
                        detail = {
                            'is_limit_up': True,
                            'change_ratio': price_change_ratio,
                            'change_amount': price_change_amount,
                            'today_price': today_close,
                            'yesterday_price': yesterday_close,
                            'data_source': '日线数据'
                        }
                        return True, status, detail
                    else:
                        status = f"⚪ 正常 - 涨幅{price_change_ratio:.2f}%"
                        detail = {
                            'is_limit_up': False,
                            'change_ratio': price_change_ratio,
                            'change_amount': price_change_amount,
                            'today_price': today_close,
                            'yesterday_price': yesterday_close,
                            'data_source': '日线数据'
                        }
                        return False, status, detail
                else:
                    return False, "❌ 昨日收盘价无效", None
            else:
                return False, f"❌ 日线数据不足，只有{len(closes)}条", None
        else:
            return False, "❌ 无法获取日线数据", None
            
    except Exception as e:
        return False, f"❌ 检查出错: {str(e)}", None

def simulate_profit_taking_decision(code, current_profit, profit_threshold):
    """
    模拟止盈决策过程
    
    Args:
        code: 股票代码
        current_profit: 当前盈利
        profit_threshold: 止盈阈值
        
    Returns:
        dict: 决策结果
    """
    print(f"\n=== 止盈决策模拟 ===")
    print(f"股票代码: {code}")
    print(f"当前盈利: {current_profit:.2f}元")
    print(f"止盈阈值: {profit_threshold}元")
    
    # 检查是否达到止盈条件
    if current_profit >= profit_threshold:
        print(f"✅ 达到止盈条件（盈利{current_profit:.2f}元 >= {profit_threshold}元）")
        
        # 检查涨停状态
        is_limit_up, status, detail = check_limit_up_with_daily_data(code)
        
        if is_limit_up:
            decision = {
                'action': 'HOLD',
                'reason': f'涨停状态，暂不卖出 - {status}',
                'limit_up_info': detail
            }
            print(f"🚫 决策：暂不卖出 - {decision['reason']}")
        else:
            decision = {
                'action': 'SELL',
                'reason': f'执行止盈卖出 - {status}',
                'limit_up_info': detail
            }
            print(f"💰 决策：执行止盈 - {decision['reason']}")
    else:
        decision = {
            'action': 'HOLD',
            'reason': f'未达到止盈条件（盈利{current_profit:.2f}元 < {profit_threshold}元）',
            'limit_up_info': None
        }
        print(f"⏳ 决策：继续持有 - {decision['reason']}")
    
    return decision

if __name__ == "__main__":
    # 测试股票代码
    test_codes = [
        "113050.SZ",  # 南银转债
        "110059.SH",  # 浦发转债
        "128136.SZ",  # 立讯转债
    ]
    
    print("=" * 60)
    print("涨停检查功能演示")
    print("=" * 60)
    
    for code in test_codes:
        # 检查涨停状态
        is_limit_up, status, detail = check_limit_up_with_daily_data(code)
        print(f"结果: {status}")
        
        # 模拟止盈决策
        simulate_profit_taking_decision(code, 450.0, 400.0)  # 假设盈利450元，止盈阈值400元
        
        print("-" * 60)
