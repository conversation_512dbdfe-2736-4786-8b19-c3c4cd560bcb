#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from datetime import datetime

def test_position_record_integrity():
    """测试持仓记录完整性"""
    print("🔍 测试持仓记录完整性...")
    
    position_file = "可转债放量方法持仓记录.json"
    if not os.path.exists(position_file):
        print("❌ 持仓记录文件不存在")
        return False
    
    try:
        with open(position_file, 'r', encoding='utf-8') as f:
            position_records = json.load(f)
        
        print(f"📊 总共 {len(position_records)} 条持仓记录")
        
        all_valid = True
        for code, info in position_records.items():
            print(f"\n🔍 检查 {code}:")
            
            # 检查新格式必需字段
            required_fields = ['buy_queue', 'total_quantity', 'total_cost', 'total_fee']
            for field in required_fields:
                if field in info:
                    print(f"   ✅ {field}: {info[field]}")
                else:
                    print(f"   ❌ 缺少字段: {field}")
                    all_valid = False
            
            # 检查buy_queue结构
            if 'buy_queue' in info and info['buy_queue']:
                buy_record = info['buy_queue'][0]
                buy_required = ['buy_price', 'buy_time', 'quantity', 'virtual']
                for field in buy_required:
                    if field in buy_record:
                        print(f"   ✅ buy_queue.{field}: {buy_record[field]}")
                    else:
                        print(f"   ❌ buy_queue缺少字段: {field}")
                        all_valid = False
            else:
                print(f"   ❌ buy_queue为空或不存在")
                all_valid = False
        
        return all_valid
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def simulate_key_functions():
    """模拟关键函数的执行"""
    print("\n🧪 模拟关键函数执行...")
    
    try:
        with open("可转债放量方法持仓记录.json", 'r', encoding='utf-8') as f:
            position_records = json.load(f)
        
        success_count = 0
        total_tests = 0
        
        for code, info in position_records.items():
            print(f"\n🧪 测试 {code}:")
            
            # 测试1: display_position逻辑
            total_tests += 1
            if 'buy_queue' in info and info['buy_queue']:
                buy_price = info['buy_queue'][0]['buy_price']
                print(f"   ✅ display_position: 买入价格 {buy_price}")
                success_count += 1
            else:
                print(f"   ❌ display_position: 格式异常")
            
            # 测试2: 交易汇总逻辑
            total_tests += 1
            if 'buy_queue' in info and info['buy_queue']:
                volume = info.get('total_quantity', 0)
                buy_price = info['buy_queue'][0]['buy_price']
                cost = buy_price * volume
                print(f"   ✅ 交易汇总: 成本 {cost:.2f}")
                success_count += 1
            else:
                print(f"   ❌ 交易汇总: 格式异常")
            
            # 测试3: 卖出条件分析
            total_tests += 1
            if 'buy_queue' in info and info['buy_queue']:
                buy_price = float(info['buy_queue'][0]['buy_price'])
                quantity = info.get('total_quantity', 10)
                # 模拟当前价格
                current_price = buy_price * 1.02  # 假设上涨2%
                profit_amount = (current_price - buy_price) * quantity
                print(f"   ✅ 卖出分析: 模拟盈利 {profit_amount:.2f}")
                success_count += 1
            else:
                print(f"   ❌ 卖出分析: 格式异常")
        
        print(f"\n📊 函数测试结果: {success_count}/{total_tests} 成功")
        return success_count == total_tests
        
    except Exception as e:
        print(f"❌ 函数测试失败: {e}")
        return False

def test_data_consistency():
    """测试数据一致性"""
    print("\n🔍 测试数据一致性...")
    
    try:
        with open("可转债放量方法持仓记录.json", 'r', encoding='utf-8') as f:
            position_records = json.load(f)
        
        all_consistent = True
        for code, info in position_records.items():
            if 'buy_queue' in info and info['buy_queue']:
                # 检查总量是否一致
                queue_quantity = sum(record.get('quantity', 0) for record in info['buy_queue'])
                total_quantity = info.get('total_quantity', 0)
                
                if queue_quantity == total_quantity:
                    print(f"   ✅ {code}: 数量一致 ({total_quantity})")
                else:
                    print(f"   ❌ {code}: 数量不一致 queue:{queue_quantity} vs total:{total_quantity}")
                    all_consistent = False
                
                # 检查成本是否合理
                if info.get('total_cost', 0) > 0:
                    print(f"   ✅ {code}: 总成本 {info['total_cost']:.2f}")
                else:
                    print(f"   ⚠️ {code}: 总成本为0（可能是CCI信号记录）")
        
        return all_consistent
        
    except Exception as e:
        print(f"❌ 一致性测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 系统完整性测试")
    print("=" * 60)
    
    # 测试1: 持仓记录完整性
    integrity_ok = test_position_record_integrity()
    
    # 测试2: 关键函数模拟
    functions_ok = simulate_key_functions()
    
    # 测试3: 数据一致性
    consistency_ok = test_data_consistency()
    
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    print(f"持仓记录完整性: {'✅ 通过' if integrity_ok else '❌ 失败'}")
    print(f"关键函数兼容性: {'✅ 通过' if functions_ok else '❌ 失败'}")
    print(f"数据一致性: {'✅ 通过' if consistency_ok else '❌ 失败'}")
    
    if integrity_ok and functions_ok and consistency_ok:
        print("\n🎉 所有测试通过！系统已完全兼容新格式")
        print("💡 建议：现在可以安全地启动交易系统")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
    
    print("=" * 60)
