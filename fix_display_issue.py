#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复显示问题的脚本
"""

import json
import os
from datetime import datetime

def check_connection_status():
    """检查连接状态"""
    print("=== 检查连接状态 ===")
    
    # 检查是否存在连接状态文件
    status_file = "connection_status.json"
    if os.path.exists(status_file):
        try:
            with open(status_file, 'r', encoding='utf-8') as f:
                status = json.load(f)
                print(f"连接状态: {status}")
                return status.get('connected', False)
        except Exception as e:
            print(f"读取连接状态文件失败: {str(e)}")
    
    print("未找到连接状态文件，假设未连接")
    return False

def fix_position_records():
    """修复持仓记录"""
    print("\n=== 修复持仓记录 ===")
    
    position_file = "自选股30m系统持仓记录.json"
    if not os.path.exists(position_file):
        print(f"持仓记录文件不存在: {position_file}")
        return False
    
    try:
        # 读取持仓记录
        with open(position_file, 'r', encoding='utf-8') as f:
            position_records = json.load(f)
        
        print(f"读取 {len(position_records)} 条持仓记录")
        
        # 备份原始文件
        backup_file = f"{position_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(position_records, f, ensure_ascii=False, indent=2)
        print(f"已备份原始文件: {backup_file}")
        
        # 修复持仓记录
        modified = False
        for code, info in position_records.items():
            if 'buy_queue' in info and info['buy_queue']:
                # 检查是否有real_trade标记
                first_buy = info['buy_queue'][0]
                if 'real_trade' not in first_buy and not first_buy.get('virtual', False):
                    # 添加real_trade标记
                    first_buy['real_trade'] = True
                    modified = True
                    print(f"修复 {code}: 添加real_trade=True标记")
        
        if modified:
            # 保存修复后的持仓记录
            with open(position_file, 'w', encoding='utf-8') as f:
                json.dump(position_records, f, ensure_ascii=False, indent=2)
            print("已保存修复后的持仓记录")
        else:
            print("持仓记录无需修复")
        
        return True
    except Exception as e:
        print(f"修复持仓记录失败: {str(e)}")
        return False

def create_connection_status(connected=False):
    """创建连接状态文件"""
    print("\n=== 创建连接状态文件 ===")
    
    status_file = "connection_status.json"
    status = {
        'connected': connected,
        'last_check': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    
    try:
        with open(status_file, 'w', encoding='utf-8') as f:
            json.dump(status, f, ensure_ascii=False, indent=2)
        print(f"已创建连接状态文件: {status_file}")
        print(f"连接状态: {'已连接' if connected else '未连接'}")
        return True
    except Exception as e:
        print(f"创建连接状态文件失败: {str(e)}")
        return False

def create_test_script():
    """创建测试脚本"""
    print("\n=== 创建测试脚本 ===")
    
    test_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
测试持仓显示的脚本
\"\"\"

import json
import os
from datetime import datetime

def main():
    \"\"\"主函数\"\"\"
    print("开始测试持仓显示...")
    
    # 检查连接状态
    status_file = "connection_status.json"
    if os.path.exists(status_file):
        try:
            with open(status_file, 'r', encoding='utf-8') as f:
                status = json.load(f)
                print(f"连接状态: {status}")
                connected = status.get('connected', False)
        except Exception as e:
            print(f"读取连接状态文件失败: {str(e)}")
            connected = False
    else:
        print("未找到连接状态文件，假设未连接")
        connected = False
    
    # 读取持仓记录
    position_file = "自选股30m系统持仓记录.json"
    if os.path.exists(position_file):
        try:
            with open(position_file, 'r', encoding='utf-8') as f:
                position_records = json.load(f)
            print(f"读取 {len(position_records)} 条持仓记录")
            
            # 分析持仓记录
            real_positions = 0
            virtual_positions = 0
            
            for code, info in position_records.items():
                if 'buy_queue' in info and info['buy_queue']:
                    first_buy = info['buy_queue'][0]
                    is_virtual = first_buy.get('virtual', False)
                    is_real_trade = first_buy.get('real_trade', False)
                    
                    if is_virtual:
                        virtual_positions += 1
                        print(f"{code}: 虚拟持仓")
                    elif is_real_trade or not is_virtual:
                        real_positions += 1
                        print(f"{code}: 实际持仓")
                else:
                    is_virtual = info.get('virtual', False)
                    if is_virtual:
                        virtual_positions += 1
                        print(f"{code}: 虚拟持仓 (旧格式)")
                    else:
                        real_positions += 1
                        print(f"{code}: 实际持仓 (旧格式)")
            
            print(f"\\n持仓统计:")
            print(f"  实际持仓: {real_positions}")
            print(f"  虚拟持仓: {virtual_positions}")
            print(f"  总持仓: {real_positions + virtual_positions}")
            
            # 模拟显示逻辑
            print(f"\\n模拟显示逻辑:")
            if connected:
                print("连接状态: 已连接，将查询服务器持仓")
                print("如果查询失败，应显示本地实际持仓")
                print(f"  应显示实际持仓: {real_positions}个")
            else:
                print("连接状态: 未连接，将显示本地持仓")
                if real_positions > 0:
                    print(f"  应显示实际持仓: {real_positions}个")
                else:
                    print(f"  应显示虚拟持仓: {virtual_positions}个")
        
        except Exception as e:
            print(f"读取持仓记录失败: {str(e)}")
    else:
        print(f"持仓记录文件不存在: {position_file}")

if __name__ == "__main__":
    main()
"""
    
    test_file = "test_position_display.py"
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_script)
        print(f"已创建测试脚本: {test_file}")
        return True
    except Exception as e:
        print(f"创建测试脚本失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("开始修复显示问题...")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查连接状态
    is_connected = check_connection_status()
    
    # 修复持仓记录
    fix_position_records()
    
    # 创建连接状态文件（设置为未连接，确保显示本地持仓）
    create_connection_status(connected=False)
    
    # 创建测试脚本
    create_test_script()
    
    print("\n修复完成，请运行测试脚本检查效果: python test_position_display.py")
    print("然后重新启动自选股交易程序")

if __name__ == "__main__":
    main()
