#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试收盘后价格更新与交易信号检查的股票范围一致性
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_position_records():
    """加载持仓记录"""
    try:
        with open('可转债放量方法持仓记录.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return {}

def load_tech_sell_waiting():
    """加载技术指标卖出等待重新买回记录"""
    try:
        with open('tech_sell_waiting_rebuy.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return {}

def load_waiting_for_exp3_break():
    """加载等待EXP3突破记录"""
    try:
        with open('waiting_for_exp3_break.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return {}

def load_bond_codes():
    """加载股票池"""
    try:
        with open('stock_pool.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get('bond_codes', [])
    except:
        return []

def get_all_display_stocks(position_records, tech_sell_waiting_rebuy, waiting_for_exp3_break, bond_codes):
    """获取所有需要显示的股票（模拟原函数逻辑）"""
    all_codes = set()
    
    # 1. 添加持仓记录中的股票
    for code in position_records.keys():
        if code.startswith(('11', '12', '15', '5')):
            all_codes.add(code)
    
    # 2. 添加技术指标卖出等待重新买回的股票
    for code in tech_sell_waiting_rebuy.keys():
        if code.startswith(('11', '12', '15', '5')):
            all_codes.add(code)
    
    # 3. 添加等待EXP3突破的股票
    for code in waiting_for_exp3_break.keys():
        if code.startswith(('11', '12', '15', '5')):
            all_codes.add(code)
    
    # 4. 添加股票池中的股票
    for code in bond_codes:
        if code.startswith(('11', '12', '15', '5')):
            all_codes.add(code)

    return list(all_codes)

def simulate_check_trading_signals_stock_range():
    """模拟 check_trading_signals 函数的股票范围逻辑"""
    print("=== 模拟交易信号检查的股票范围 ===")
    
    # 加载数据
    position_records = load_position_records()
    tech_sell_waiting_rebuy = load_tech_sell_waiting()
    waiting_for_exp3_break = load_waiting_for_exp3_break()
    bond_codes = load_bond_codes()
    
    # 使用可转债代码作为监控列表
    monitor_codes = bond_codes
    
    # 获取所有需要数据的股票
    all_display_codes = get_all_display_stocks(position_records, tech_sell_waiting_rebuy, waiting_for_exp3_break, bond_codes)
    
    # 合并股票池和所有显示的股票
    all_check_codes = list(set(monitor_codes + all_display_codes))
    
    # 分类统计
    position_only_codes = [code for code in all_display_codes if code not in monitor_codes]
    tech_sell_waiting_codes = [code for code in tech_sell_waiting_rebuy.keys() if code.startswith(('11', '12', '15', '5'))]
    waiting_break_codes = [code for code in waiting_for_exp3_break.keys() if code.startswith(('11', '12', '15', '5'))]
    cci_signal_codes = [code for code, info in position_records.items()
                       if code.startswith(('11', '12', '15', '5')) and info.get('is_signal_only', False)]
    all_waiting_break_codes = list(set(waiting_break_codes + cci_signal_codes))

    total_monitor_codes = len(monitor_codes)
    total_position_only = len(position_only_codes)
    total_tech_sell_waiting = len(tech_sell_waiting_codes)
    total_waiting_break = len(all_waiting_break_codes)
    total_all_codes = len(all_check_codes)

    print(f"股票池: {total_monitor_codes}只")
    print(f"持仓列表额外: {total_position_only}只")
    print(f"等待重新买回: {total_tech_sell_waiting}只")
    print(f"等待突破: {total_waiting_break}只")
    print(f"总计: {total_all_codes}只")
    print(f"完整股票列表: {sorted(all_check_codes)}")
    
    return all_check_codes

def simulate_closing_price_refresh_stock_range():
    """模拟收盘后价格刷新的股票范围逻辑（修改后）"""
    print("\n=== 模拟收盘后价格刷新的股票范围（修改后） ===")
    
    # 这个逻辑现在与 check_trading_signals 完全相同
    return simulate_check_trading_signals_stock_range()

def simulate_old_closing_price_refresh():
    """模拟原来的收盘后价格刷新逻辑（仅持仓记录）"""
    print("\n=== 模拟原来的收盘后价格刷新逻辑（仅持仓记录） ===")
    
    position_records = load_position_records()
    position_codes = [code for code in position_records.keys() if code.startswith(('11', '12', '15', '5'))]
    
    print(f"仅持仓记录: {len(position_codes)}只")
    print(f"持仓股票列表: {sorted(position_codes)}")
    
    return position_codes

def main():
    print("测试收盘后价格更新与交易信号检查的股票范围一致性")
    print("=" * 60)
    
    # 模拟交易信号检查的股票范围
    trading_signals_codes = simulate_check_trading_signals_stock_range()
    
    # 模拟修改后的收盘后价格刷新股票范围
    closing_refresh_codes = simulate_closing_price_refresh_stock_range()
    
    # 模拟原来的收盘后价格刷新逻辑
    old_closing_codes = simulate_old_closing_price_refresh()
    
    print("\n=== 一致性检查 ===")
    
    # 检查修改后的一致性
    if set(trading_signals_codes) == set(closing_refresh_codes):
        print("✅ 修改后：交易信号检查与收盘后价格刷新的股票范围完全一致")
    else:
        print("❌ 修改后：仍然存在不一致")
        diff1 = set(trading_signals_codes) - set(closing_refresh_codes)
        diff2 = set(closing_refresh_codes) - set(trading_signals_codes)
        if diff1:
            print(f"   交易信号检查多出: {sorted(diff1)}")
        if diff2:
            print(f"   收盘后刷新多出: {sorted(diff2)}")
    
    # 检查与原来逻辑的差异
    print(f"\n📊 原来的收盘后刷新缺少的股票数量: {len(set(trading_signals_codes) - set(old_closing_codes))}只")
    missing_codes = sorted(set(trading_signals_codes) - set(old_closing_codes))
    if missing_codes:
        print(f"   缺少的股票: {missing_codes}")
    
    print(f"\n📈 修改后新增的价格更新股票数量: {len(set(closing_refresh_codes) - set(old_closing_codes))}只")

if __name__ == "__main__":
    main()
