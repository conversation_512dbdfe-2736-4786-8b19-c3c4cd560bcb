#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交易记录恢复工具 - GUI版本
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import os
import json
import threading
from datetime import datetime, timedelta
from 从日志恢复交易记录 import restore_trading_records_advanced, verify_restored_records

class TradingRecoveryTool:
    def __init__(self, root):
        self.root = root
        self.root.title("交易记录恢复工具")
        self.root.geometry("800x600")
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="交易记录恢复工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 说明文本
        info_text = """
此工具可以从交易日志文件中恢复近期的交易记录。

功能特点：
• 自动扫描多个日期的交易日志文件
• 智能匹配买入和卖出记录（支持跨日交易）
• 自动备份现有交易历史文件
• 生成完整的交易统计信息
• 验证恢复数据的完整性

使用说明：
1. 确保 log/ 目录下有相应的交易日志文件
2. 点击"开始恢复"按钮
3. 等待恢复完成，查看结果报告
        """
        
        info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
        info_label.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # 操作按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=(0, 20))
        
        # 开始恢复按钮
        self.restore_btn = ttk.Button(button_frame, text="开始恢复交易记录", 
                                     command=self.start_recovery, style="Accent.TButton")
        self.restore_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 验证记录按钮
        self.verify_btn = ttk.Button(button_frame, text="验证已恢复记录", 
                                    command=self.verify_records)
        self.verify_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 查看日志按钮
        self.view_logs_btn = ttk.Button(button_frame, text="查看日志文件", 
                                       command=self.view_log_files)
        self.view_logs_btn.pack(side=tk.LEFT)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="准备就绪", foreground="green")
        self.status_label.grid(row=4, column=0, columnspan=2, pady=(0, 10))
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="恢复结果", padding="10")
        result_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 结果文本框
        self.result_text = scrolledtext.ScrolledText(result_frame, height=15, width=80)
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
    def log_message(self, message):
        """在结果区域显示消息"""
        self.result_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] {message}\n")
        self.result_text.see(tk.END)
        self.root.update_idletasks()
        
    def start_recovery(self):
        """开始恢复交易记录"""
        # 清空结果区域
        self.result_text.delete(1.0, tk.END)
        
        # 禁用按钮
        self.restore_btn.config(state='disabled')
        self.verify_btn.config(state='disabled')
        
        # 开始进度条
        self.progress.start()
        self.status_label.config(text="正在恢复交易记录...", foreground="blue")
        
        # 在后台线程中执行恢复
        def recovery_thread():
            try:
                self.log_message("=== 开始恢复交易记录 ===")
                
                # 检查日志文件
                log_dir = "log"
                if not os.path.exists(log_dir):
                    self.log_message("错误: log/ 目录不存在")
                    return
                
                log_files = [f for f in os.listdir(log_dir) if f.startswith('trading_log_') and f.endswith('.txt')]
                if not log_files:
                    self.log_message("错误: 没有找到交易日志文件")
                    return
                
                self.log_message(f"找到 {len(log_files)} 个日志文件")
                
                # 执行恢复
                restored_count = restore_trading_records_advanced()
                
                if restored_count > 0:
                    self.log_message(f"\n✅ 成功恢复了 {restored_count} 个交易日的记录")
                    
                    # 显示恢复的文件
                    self.log_message("\n恢复的文件:")
                    today = datetime.now()
                    for i in range(3):
                        check_date = today - timedelta(days=i)
                        if check_date.weekday() < 5:  # 工作日
                            date_str = check_date.strftime('%Y%m%d')
                            history_file = f"交易历史_{date_str}.json"
                            if os.path.exists(history_file):
                                try:
                                    with open(history_file, 'r', encoding='utf-8') as f:
                                        trades = json.load(f)
                                    total_profit = sum(trade.get('profit', 0) for trade in trades)
                                    self.log_message(f"  📅 {date_str}: {len(trades)} 条记录, 总盈亏: {total_profit:.2f}元")
                                except:
                                    self.log_message(f"  📅 {date_str}: 文件读取失败")
                    
                    self.log_message("\n建议使用'验证已恢复记录'功能检查数据完整性")
                else:
                    self.log_message("❌ 没有成功恢复任何交易记录")
                
            except Exception as e:
                self.log_message(f"恢复过程中发生错误: {str(e)}")
            
            finally:
                # 恢复UI状态
                self.root.after(0, self.recovery_finished)
        
        # 启动后台线程
        threading.Thread(target=recovery_thread, daemon=True).start()
        
    def recovery_finished(self):
        """恢复完成后的UI更新"""
        self.progress.stop()
        self.status_label.config(text="恢复完成", foreground="green")
        self.restore_btn.config(state='normal')
        self.verify_btn.config(state='normal')
        
    def verify_records(self):
        """验证已恢复的记录"""
        self.result_text.delete(1.0, tk.END)
        self.log_message("=== 验证已恢复的交易记录 ===")
        
        try:
            # 获取近三天的工作日
            today = datetime.now()
            verified_count = 0
            
            for i in range(7):  # 检查更多天数
                check_date = today - timedelta(days=i)
                if check_date.weekday() < 5:  # 工作日
                    date_str = check_date.strftime('%Y%m%d')
                    history_file = f"交易历史_{date_str}.json"
                    
                    if os.path.exists(history_file):
                        try:
                            with open(history_file, 'r', encoding='utf-8') as f:
                                trades = json.load(f)
                            
                            self.log_message(f"\n📅 {date_str} ({check_date.strftime('%Y-%m-%d')}):")
                            self.log_message(f"  交易记录数: {len(trades)}")
                            
                            if trades:
                                total_profit = sum(trade.get('profit', 0) for trade in trades)
                                profit_trades = sum(1 for trade in trades if trade.get('profit', 0) > 0)
                                loss_trades = len(trades) - profit_trades
                                win_rate = (profit_trades / len(trades) * 100) if len(trades) > 0 else 0
                                
                                self.log_message(f"  总盈亏: {total_profit:.2f}元")
                                self.log_message(f"  盈利交易: {profit_trades}次, 亏损交易: {loss_trades}次")
                                self.log_message(f"  胜率: {win_rate:.1f}%")
                                
                                # 显示前几条记录作为样本
                                self.log_message("  样本记录:")
                                for j, trade in enumerate(trades[:3]):
                                    buy_time = trade.get('buy_time', 'N/A')
                                    sell_time = trade.get('sell_time', 'N/A')
                                    self.log_message(f"    {j+1}. {trade.get('code', 'N/A')} "
                                                   f"买入@{trade.get('buy_price', 0):.3f} "
                                                   f"卖出@{trade.get('sell_price', 0):.3f} "
                                                   f"盈亏:{trade.get('profit', 0):.2f}元")
                                    self.log_message(f"       买入时间: {buy_time}")
                                    self.log_message(f"       卖出时间: {sell_time}")
                                
                                verified_count += 1
                            else:
                                self.log_message("  无交易记录")
                                
                        except Exception as e:
                            self.log_message(f"  读取文件失败: {str(e)}")
                    else:
                        self.log_message(f"\n📅 {date_str}: 文件不存在")
                        
                    if verified_count >= 3:  # 只验证最近3个工作日
                        break
            
            if verified_count > 0:
                self.log_message(f"\n✅ 验证完成，共检查了 {verified_count} 个交易日的记录")
            else:
                self.log_message("\n❌ 没有找到任何交易记录文件")
                
        except Exception as e:
            self.log_message(f"验证过程中发生错误: {str(e)}")
            
    def view_log_files(self):
        """查看可用的日志文件"""
        self.result_text.delete(1.0, tk.END)
        self.log_message("=== 可用的交易日志文件 ===")
        
        log_dir = "log"
        if not os.path.exists(log_dir):
            self.log_message("错误: log/ 目录不存在")
            return
        
        log_files = [f for f in os.listdir(log_dir) if f.startswith('trading_log_') and f.endswith('.txt')]
        
        if not log_files:
            self.log_message("没有找到交易日志文件")
            return
        
        log_files.sort(reverse=True)  # 按日期倒序排列
        
        self.log_message(f"找到 {len(log_files)} 个日志文件:")
        
        for log_file in log_files:
            file_path = os.path.join(log_dir, log_file)
            try:
                file_size = os.path.getsize(file_path)
                file_size_mb = file_size / (1024 * 1024)
                
                # 提取日期
                date_str = log_file.replace('trading_log_', '').replace('.txt', '')
                try:
                    date_obj = datetime.strptime(date_str, '%Y%m%d')
                    date_display = date_obj.strftime('%Y-%m-%d (%A)')
                except:
                    date_display = date_str
                
                self.log_message(f"  📄 {log_file}")
                self.log_message(f"      日期: {date_display}")
                self.log_message(f"      大小: {file_size_mb:.2f} MB")
                
            except Exception as e:
                self.log_message(f"  📄 {log_file} (读取信息失败: {str(e)})")

def main():
    root = tk.Tk()
    app = TradingRecoveryTool(root)
    root.mainloop()

if __name__ == "__main__":
    main()
