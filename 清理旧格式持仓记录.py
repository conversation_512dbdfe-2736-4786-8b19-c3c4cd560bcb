#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理旧格式持仓记录脚本
将所有旧格式的持仓记录转换为新格式的队列结构
"""

import json
import os
import time
from datetime import datetime

def convert_old_format_to_new(position_data):
    """将旧格式持仓记录转换为新格式"""
    # 检查是否是旧格式（有buy_price和quantity，但没有buy_queue）
    if ('buy_price' in position_data and 
        'quantity' in position_data and 
        'buy_queue' not in position_data):
        
        print(f"  发现旧格式记录，转换中...")
        
        # 提取旧格式数据
        old_buy_price = position_data['buy_price']
        old_quantity = position_data['quantity']
        old_buy_time = position_data.get('buy_time', datetime.now().strftime('%H:%M:%S'))
        old_fee = position_data.get('fee', old_buy_price * old_quantity * 0.0001)
        old_virtual = position_data.get('virtual', False)
        old_order_id = position_data.get('order_id', f"CONVERTED_{int(time.time())}")
        
        # 创建新格式的买入记录
        buy_record = {
            'buy_price': float(old_buy_price),
            'buy_time': old_buy_time,
            'quantity': int(old_quantity),
            'fee': float(old_fee),
            'actual_amount': float(old_buy_price * old_quantity),
            'order_id': old_order_id,
            'virtual': bool(old_virtual),
            'below_exp3_at_buy': position_data.get('below_exp3_at_buy', False),
            'crossed_exp3': position_data.get('crossed_exp3', False)
        }
        
        # 保留服务器相关数据
        if position_data.get('real_trade'):
            buy_record['real_trade'] = position_data['real_trade']
        if position_data.get('server_profit') is not None:
            buy_record['server_profit'] = position_data['server_profit']
        if position_data.get('server_market_value') is not None:
            buy_record['server_market_value'] = position_data['server_market_value']
        if position_data.get('server_current_price') is not None:
            buy_record['server_current_price'] = position_data['server_current_price']
        if position_data.get('use_server_data'):
            buy_record['use_server_data'] = position_data['use_server_data']
        
        # 创建新格式的持仓记录
        new_position_data = {
            'buy_queue': [buy_record],
            'total_quantity': int(old_quantity),
            'total_cost': float(old_buy_price * old_quantity),
            'total_fee': float(old_fee)
        }
        
        # 保留其他字段（如last_price等）
        for key, value in position_data.items():
            if key not in ['buy_price', 'quantity', 'buy_time', 'fee', 'virtual', 
                          'order_id', 'below_exp3_at_buy', 'crossed_exp3', 'real_trade',
                          'server_profit', 'server_market_value', 'server_current_price',
                          'use_server_data', 'actual_amount']:
                new_position_data[key] = value
        
        return new_position_data
    
    # 如果已经是新格式，直接返回
    return position_data

def clean_position_file(file_path):
    """清理单个持仓记录文件"""
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    print(f"处理文件: {file_path}")
    
    try:
        # 读取原文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content:
                print("  文件为空，跳过")
                return True
            
            position_records = json.loads(content)
        
        # 备份原文件
        backup_file = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(position_records, f, ensure_ascii=False, indent=2)
        print(f"  已备份到: {backup_file}")
        
        # 转换格式
        converted_count = 0
        for code, position_data in position_records.items():
            old_format = ('buy_price' in position_data and 
                         'quantity' in position_data and 
                         'buy_queue' not in position_data)
            
            if old_format:
                print(f"  转换 {code} 从旧格式到新格式")
                position_records[code] = convert_old_format_to_new(position_data)
                converted_count += 1
        
        if converted_count > 0:
            # 保存转换后的文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(position_records, f, ensure_ascii=False, indent=2)
            print(f"  转换完成，共转换 {converted_count} 条记录")
        else:
            print("  没有发现需要转换的旧格式记录")
        
        return True
        
    except Exception as e:
        print(f"  处理文件失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("开始清理旧格式持仓记录...")
    
    # 需要处理的文件列表
    files_to_clean = [
        "自选股30m系统持仓记录.json",
        "可转债放量方法持仓记录.json"
    ]
    
    # 查找其他可能的持仓记录文件
    import glob
    additional_files = glob.glob("*持仓记录*.json")
    for file_path in additional_files:
        if file_path not in files_to_clean:
            files_to_clean.append(file_path)
    
    print(f"发现 {len(files_to_clean)} 个持仓记录文件")
    
    success_count = 0
    for file_path in files_to_clean:
        if clean_position_file(file_path):
            success_count += 1
        print()  # 空行分隔
    
    print(f"清理完成！成功处理 {success_count}/{len(files_to_clean)} 个文件")

if __name__ == "__main__":
    main()
