#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试涨停检查功能 - 优先使用日线数据
"""

import sys
import os
from datetime import datetime
from xtquant import xtdata

def test_limit_up_check():
    """测试涨停检查功能"""
    
    def check_limit_up_status(code):
        """
        检查股票是否涨停

        Args:
            code: 股票代码

        Returns:
            tuple: (是否涨停, 涨停状态描述)
        """
        try:
            print(f"股票代码: {code}")

            # 先尝试获取实时tick数据
            try:
                tick_data = xtdata.get_full_tick([code])
                if tick_data and code in tick_data:
                    tick = tick_data[code]
                    last_price = tick.get('lastPrice', 0)
                    last_close = tick.get('lastClose', 0)  # 前收盘价

                    print(f"实时数据 - 最新价: {last_price}, 前收盘价: {last_close}")

                    if last_close and last_close > 0 and last_price > 0:
                        price_change_ratio = (last_price / last_close - 1) * 100
                        print(f"实时涨幅: {price_change_ratio:.2f}%")

                        # 判断是否涨停（涨幅在20%上下0.5%范围内）
                        if 19.5 <= price_change_ratio <= 20.5:
                            return True, f"涨幅{price_change_ratio:.2f}%"
                        else:
                            return False, f"涨幅{price_change_ratio:.2f}%"
            except Exception as tick_e:
                print(f"获取实时数据失败: {str(tick_e)}，尝试使用日线数据")

            # 如果实时数据获取失败，使用日线数据
            try:
                # 获取最近2个交易日的日线数据（今天和昨天）
                daily_data = xtdata.get_market_data(
                    field_list=['time', 'open', 'close', 'high', 'low'],
                    stock_list=[code],
                    period='1d',
                    start_time='',
                    end_time='',
                    count=2  # 获取最近2天数据
                )

                if daily_data and code in daily_data['close'].index and len(daily_data['close'].loc[code]) >= 2:
                    closes = daily_data['close'].loc[code]

                    # 获取今日收盘价（最新价）和昨日收盘价
                    today_close = closes.iloc[-1]  # 今日收盘价（或当前价）
                    yesterday_close = closes.iloc[-2]  # 昨日收盘价

                    print(f"日线数据 - 今日收盘: {today_close}, 昨日收盘: {yesterday_close}")

                    if yesterday_close > 0:
                        price_change_ratio = (today_close / yesterday_close - 1) * 100
                        print(f"日线涨幅: {price_change_ratio:.2f}%")

                        # 判断是否涨停（涨幅在20%上下0.5%范围内）
                        if 19.5 <= price_change_ratio <= 20.5:
                            return True, f"涨幅{price_change_ratio:.2f}%"
                        else:
                            return False, f"涨幅{price_change_ratio:.2f}%"
                    else:
                        return False, "昨日收盘价无效"
                else:
                    return False, "无法获取日线数据"

            except Exception as daily_e:
                print(f"获取日线数据失败: {str(daily_e)}")
                return False, f"获取日线数据失败: {str(daily_e)}"

        except Exception as e:
            print(f"检查{code}涨停状态时出错: {str(e)}")
            return False, f"检查出错: {str(e)}"
    
    # 测试一些可转债代码
    test_codes = [
        "113050.SZ",  # 南银转债
        "110059.SH",  # 浦发转债
        "113008.SZ",  # 电气转债
        "128136.SZ",  # 立讯转债
        "110053.SH"   # 苏银转债
    ]
    
    print("=" * 50)
    print("测试涨停检查功能")
    print("=" * 50)
    
    for code in test_codes:
        print(f"\n检查 {code}:")
        is_limit_up, status = check_limit_up_status(code)
        
        if is_limit_up:
            print(f"✅ 涨停状态: {status}")
        else:
            print(f"❌ 非涨停: {status}")
        
        print("-" * 30)

if __name__ == "__main__":
    test_limit_up_check()
