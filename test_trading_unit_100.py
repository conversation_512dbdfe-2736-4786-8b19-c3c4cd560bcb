#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易单位调整为100股的脚本
"""

def test_buy_quantity_calculation():
    """测试买入数量计算"""
    print("=== 测试买入数量计算（100股为基数） ===")
    
    target_amount = 10000  # 目标金额
    
    test_prices = [
        (1.0, "低价股"),
        (5.0, "中低价股"),
        (10.0, "中价股"),
        (50.0, "高价股"),
        (100.0, "很高价股"),
        (200.0, "超高价股")
    ]
    
    for price, desc in test_prices:
        # 修改后的计算逻辑
        quantity = int((target_amount / price) // 100) * 100
        
        # 确保至少买入100股
        if quantity < 100:
            quantity = 100
        
        actual_amount = price * quantity
        
        print(f"{desc} - 价格: {price:6.2f}, 数量: {quantity:4d}股, 实际金额: {actual_amount:8.2f}元")

def test_default_quantity_values():
    """测试默认数量值"""
    print("\n=== 测试默认数量值 ===")
    
    # 模拟各种情况下的默认数量
    test_cases = [
        ("持仓记录默认数量", 100),
        ("保护性卖出默认数量", 100),
        ("测试委托数量", 100),
        ("持仓查询默认数量", 100),
        ("同步持仓默认数量", 100)
    ]
    
    for case_name, expected_quantity in test_cases:
        print(f"{case_name}: {expected_quantity}股 ✅")

def test_trading_scenarios():
    """测试交易场景"""
    print("\n=== 测试交易场景 ===")
    
    # 场景1: ETF交易
    print("场景1: ETF交易 (513920.SH)")
    price = 1.523
    target_amount = 10000
    quantity = int((target_amount / price) // 100) * 100
    if quantity < 100:
        quantity = 100
    
    actual_amount = price * quantity
    print(f"  价格: {price}, 计算数量: {quantity}股, 实际金额: {actual_amount:.2f}元")
    
    # 场景2: 可转债交易
    print("\n场景2: 可转债交易")
    price = 120.5
    quantity = int((target_amount / price) // 100) * 100
    if quantity < 100:
        quantity = 100
    
    actual_amount = price * quantity
    print(f"  价格: {price}, 计算数量: {quantity}股, 实际金额: {actual_amount:.2f}元")
    
    # 场景3: 高价股
    print("\n场景3: 高价股")
    price = 300.0
    quantity = int((target_amount / price) // 100) * 100
    if quantity < 100:
        quantity = 100
    
    actual_amount = price * quantity
    print(f"  价格: {price}, 计算数量: {quantity}股, 实际金额: {actual_amount:.2f}元")

def test_comparison_old_vs_new():
    """对比旧单位(10股)和新单位(100股)的差异"""
    print("\n=== 对比旧单位(10股)和新单位(100股) ===")
    
    target_amount = 10000
    test_prices = [1.0, 5.0, 10.0, 50.0, 100.0]
    
    print(f"{'价格':>8} {'旧单位(10股)':>12} {'新单位(100股)':>13} {'金额差异':>10}")
    print("-" * 50)
    
    for price in test_prices:
        # 旧逻辑 (10股为基数)
        old_quantity = int((target_amount / price) // 10) * 10
        if old_quantity < 10:
            old_quantity = 10
        old_amount = price * old_quantity
        
        # 新逻辑 (100股为基数)
        new_quantity = int((target_amount / price) // 100) * 100
        if new_quantity < 100:
            new_quantity = 100
        new_amount = price * new_quantity
        
        amount_diff = new_amount - old_amount
        
        print(f"{price:8.2f} {old_quantity:8d}股 {new_quantity:9d}股 {amount_diff:+8.2f}元")

def main():
    """主测试函数"""
    print("开始测试交易单位调整为100股...")
    
    try:
        test_buy_quantity_calculation()
        test_default_quantity_values()
        test_trading_scenarios()
        test_comparison_old_vs_new()
        
        print("\n=== 修改总结 ===")
        print("✅ 买入数量计算：从10股基数改为100股基数")
        print("✅ 最小买入数量：从10股改为100股")
        print("✅ 默认持仓数量：从10股改为100股")
        print("✅ 测试委托数量：从10股改为100股")
        print("✅ 所有相关默认值都已更新为100股")
        
        print("\n=== 影响分析 ===")
        print("1. 交易规模增大：每次交易的股数是原来的10倍")
        print("2. 资金使用增加：每次交易需要更多资金")
        print("3. 手续费影响：绝对金额增加，但比例保持不变")
        print("4. 风险控制：单笔交易风险增大，需要相应调整策略")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    main()
