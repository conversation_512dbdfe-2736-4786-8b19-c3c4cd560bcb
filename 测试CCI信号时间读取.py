#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试CCI信号时间读取修复
"""

import json
import os
from datetime import datetime

def test_signal_time_reading():
    """测试CCI信号记录的时间读取逻辑"""
    
    print("=== 测试CCI信号时间读取修复 ===\n")
    
    # 检查当前持仓记录
    position_file = "可转债放量方法持仓记录.json"
    if os.path.exists(position_file):
        print(f"📁 读取持仓记录文件: {position_file}")
        with open(position_file, 'r', encoding='utf-8') as f:
            position_records = json.load(f)
        
        print(f"📊 总持仓记录数: {len(position_records)}")
        
        # 分析每个记录的时间信息
        for code, position_info in position_records.items():
            print(f"\n🔍 分析 {code}:")
            print(f"   is_signal_only: {position_info.get('is_signal_only', False)}")
            print(f"   is_cci_signal: {position_info.get('is_cci_signal', False)}")
            
            # 测试新的时间读取逻辑
            buy_time_str = ''
            
            # 优先从新格式中获取时间信息
            if 'buy_queue' in position_info and position_info['buy_queue']:
                buy_time_str = position_info['buy_queue'][0].get('buy_time', '')
                print(f"   从buy_queue获取时间: {buy_time_str}")
            
            # 如果新格式没有，尝试从旧格式获取
            if not buy_time_str:
                buy_time_str = position_info.get('buy_time', '')
                print(f"   从旧格式获取时间: {buy_time_str}")
            
            if buy_time_str:
                try:
                    # 测试时间解析
                    if len(buy_time_str) > 10:  # 包含日期和时间
                        signal_time = datetime.strptime(buy_time_str, '%Y-%m-%d %H:%M:%S')
                        print(f"   ✅ 时间解析成功: {signal_time}")
                        
                        # 计算天数差
                        current_time = datetime.now()
                        time_diff = current_time - signal_time
                        days_passed = time_diff.days
                        print(f"   📅 信号已存在 {days_passed} 天")
                        
                        if days_passed > 2:
                            print(f"   🕒 信号已过期，应被清理")
                        elif days_passed >= 1:
                            print(f"   ⏰ 信号存在1天以上，明天将被清理")
                        else:
                            print(f"   ✅ 信号仍然有效")
                            
                    else:
                        print(f"   ⚠️ 时间格式不完整: {buy_time_str}")
                        
                except ValueError as e:
                    print(f"   ❌ 时间解析失败: {e}")
            else:
                print(f"   ❌ 缺少时间信息")
    else:
        print(f"❌ 持仓记录文件不存在: {position_file}")
    
    print("\n=== 检查昨天的CCI信号记录 ===")
    
    # 检查昨天的日志，看看CCI信号是如何记录的
    yesterday = datetime.now().replace(day=31).strftime('%Y%m%d')  # 07-31
    log_file = f"log/trading_log_{yesterday}.txt"
    
    if os.path.exists(log_file):
        print(f"📁 检查昨天的日志文件: {log_file}")
        
        # 搜索CCI信号创建记录
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        signal_creation_lines = []
        for i, line in enumerate(lines):
            if "创建CCI信号记录" in line or "CCI上穿-100买入信号（仅记录" in line:
                signal_creation_lines.append((i+1, line.strip()))
        
        print(f"📊 找到 {len(signal_creation_lines)} 条CCI信号创建记录:")
        for line_num, line in signal_creation_lines[-10:]:  # 显示最后10条
            print(f"   第{line_num}行: {line}")
    else:
        print(f"❌ 昨天的日志文件不存在: {log_file}")

if __name__ == "__main__":
    test_signal_time_reading()
