#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复买入价格访问问题脚本
将所有直接访问buy_price字段的代码修改为兼容新旧格式的访问方式
"""

import os
import re

def fix_buy_price_access(file_path):
    """修复单个文件中的买入价格访问问题"""
    if not os.path.exists(file_path):
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_file = f"{file_path}.backup_buy_price_fix"
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"已备份到: {backup_file}")
        
        # 定义需要替换的模式和对应的替换内容
        replacements = [
            # position_info['buy_price'] -> 兼容新旧格式的获取方式
            (
                r"(\s+)buy_price\s*=\s*position_info\[['\"']buy_price['\"']\]",
                r"\1# 兼容新旧格式获取买入价格\n\1if 'buy_queue' in position_info and position_info['buy_queue']:\n\1    buy_price = position_info['buy_queue'][0]['buy_price']\n\1else:\n\1    buy_price = position_info.get('buy_price', 0)"
            ),
            
            # position_info.get('buy_price') -> 兼容新旧格式的获取方式（如果前面没有新格式检查）
            (
                r"(\s+)buy_price\s*=\s*position_info\.get\(['\"']buy_price['\"']\)",
                r"\1# 兼容新旧格式获取买入价格\n\1if 'buy_queue' in position_info and position_info['buy_queue']:\n\1    buy_price = position_info['buy_queue'][0]['buy_price']\n\1else:\n\1    buy_price = position_info.get('buy_price')"
            ),
            
            # 类似的模式，但针对其他变量名
            (
                r"(\s+)buy_price\s*=\s*info\[['\"']buy_price['\"']\]",
                r"\1# 兼容新旧格式获取买入价格\n\1if 'buy_queue' in info and info['buy_queue']:\n\1    buy_price = info['buy_queue'][0]['buy_price']\n\1else:\n\1    buy_price = info.get('buy_price', 0)"
            ),
            
            (
                r"(\s+)buy_price\s*=\s*info\.get\(['\"']buy_price['\"']\)",
                r"\1# 兼容新旧格式获取买入价格\n\1if 'buy_queue' in info and info['buy_queue']:\n\1    buy_price = info['buy_queue'][0]['buy_price']\n\1else:\n\1    buy_price = info.get('buy_price')"
            ),
            
            # 同时获取quantity的情况
            (
                r"(\s+)quantity\s*=\s*position_info\.get\(['\"']quantity['\"'],\s*(\d+)\)",
                r"\1# 兼容新旧格式获取数量\n\1if 'buy_queue' in position_info and position_info['buy_queue']:\n\1    quantity = position_info.get('total_quantity', \2)\n\1else:\n\1    quantity = position_info.get('quantity', \2)"
            ),
        ]
        
        modified = False
        for pattern, replacement in replacements:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                modified = True
                print(f"  应用替换模式: {pattern[:50]}...")
        
        if modified:
            # 保存修改后的文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  已修复文件: {file_path}")
            return True
        else:
            print(f"  未发现需要修复的模式: {file_path}")
            # 删除不必要的备份文件
            os.remove(backup_file)
            return True
            
    except Exception as e:
        print(f"修复文件 {file_path} 时出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复买入价格访问问题...")
    
    # 需要修复的文件列表
    files_to_fix = [
        "可转债做T交易-cci.py",
        "可转债做T交易-成交额.py"
    ]
    
    success_count = 0
    for file_path in files_to_fix:
        print(f"\n📁 修复文件: {file_path}")
        if fix_buy_price_access(file_path):
            success_count += 1
    
    print(f"\n🏁 修复完成！成功处理 {success_count}/{len(files_to_fix)} 个文件")

if __name__ == "__main__":
    main()
