#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 volume 字段修复的脚本
"""

def test_trade_data_parsing():
    """测试成交回报数据解析"""
    print("测试成交回报数据解析...")
    
    # 模拟不同格式的成交回报数据
    test_trades = [
        # 标准格式 - 使用 traded_volume
        {
            'stock_code': '600000.SH',
            'order_id': '12345',
            'traded_volume': 1000,
            'price': 10.5,
            'direction': 23,
            'trade_time': 1642752000000
        },
        # 旧格式 - 使用 volume
        {
            'stock_code': '000001.SZ',
            'order_id': '12346',
            'volume': 500,
            'price': 15.2,
            'direction': 24,
            'trade_time': 1642752000000
        },
        # 缺少字段的格式
        {
            'stock_code': '159516.SZ',
            'order_id': '12347',
            'price': 1.05,
            'direction': 23,
            'trade_time': 1642752000000
        },
        # 空数据
        {}
    ]
    
    for i, trade in enumerate(test_trades):
        print(f"\n测试数据 {i+1}:")
        print(f"原始数据: {trade}")
        
        # 模拟修复后的字段访问逻辑
        trade_code = trade.get('stock_code', '')
        trade_order_id = trade.get('order_id', '')
        
        # 尝试多种可能的成交量字段名，根据官方文档，成交量字段应该是 traded_volume
        trade_volume = (trade.get('traded_volume') or 
                       trade.get('volume') or 
                       trade.get('quantity') or 
                       trade.get('deal_volume') or 0)
        
        trade_price = trade.get('price', 0)
        trade_direction = trade.get('direction', 0)
        
        # 处理交易时间
        trade_time_ms = trade.get('trade_time', 0)
        if trade_time_ms:
            from datetime import datetime
            trade_time = datetime.fromtimestamp(trade_time_ms/1000).strftime('%H:%M:%S')
        else:
            trade_time = datetime.now().strftime('%H:%M:%S')
        
        # 检查必要字段是否有效
        if not trade_code or not trade_order_id or trade_volume <= 0 or trade_price <= 0:
            print(f"跳过无效成交回报: 代码={trade_code}, 委托号={trade_order_id}, 成交量={trade_volume}, 价格={trade_price}")
            continue
        
        # 记录成交详情
        direction_text = '买入' if trade_direction == 23 else '卖出'
        print(f"成交回报: {trade_code} 委托号:{trade_order_id} 方向:{direction_text} "
              f"成交量:{trade_volume} 成交价:{trade_price:.3f} 时间:{trade_time}")

def test_order_data_parsing():
    """测试委托数据解析"""
    print("\n\n测试委托数据解析...")
    
    # 模拟不同格式的委托数据
    test_orders = [
        # 标准格式 - 使用 traded_volume
        {
            'stock_code': '600000.SH',
            'order_id': '12345',
            'order_status': 56,
            'traded_volume': 1000,
            'order_time': 1642752000000
        },
        # 旧格式 - 使用 volume
        {
            'stock_code': '000001.SZ',
            'order_id': '12346',
            'order_status': 55,
            'volume': 500,
            'order_time': 1642752000000
        },
        # 缺少字段的格式
        {
            'stock_code': '159516.SZ',
            'order_id': '12347',
            'order_status': 54,
            'order_time': 1642752000000
        }
    ]
    
    for i, order in enumerate(test_orders):
        print(f"\n测试数据 {i+1}:")
        print(f"原始数据: {order}")
        
        # 模拟修复后的字段访问逻辑
        code = order.get('stock_code', '')
        order_id = order.get('order_id', '')
        status = order.get('order_status', 0)
        
        # 尝试多种可能的成交量字段名，根据官方文档，委托中的成交量字段应该是 traded_volume
        volume = (order.get('traded_volume') or 
                 order.get('volume') or 
                 order.get('deal_volume') or 
                 order.get('filled_volume') or 0)
        
        # 检查必要字段是否有效
        if not code or not order_id:
            print("跳过无效委托数据")
            continue
        
        # 处理委托时间
        order_time_ms = order.get('order_time', 0)
        if order_time_ms:
            try:
                from datetime import datetime
                order_time = datetime.fromtimestamp(order_time_ms/1000)
                elapsed_seconds = (datetime.now() - order_time).total_seconds()
                print(f"委托: {code} 委托号: {order_id} 状态: {status} 成交量: {volume} 已等待: {elapsed_seconds:.1f}秒")
            except (ValueError, OSError) as e:
                print(f"解析委托时间失败: {order_time_ms}, 错误: {str(e)}")
        else:
            print(f"委托: {code} 委托号: {order_id} 状态: {status} 成交量: {volume} (缺少时间信息)")

if __name__ == "__main__":
    print("=== 测试 volume 字段修复 ===")
    test_trade_data_parsing()
    test_order_data_parsing()
    print("\n=== 测试完成 ===")
