#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试卖出操作的buy_time访问修复
"""

import json
import sys
import os

def test_buy_time_access():
    """测试buy_time字段的访问逻辑"""
    
    print("=== 测试buy_time访问修复 ===\n")
    
    # 读取当前持仓记录
    position_file = "可转债放量方法持仓记录.json"
    if not os.path.exists(position_file):
        print(f"❌ 持仓记录文件不存在: {position_file}")
        return
    
    with open(position_file, 'r', encoding='utf-8') as f:
        position_records = json.load(f)
    
    print(f"📁 读取持仓记录，共 {len(position_records)} 条")
    
    # 测试每个持仓记录的buy_time访问
    for code, position_info in position_records.items():
        print(f"\n🔍 测试 {code}:")
        print(f"   is_signal_only: {position_info.get('is_signal_only', False)}")
        print(f"   total_quantity: {position_info.get('total_quantity', 0)}")
        
        # 测试新的buy_time访问逻辑
        try:
            # 方法1：新格式访问
            if 'buy_queue' in position_info and position_info['buy_queue']:
                buy_time = position_info['buy_queue'][0].get('buy_time', '')
                buy_order_id = position_info['buy_queue'][0].get('order_id', '')
                buy_price = position_info['buy_queue'][0].get('buy_price', 0)
                print(f"   ✅ 新格式访问成功:")
                print(f"      buy_time: {buy_time}")
                print(f"      buy_price: {buy_price}")
                print(f"      order_id: {buy_order_id}")
            else:
                # 方法2：旧格式访问
                buy_time = position_info.get('buy_time', '')
                buy_order_id = position_info.get('order_id', '')
                buy_price = position_info.get('buy_price', 0)
                print(f"   ⚠️  使用旧格式访问:")
                print(f"      buy_time: {buy_time}")
                print(f"      buy_price: {buy_price}")
                print(f"      order_id: {buy_order_id}")
            
            # 测试旧的错误访问方式（这会导致KeyError）
            try:
                old_buy_time = position_info['buy_time']  # 这是错误的访问方式
                print(f"   ❌ 旧格式直接访问成功（不应该发生）: {old_buy_time}")
            except KeyError:
                print(f"   ✅ 旧格式直接访问失败（符合预期）")
                
        except Exception as e:
            print(f"   ❌ 访问失败: {e}")
    
    print(f"\n=== 模拟卖出操作的buy_time访问 ===")
    
    # 找一个实际持仓进行模拟测试
    actual_positions = {code: info for code, info in position_records.items() 
                       if info.get('total_quantity', 0) > 0}
    
    if actual_positions:
        test_code = list(actual_positions.keys())[0]
        test_position = actual_positions[test_code]
        
        print(f"\n🧪 模拟卖出 {test_code}:")
        
        # 模拟execute_sell中的buy_time访问逻辑
        try:
            # 获取买入时间 - 支持新格式和旧格式
            if 'buy_queue' in test_position and test_position['buy_queue']:
                buy_time = test_position['buy_queue'][0].get('buy_time', '')
                buy_order_id = test_position['buy_queue'][0].get('order_id', '')
                buy_price = test_position['buy_queue'][0].get('buy_price', 0)
            else:
                buy_time = test_position.get('buy_time', '')
                buy_order_id = test_position.get('order_id', '')
                buy_price = test_position.get('buy_price', 0)
            
            print(f"   ✅ 卖出操作buy_time访问成功:")
            print(f"      买入时间: {buy_time}")
            print(f"      买入价格: {buy_price}")
            print(f"      订单ID: {buy_order_id}")
            
            # 模拟创建交易记录
            trade_record = {
                'code': test_code,
                'buy_price': buy_price,
                'buy_time': buy_time,
                'sell_price': 150.0,  # 模拟卖出价格
                'sell_time': '2025-08-01 10:10:08',
                'quantity': test_position.get('total_quantity', 0),
                'buy_order_id': buy_order_id,
                'sell_order_id': 'TEST_SELL_ORDER'
            }
            
            print(f"   ✅ 交易记录创建成功:")
            for key, value in trade_record.items():
                print(f"      {key}: {value}")
                
        except Exception as e:
            print(f"   ❌ 模拟卖出失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("   ⚠️  没有实际持仓可供测试")

if __name__ == "__main__":
    test_buy_time_access()
