#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从交易日志文件中恢复交易记录
"""

import os
import re
import json
from datetime import datetime, <PERSON><PERSON><PERSON>

def parse_trading_log(log_file):
    """解析交易日志文件，提取交易记录"""
    trades = []
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"正在解析日志文件: {log_file}")
        
        # 用于匹配买入和卖出记录的正则表达式
        buy_pattern = r'\[(\d{2}:\d{2}:\d{2})\].*\[虚拟\].*买入委托\s+(\w+\.\w+)\s+数量:(\d+)股\s+价格:([\d.]+)'
        sell_pattern = r'\[(\d{2}:\d{2}:\d{2})\].*\[虚拟\]卖出\s+(\w+\.\w+)\s+价格:([\d.]+)\s+数量:(\d+)\s+盈亏:([-+]?[\d.]+)元'
        
        # 临时存储买入记录，用于匹配卖出
        pending_buys = {}
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # 匹配买入记录
            buy_match = re.search(buy_pattern, line)
            if buy_match:
                time_str, code, quantity, price = buy_match.groups()
                
                # 从文件名提取日期
                date_str = log_file.split('_')[-1].replace('.txt', '')
                buy_time = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]} {time_str}"
                
                # 存储买入记录
                if code not in pending_buys:
                    pending_buys[code] = []
                
                pending_buys[code].append({
                    'buy_time': buy_time,
                    'buy_price': float(price),
                    'quantity': int(quantity),
                    'date_str': date_str
                })
                
                print(f"  买入: {code} {quantity}股 @{price} 时间:{time_str}")
            
            # 匹配卖出记录
            sell_match = re.search(sell_pattern, line)
            if sell_match:
                time_str, code, sell_price, quantity, profit = sell_match.groups()
                
                # 从文件名提取日期
                date_str = log_file.split('_')[-1].replace('.txt', '')
                sell_time = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]} {time_str}"
                
                # 查找对应的买入记录
                if code in pending_buys and pending_buys[code]:
                    buy_record = pending_buys[code].pop(0)  # FIFO
                    
                    # 计算手续费（简单估算）
                    buy_amount = buy_record['buy_price'] * buy_record['quantity']
                    sell_amount = float(sell_price) * int(quantity)
                    estimated_fee = (buy_amount + sell_amount) * 0.0003  # 估算手续费
                    
                    # 创建交易记录
                    trade_record = {
                        'code': code,
                        'buy_time': buy_record['buy_time'],
                        'sell_time': sell_time,
                        'buy_price': buy_record['buy_price'],
                        'sell_price': float(sell_price),
                        'quantity': int(quantity),
                        'profit': float(profit),
                        'profit_percent': (float(profit) / buy_amount * 100) if buy_amount > 0 else 0,
                        'total_fee': estimated_fee,
                        'trade_date': date_str
                    }
                    
                    trades.append(trade_record)
                    print(f"  卖出: {code} {quantity}股 @{sell_price} 盈亏:{profit}元 时间:{time_str}")
                else:
                    print(f"  警告: 找不到 {code} 的对应买入记录")
        
        # 清理剩余的买入记录
        for code, buys in pending_buys.items():
            if buys:
                print(f"  警告: {code} 有 {len(buys)} 个未匹配的买入记录")
        
        print(f"  共解析出 {len(trades)} 条交易记录")
        return trades
        
    except Exception as e:
        print(f"解析日志文件失败: {str(e)}")
        return []

def restore_trading_records_advanced():
    """从日志文件恢复近三天的交易记录（改进版，处理跨日交易）"""
    print("=== 从交易日志恢复交易记录（改进版）===")

    # 获取近5天的日期（包括周末，用于查找日志文件）
    today = datetime.now()
    dates_to_check = []

    for i in range(7):  # 检查更多天数以确保覆盖
        check_date = today - timedelta(days=i)
        date_str = check_date.strftime('%Y%m%d')
        dates_to_check.append(date_str)

    print(f"将检查以下日期的日志文件: {dates_to_check}")

    # 全局买入记录池，用于跨日匹配
    global_pending_buys = {}
    all_trades_by_date = {}

    # 第一遍：收集所有买入记录
    print("\n第一步：收集所有买入记录...")
    for date_str in dates_to_check:
        log_file = f"log/trading_log_{date_str}.txt"

        if not os.path.exists(log_file):
            continue

        print(f"  扫描买入记录: {log_file}")

        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            buy_pattern = r'\[(\d{2}:\d{2}:\d{2})\].*\[虚拟\].*买入委托\s+(\w+\.\w+)\s+数量:(\d+)股\s+价格:([\d.]+)'

            for line in lines:
                buy_match = re.search(buy_pattern, line.strip())
                if buy_match:
                    time_str, code, quantity, price = buy_match.groups()
                    buy_time = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]} {time_str}"

                    if code not in global_pending_buys:
                        global_pending_buys[code] = []

                    global_pending_buys[code].append({
                        'buy_time': buy_time,
                        'buy_price': float(price),
                        'quantity': int(quantity),
                        'date_str': date_str
                    })

                    print(f"    买入: {code} {quantity}股 @{price} {buy_time}")

        except Exception as e:
            print(f"    读取文件失败: {str(e)}")

    # 第二遍：匹配卖出记录
    print("\n第二步：匹配卖出记录...")
    for date_str in dates_to_check:
        log_file = f"log/trading_log_{date_str}.txt"

        if not os.path.exists(log_file):
            continue

        print(f"  处理卖出记录: {log_file}")

        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            sell_pattern = r'\[(\d{2}:\d{2}:\d{2})\].*\[虚拟\]卖出\s+(\w+\.\w+)\s+价格:([\d.]+)\s+数量:(\d+)\s+盈亏:([-+]?[\d.]+)元'

            for line in lines:
                sell_match = re.search(sell_pattern, line.strip())
                if sell_match:
                    time_str, code, sell_price, quantity, profit = sell_match.groups()
                    sell_time = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]} {time_str}"

                    # 查找对应的买入记录
                    if code in global_pending_buys and global_pending_buys[code]:
                        buy_record = global_pending_buys[code].pop(0)  # FIFO

                        # 计算手续费
                        buy_amount = buy_record['buy_price'] * buy_record['quantity']
                        sell_amount = float(sell_price) * int(quantity)
                        estimated_fee = (buy_amount + sell_amount) * 0.0003

                        # 创建交易记录
                        trade_record = {
                            'code': code,
                            'buy_time': buy_record['buy_time'],
                            'sell_time': sell_time,
                            'buy_price': buy_record['buy_price'],
                            'sell_price': float(sell_price),
                            'quantity': int(quantity),
                            'profit': float(profit),
                            'profit_percent': (float(profit) / buy_amount * 100) if buy_amount > 0 else 0,
                            'total_fee': estimated_fee,
                            'trade_date': date_str
                        }

                        # 按卖出日期分组
                        if date_str not in all_trades_by_date:
                            all_trades_by_date[date_str] = []
                        all_trades_by_date[date_str].append(trade_record)

                        print(f"    匹配成功: {code} {quantity}股 @{sell_price} 盈亏:{profit}元")
                        print(f"      买入时间: {buy_record['buy_time']}")
                        print(f"      卖出时间: {sell_time}")
                    else:
                        print(f"    警告: 找不到 {code} 的对应买入记录")

        except Exception as e:
            print(f"    读取文件失败: {str(e)}")

    # 保存交易记录
    print("\n第三步：保存交易记录...")
    restored_count = 0

    # 只保存近3个工作日的记录
    work_dates = []
    for i in range(7):
        check_date = today - timedelta(days=i)
        if check_date.weekday() < 5:  # 工作日
            work_dates.append(check_date.strftime('%Y%m%d'))
            if len(work_dates) >= 3:
                break

    for date_str in work_dates:
        if date_str in all_trades_by_date:
            trades = all_trades_by_date[date_str]
            history_file = f"交易历史_{date_str}.json"

            # 备份现有文件
            if os.path.exists(history_file):
                backup_file = f"{history_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                os.rename(history_file, backup_file)
                print(f"  已备份原文件: {backup_file}")

            # 写入新的交易记录
            try:
                with open(history_file, 'w', encoding='utf-8') as f:
                    json.dump(trades, f, ensure_ascii=False, indent=2)

                print(f"  ✅ 成功恢复 {date_str} 的交易记录: {len(trades)} 条记录")
                restored_count += 1

                # 显示统计信息
                total_profit = sum(trade['profit'] for trade in trades)
                profit_trades = sum(1 for trade in trades if trade['profit'] > 0)
                loss_trades = len(trades) - profit_trades
                win_rate = (profit_trades / len(trades) * 100) if len(trades) > 0 else 0

                print(f"    统计: 总交易{len(trades)}次, 盈利{profit_trades}次, 亏损{loss_trades}次")
                print(f"    胜率: {win_rate:.1f}%, 总盈亏: {total_profit:.2f}元")

            except Exception as e:
                print(f"  保存交易记录失败: {str(e)}")
        else:
            print(f"  {date_str}: 没有找到交易记录")

    # 显示未匹配的买入记录
    print("\n未匹配的买入记录:")
    for code, buys in global_pending_buys.items():
        if buys:
            print(f"  {code}: {len(buys)} 个未匹配的买入记录")
            for buy in buys:
                print(f"    {buy['buy_time']} {buy['quantity']}股 @{buy['buy_price']}")

    print(f"\n=== 恢复完成 ===")
    print(f"成功恢复了 {restored_count} 个交易日的记录")

    return restored_count

def restore_trading_records():
    """从日志文件恢复近三天的交易记录"""
    return restore_trading_records_advanced()

def verify_restored_records():
    """验证恢复的交易记录"""
    print("\n=== 验证恢复的交易记录 ===")
    
    # 获取近三天的日期
    today = datetime.now()
    
    for i in range(3):
        check_date = today - timedelta(days=i)
        if check_date.weekday() < 5:  # 只检查工作日
            date_str = check_date.strftime('%Y%m%d')
            history_file = f"交易历史_{date_str}.json"
            
            if os.path.exists(history_file):
                try:
                    with open(history_file, 'r', encoding='utf-8') as f:
                        trades = json.load(f)
                    
                    print(f"📅 {date_str} ({check_date.strftime('%Y-%m-%d')}):")
                    print(f"  交易记录数: {len(trades)}")
                    
                    if trades:
                        total_profit = sum(trade.get('profit', 0) for trade in trades)
                        print(f"  总盈亏: {total_profit:.2f}元")
                        
                        # 显示前几条记录作为样本
                        print("  样本记录:")
                        for i, trade in enumerate(trades[:3]):
                            print(f"    {i+1}. {trade.get('code', 'N/A')} "
                                  f"买入@{trade.get('buy_price', 0):.3f} "
                                  f"卖出@{trade.get('sell_price', 0):.3f} "
                                  f"盈亏:{trade.get('profit', 0):.2f}元")
                    print()
                    
                except Exception as e:
                    print(f"  读取文件失败: {str(e)}")
            else:
                print(f"📅 {date_str}: 文件不存在")

if __name__ == "__main__":
    # 恢复交易记录
    restored_count = restore_trading_records()
    
    if restored_count > 0:
        # 验证恢复的记录
        verify_restored_records()
    else:
        print("没有成功恢复任何交易记录")
