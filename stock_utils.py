# coding=utf-8
"""
股票交易工具库
提供常用的股票数据处理、技术分析和可视化功能

主要功能:
1. 数据下载和订阅
2. 股票代码转换
3. 技术指标计算
4. 图表绘制

Technical indicators calculation utilities
使用backtrader内置指标计算以保证与回测结果的一致性
"""

import os
import string
import time
import pandas as pd
import numpy as np
import mplfinance as mpf
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from typing import List, Dict, Union, Optional, Tuple
from xtquant import xtdata
import psutil
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
from datetime import datetime, time as dt_time, timedelta
import matplotlib.dates as mdates
from PyQt5.QtWidgets import QMessageBox
from xtquant import xtdata
import backtrader as bt

# 设置中文显示
os.environ['NLS_LANG'] = 'SIMPLIFIED CHINESE_CHINA.UTF8'
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def calculate_bollinger_bands(df: pd.DataFrame, window: int = 30) -> pd.DataFrame:
    """
    计算布林线指标
    
    计算公式:
    MID = MA(C, N)
    VART1 = POW(C-MID, 2)
    VART2 = MA(VART1, N)
    VART3 = SQRT(VART2)
    UPPER = MID + 2*VART3
    LOWER = MID - 2*VART3
    
    Args:
        df: 包含OHLCV数据的DataFrame
        window: 计算周期，默认30
        
    Returns:
        添加了布林线指标的DataFrame，包含:
        - MA: 中轨线(移动平均线)
        - Upper: 上轨线
        - Lower: 下轨线
    """
    try:
        # 确保数据足够
        if len(df) < window * 2:
            print(f"数据点数太少({len(df)})，无法计算布林线，至少需要{window * 2}个数据点")
            return df
        
        # 确保close列为数值类型并处理NaN值
        df['close'] = pd.to_numeric(df['close'], errors='coerce')
        df['close'] = df['close'].ffill()
        
        # 计算中轨线 (MA)
        df['MA'] = df['close'].rolling(window=window, min_periods=window).mean()
        
        # 计算标准差
        df['VART1'] = (df['close'] - df['MA']).pow(2)  # POW(C-MID,2)
        df['VART2'] = df['VART1'].rolling(window=window, min_periods=window).mean()  # MA(VART1,N)
        df['VART3'] = df['VART2'].pow(0.5)  # SQRT(VART2)
        
        # 计算上轨和下轨
        df['Upper'] = df['MA'] + (2 * df['VART3'])
        df['Lower'] = df['MA'] - (2 * df['VART3'])
        
        # 删除中间计算列
        df = df.drop(['VART1', 'VART2', 'VART3'], axis=1)
        
        # 检查是否有足够的有效数据
        valid_data = df[['MA', 'Upper', 'Lower']].notna().all(axis=1)
        valid_count = valid_data.sum()
        
        if valid_count < window:
            print(f"有效数据点数太少({valid_count})，无法可靠计算布林线")
        
        return df
        
    except Exception as e:
        print(f"计算布林线时出错: {e}")
        return df

def get_base_prices(df: pd.DataFrame) -> Optional[List[Dict]]:
    """
    获取基准价格
    
    Args:
        df: 包含OHLCV数据的DataFrame
        
    Returns:
        List[Dict]: 基准价格记录列表，每个记录包含:
            - base_lower_price: 基准下价
            - base_lower_time: 基准下价时间
    """
    try:
        # 检查数据是否足够
        if len(df) < 35:
            return None
            
        # 计算布林线
        df = calculate_bollinger_bands(df)
        
        # 记录触发条件
        trigger_records = []
        
        for i in range(35, len(df)):
            window_df = df.iloc[i-35:i+1].copy()
            window_df = window_df.reset_index(drop=True)
            
            condition_a_triggered = False
            condition_a_time = None
            condition_b_time = None
            condition_a_idx = None
            condition_b_idx = None
            
            for idx in range(len(window_df)-1):
                current_row = window_df.iloc[idx]
                next_row = window_df.iloc[idx+1]
                
                # 检查条件a：布林线下轨在K线范围内
                if (current_row['Lower'] > current_row['low']) and (current_row['Lower'] < current_row['high']):
                    if not condition_a_triggered:
                        condition_a_triggered = True
                        condition_a_time = df.iloc[i-35+idx]['time']
                        condition_a_idx = i-35+idx
                        continue
                
                # 如果条件a已触发，检查条件b：股价上穿布林线中轨
                if condition_a_triggered:
                    if (current_row['close'] < current_row['MA']) and (next_row['close'] > next_row['MA']):
                        condition_b_time = df.iloc[i-35+idx+1]['time']
                        condition_b_idx = i-35+idx+1
                        break
            
            # 如果两个条件都触发且顺序正确
            if condition_a_triggered and condition_b_time and condition_a_time < condition_b_time:
                # 计算基准下价
                base_window = df.iloc[condition_a_idx:condition_b_idx+1]
                base_lower_price = base_window['low'].min()
                base_lower_time = df.iloc[base_window['low'].idxmin()]['time']
                
                # 添加记录
                trigger_records.append({
                    'base_lower_price': float(base_lower_price),
                    'base_lower_time': base_lower_time
                })
        
        return trigger_records if trigger_records else None
        
    except Exception as e:
        print(f"获取基准价格时出错: {e}")
        return None

def plot_kline(df: pd.DataFrame, title: str, period: str = '') -> None:
    """
    绘制K线图
    
    Args:
        df: 包含OHLCV数据的DataFrame，必须包含以下列:
           - time: 时间戳
           - open: 开盘价
           - high: 最高价
           - low: 最低价
           - close: 收盘价
           - volume: 成交量
        title: 图表标题
        period: 数据周期
    """
    try:
        # 检查数据
        if df is None or df.empty:
            raise ValueError("数据为空")
            
        required_columns = ['time', 'open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"数据缺少必要的列: {', '.join(missing_columns)}")
            
        # 数据预处理
        df = df.copy()
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
            
        # 删除无效数据
        df = df.dropna(subset=numeric_columns)
        if len(df) < 1:
            raise ValueError("处理后的数据为空")
            
        # 设置时间索引
        df.index = pd.to_datetime(df['time']/1000, unit='s') + timedelta(hours=8)
        
        # 计算布林线
        df = calculate_bollinger_bands(df)
        
        # 创建附加图表
        add_plots = []
        if all(col in df.columns for col in ['MA', 'Upper', 'Lower']):
            # 处理布林线数据
            df['MA'] = df['MA'].ffill()
            df['Upper'] = df['Upper'].ffill()
            df['Lower'] = df['Lower'].ffill()
            
            valid_length = len(df)
            if valid_length > 0:
                ma_data = df['MA'].values
                upper_data = df['Upper'].values
                lower_data = df['Lower'].values
                
                if (not np.isnan(ma_data).all() and 
                    not np.isnan(upper_data).all() and 
                    not np.isnan(lower_data).all()):
                    add_plots.extend([
                        mpf.make_addplot(ma_data, color='blue', width=0.5),
                        mpf.make_addplot(upper_data, color='gray', width=0.5, linestyle='--'),
                        mpf.make_addplot(lower_data, color='gray', width=0.5, linestyle='--')
                    ])
        
        # 获取原始数据的副本并处理时间
        orig_df = df.copy()
        orig_df['time'] = df.index.astype(np.int64) // 10**6 - 8*60*60*1000
        
        # 计算基准价信息
        base_prices = check_stock_condition(orig_df)
        if base_prices:
            # 为基准下价和基准上价创建不同的颜色列表
            lower_colors = ['green', 'lime', 'darkgreen', 'lightgreen', 'forestgreen']  # 基准下价的颜色列表
            upper_colors = ['red', 'crimson', 'darkred', 'indianred', 'firebrick']  # 基准上价的颜色列表
            lower_count = 0
            upper_count = 0
            
            for record in base_prices:
                # 绘制基准下价线
                if record.get('base_lower_price') is not None:
                    color = lower_colors[lower_count % len(lower_colors)]
                    base_lower_line = pd.Series(record['base_lower_price'], index=df.index)
                    add_plots.append(
                        mpf.make_addplot(base_lower_line, color=color, width=1, linestyle=':',
                                       panel=0, secondary_y=False)
                    )
                    lower_count += 1
                
                # 绘制基准上价线
                if record.get('base_upper_price') is not None:
                    color = upper_colors[upper_count % len(upper_colors)]
                    base_upper_line = pd.Series(record['base_upper_price'], index=df.index)
                    add_plots.append(
                        mpf.make_addplot(base_upper_line, color=color, width=1, linestyle='-.',
                                       panel=0, secondary_y=False)
                    )
                    upper_count += 1
        
        # 设置样式
        my_style = mpf.make_mpf_style(
            base_mpf_style='charles', 
            gridstyle='',
            rc={'font.family': 'Microsoft YaHei'},
            marketcolors=mpf.make_marketcolors(
                up='red',
                down='green',
                edge='inherit',
                wick='inherit',
                volume='inherit'
            )
        )
        
        # 绘制图表
        kwargs = {
            'type': 'candle',
            'title': title,
            'style': my_style,
            'volume': True,
            'figsize': (12, 8)
        }
        
        if add_plots:
            kwargs['addplot'] = add_plots
        
        mpf.plot(df, **kwargs)
                
    except Exception as e:
        raise Exception(f"绘制K线图时出错: {str(e)}")

def plot_time_series(df: pd.DataFrame, title: str) -> None:
    """
    绘制分时图
    
    Args:
        df: 包含分时数据的DataFrame，必须包含以下列:
           - time: 时间戳
           - open: 开盘价
           - close: 收盘价
           - volume: 成交量
           - amount: 成交额
        title: 图表标题
    """
    try:
        # 检查数据
        if df.empty:
            raise ValueError("数据为空")
            
        required_columns = ['time', 'open', 'close', 'volume', 'amount']
        if not all(col in df.columns for col in required_columns):
            raise ValueError("数据缺少必要的列")
        
        # 关闭已有图形
        plt.close('all')
        
        # 创建图形
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), height_ratios=[3, 1], sharex=True)
        fig.suptitle(title, fontsize=12)
        
        # 数据预处理
        df = df.copy()
        df['time'] = pd.to_datetime(df['time'], unit='ms')
        df = df.sort_values('time')
        
        # 获取基准价(昨收)
        base_price = df.iloc[0]['open']  # 如果没有昨收价，使用今日开盘价
        
        # 绘制价格线
        ax1.plot(df['time'], df['close'], 'b-', linewidth=1, label='价格')
        
        # 计算并绘制均价线
        df['avg_price'] = df['amount'].cumsum() / df['volume'].cumsum()
        ax1.plot(df['time'], df['avg_price'], 'r--', linewidth=1, label='均价')
        
        # 设置价格轴
        min_price = df['close'].min()
        max_price = df['close'].max()
        price_range = max(abs(max_price - base_price), abs(min_price - base_price))
        if price_range == 0:
            price_range = base_price * 0.01
        else:
            price_range = price_range * 1.1
        
        ax1.set_ylim(base_price - price_range, base_price + price_range)
        ax1.axhline(y=base_price, color='gray', linestyle=':', alpha=0.5)
        
        # 添加涨跌幅坐标轴
        ax1_right = ax1.twinx()
        ratio_range = price_range / base_price * 100
        ax1_right.set_ylim(-ratio_range, ratio_range)
        ax1_right.set_ylabel('涨跌幅(%)')
        
        # 设置网格和标签
        ax1.grid(True, linestyle='--', alpha=0.3)
        ax1.set_ylabel('价格')
        ax1.legend(loc='best')
        
        # 绘制成交量
        volume_colors = ['red' if c >= o else 'green' 
                        for o, c in zip(df['open'], df['close'])]
        time_diff = (df['time'].iloc[1] - df['time'].iloc[0]).total_seconds() / 86400
        bar_width = time_diff * 0.8
        ax2.bar(df['time'], df['volume'], width=bar_width, 
                color=volume_colors, alpha=0.7)
        ax2.grid(True, linestyle='--', alpha=0.3)
        ax2.set_ylabel('成交量')
        
        # 设置时间轴
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        raise Exception(f"绘制分时图时出错: {str(e)}")

def calculate_rsi(prices: pd.Series, period: int = 14) -> pd.Series:
    """
    计算RSI指标
    
    Args:
        prices: 价格序列
        period: RSI计算周期，默认14
        
    Returns:
        RSI指标序列
    """
    try:
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    except Exception as e:
        print(f"计算RSI时出错: {e}")
        return pd.Series()

def calculate_momentum(prices: pd.Series, period: int = 20) -> pd.Series:
    """
    计算动量指标
    
    Args:
        prices: 价格序列
        period: 动量计算周期，默认20
        
    Returns:
        动量指标序列
    """
    try:
        return prices.pct_change(periods=period)
    except Exception as e:
        print(f"计算动量时出错: {e}")
        return pd.Series()

def calculate_ma(prices: pd.Series, window: int = 5) -> pd.Series:
    """
    计算移动平均线
    
    Args:
        prices: 价格序列
        window: 移动平均周期，默认5
        
    Returns:
        移动平均线序列
    """
    try:
        return prices.rolling(window=window).mean()
    except Exception as e:
        print(f"计算移动平均线时出错: {e}")
        return pd.Series()

def is_etf(stock_code: str) -> bool:
    """
    判断是否为ETF
    
    Args:
        stock_code: 证券代码，格式如 '510300.SH'
        
    Returns:
        bool: 是否为ETF
    """
    if not stock_code:
        return False
    # ETF代码规则：
    # 上海ETF以51开头
    # 深圳ETF以15开头
    code = stock_code.split('.')[0]
    return code.startswith(('51', '15'))

def is_money_fund(stock_code: str) -> bool:
    """
    判断是否为货币基金
    
    Args:
        stock_code: 证券代码，格式如 '511880.SH'
        
    Returns:
        bool: 是否为货币基金
    """
    money_funds = [
        "511800.SH",  # 易方达货币ETF
        "511880.SH",  # 银华日利
        "511990.SH",  # 华宝添益
        "511660.SH",  # 建信添益
        # 可以继续添加其他货币基金代码
    ]
    return stock_code in money_funds

def check_limit_up(code: str, tick_data: dict) -> Tuple[bool, str]:
    """
    检查股票是否涨停
    
    Args:
        code: 股票代码，例如 '000001.SZ'
        tick_data: tick数据字典，包含以下关键字段：
            - lastPrice: 最新价
            - bidPrice: 买一价列表
            - bidVol: 买一量列表
            - askPrice: 卖一价列表
            - askVol: 卖一量列表
            - lastClose: 昨收价
            
    Returns:
        Tuple[bool, str]: 返回一个元组，包含：
            - is_limit_up (bool): 是否涨停
            - status_text (str): 状态描述文本
            
    Example:
        is_limit, status = check_limit_up('000001.SZ', tick_data)
        print(f"涨停状态: {status}")
    """
    try:
        # 获取合约信息
        stock_info = xtdata.get_instrument_detail(code, False)
        if not stock_info:
            return False, "无法获取合约信息"
        
        # 获取必要数据
        last_price = tick_data.get('lastPrice')
        limit_up_price = stock_info.get('UpStopPrice')  # 涨停价
        bid_price = tick_data.get('bidPrice', [None])[0]  # 买一价
        bid_volume = tick_data.get('bidVol', [None])[0]  # 买一量
        ask_price = tick_data.get('askPrice', [None])[0]  # 卖一价
        ask_volume = tick_data.get('askVol', [None])[0]  # 卖一量
        
        # 计算涨幅
        prev_close = tick_data.get('lastClose')
        if prev_close and prev_close > 0:
            ratio = (last_price / prev_close - 1) * 100
            ratio_str = f"涨幅: {ratio:.2f}%"
        else:
            ratio_str = "涨幅: --"
        
        # 构建买卖盘信息
        volume_info = []
        if bid_volume is not None:
            volume_info.append(f"买一量: {bid_volume}")
        if ask_volume is not None:
            volume_info.append(f"卖一量: {ask_volume}")
        volume_str = " | ".join(volume_info) if volume_info else "无买卖盘"
        
        # 判断涨停状态
        price_is_limit = abs(last_price - limit_up_price) < 0.01  # 价格达到涨停价
        bid_is_limit = bid_price == 0 and bid_volume and bid_volume > 0  # 买一价为0且有买盘
        ask_is_limit = ask_volume == 0 or ask_volume is None
        
        if price_is_limit:
            if ask_is_limit:
                status = "已涨停"  # 价格涨停且无卖盘
            elif bid_is_limit:
                status = "涨停封板"  # 价格涨停且买盘封住
            elif bid_price == 0:
                status = "涨停未封(无买盘)"  # 价格涨停但无人买入
            else:
                status = "涨停未封(有卖盘)"  # 价格涨停但有卖盘
            return True, f"{status} | {volume_str} | {ratio_str}"
        else:
            return False, f"{volume_str} | {ratio_str}"
            
    except Exception as e:
        return False, f"检查涨停出错: {str(e)}"

def check_stock_condition(df: pd.DataFrame) -> Optional[List[Dict]]:
    """
    检查股票是否满足条件：
    基准下价的计算条件：
       - 条件a：布林线下轨数值>k线最低价 and <k线最高价
       - 条件b：股价上穿布林线中轨
       - 在35个周期内，先发生a再发生b
       - 基准下价为条件a和条件b之间的最低价
       
    基准上价的计算条件：
       - 条件c：股价最高价大于上轨
       - 记录突破上轨后的最高价
       - 如果连续5日不创新高，则将这期间的最高价设为基准上价
       - 基准上价必须与上轨线至少有两次交叉才能确认为有效
    """
    try:
        if len(df) < 35:
            return None
            
        # 确保数据类型正确
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
            
        # 重置索引
        df = df.reset_index(drop=True)
            
        # 计算布林线
        window = 30
        df = calculate_bollinger_bands(df)
        
        # 记录所有触发记录
        trigger_records = []
        
        # 用于跟踪上轨突破状态
        upper_tracking = {
            'is_tracking': False,  # 是否正在跟踪
            'highest_price': 0,    # 跟踪期间的最高价
            'highest_time': None,  # 最高价出现的时间
            'no_new_high_count': 0,  # 连续未创新高的天数
            'start_time': None     # 开始跟踪的时间
        }
        
        # 临时存储待验证的基准上价记录
        temp_upper_records = []
        
        # 遍历数据计算基准价格
        for i in range(35, len(df)):
            # 获取当前窗口数据
            window_df = df.iloc[i-35:i+1].copy()
            window_df = window_df.reset_index(drop=True)
            
            # 用于存储当前窗口的基准下价计算结果
            lower_price_records = []
            
            # 检查条件a和b，可能有多个组合
            condition_a_points = []  # 存储所有满足条件a的点
            
            for idx in range(len(window_df)-1):
                current_row = window_df.iloc[idx]
                next_row = window_df.iloc[idx+1]
                
                # 检查条件a：布林线下轨在K线范围内
                if (current_row['Lower'] > current_row['low']) and (current_row['Lower'] < current_row['high']):
                    condition_a_points.append({
                        'time': df.iloc[i-35+idx]['time'],
                        'idx': i-35+idx
                    })
                
                # 检查条件b：股价上穿布林线中轨
                if (current_row['close'] < current_row['MA']) and (next_row['close'] > next_row['MA']):
                    condition_b_time = df.iloc[i-35+idx+1]['time']
                    condition_b_idx = i-35+idx+1
                    
                    # 检查之前的所有条件a点，找出有效的组合
                    for a_point in condition_a_points:
                        if a_point['time'] < condition_b_time:  # 确保条件a在条件b之前
                            # 计算这对a-b点之间的基准下价
                            base_window = df.iloc[a_point['idx']:condition_b_idx+1]
                            base_lower_price = base_window['low'].min()
                            base_lower_idx = base_window['low'].idxmin()
                            base_lower_time = df.iloc[base_lower_idx]['time']
                            
                            lower_price_records.append({
                                'base_lower_price': float(base_lower_price),
                                'base_lower_time': base_lower_time,
                                'condition_a_time': a_point['time'],
                                'condition_b_time': condition_b_time
                            })
            
            # 检查条件c：股价最高价大于上轨
            current_row = df.iloc[i]
            
            # 如果当前最高价大于上轨且未在跟踪状态，开始跟踪
            if current_row['high'] > current_row['Upper'] and not upper_tracking['is_tracking']:
                upper_tracking = {
                    'is_tracking': True,
                    'highest_price': current_row['high'],
                    'highest_time': current_row['time'],
                    'no_new_high_count': 0,
                    'start_time': current_row['time']
                }
            
            # 如果正在跟踪
            elif upper_tracking['is_tracking']:
                # 如果出现新高，更新最高价并重置计数器
                if current_row['high'] > upper_tracking['highest_price']:
                    upper_tracking['highest_price'] = current_row['high']
                    upper_tracking['highest_time'] = current_row['time']
                    upper_tracking['no_new_high_count'] = 0
                else:
                    # 未创新高，增加计数
                    upper_tracking['no_new_high_count'] += 1
                    
                    # 如果连续5日未创新高，记录基准上价并重置跟踪状态
                    if upper_tracking['no_new_high_count'] >= 5:
                        temp_upper_records.append({
                            'base_upper_price': float(upper_tracking['highest_price']),
                            'base_upper_time': upper_tracking['highest_time'],
                            'condition_c_time': upper_tracking['start_time']
                        })
                        # 重置跟踪状态
                        upper_tracking = {
                            'is_tracking': False,
                            'highest_price': 0,
                            'highest_time': None,
                            'no_new_high_count': 0,
                            'start_time': None
                        }
            
            # 将当前窗口的基准下价记录添加到总记录中
            for record in lower_price_records:
                trigger_records.append({
                    'base_lower_price': record['base_lower_price'],
                    'base_lower_time': record['base_lower_time'],
                    'condition_a_time': record['condition_a_time'],
                    'condition_b_time': record['condition_b_time']
                })
        
        # 验证基准上价与上轨线的交叉次数
        for record in temp_upper_records:
            base_price = record['base_upper_price']
            cross_count = 0
            prev_state = None  # None表示初始状态，True表示价格在上轨上方，False表示在下方
            
            # 遍历所有数据点检查交叉
            for _, row in df.iterrows():
                if pd.isna(row['Upper']):
                    continue
                    
                current_state = base_price > row['Upper']
                
                # 如果前一状态存在且与当前状态不同，说明发生了交叉
                if prev_state is not None and current_state != prev_state:
                    cross_count += 1
                    
                prev_state = current_state
                
                # 如果已经找到两次交叉，可以提前结束
                if cross_count >= 2:
                    break
            
            # 如果交叉次数大于等于2，将记录添加到最终结果中
            if cross_count >= 2:
                trigger_records.append(record)
        
        return trigger_records if trigger_records else None
        
    except Exception as e:
        print(f"检查股票条件时出错: {e}")
        print(f"错误发生在行: {e.__traceback__.tb_lineno}")
        return None

class DataDownloader:
    """
    数据下载管理类
    
    主要功能:
    1. 下载历史数据
    2. 订阅实时行情
    3. 获取市场数据
    
    属性:
        api_key: API密钥（可选）
        data: 存储下载的数据
        code_list: 股票代码列表
    """
    
    def __init__(self, api_key: str = None):
        """
        初始化下载器
        
        Args:
            api_key: API密钥，可选参数
        """
        self.api_key = api_key
        self.data = None
        self.code_list = []
        self.data_path = r"D:\国金证券QMT交易端\userdata_mini\datadir"  # 设置默认数据路径
        # 不自动下载板块数据，避免超时
        # xtdata.download_sector_data()
    
    @staticmethod
    def normalize_period(period):
        """
        标准化周期参数
        
        Args:
            period: 数据周期，支持 "1m", "5m", "30m", "1d", "tick"
            
        Returns:
            str: 标准化后的周期字符串
            
        Raises:
            ValueError: 当周期格式无效时抛出
        """
        if "m" in period:
            numb = period.translate(str.maketrans("", "", string.ascii_letters))
            numb = int(numb)
            if numb == 1:
                return "1m"
            elif numb == 5:
                return "5m"
            elif numb == 30:
                return "30m"
            else:
                # 对于其他分钟数，向上取整到最近的支持的周期
                if numb < 5:
                    return "1m"
                elif numb < 30:
                    return "5m"
                else:
                    return "30m"
        elif period == "1d":
            return period
        elif "tick" == period:
            return period
        else:
            raise ValueError("周期传入错误")
    
    @staticmethod
    def convert_date_for_api(date_str):
        """
        将显示格式日期转换为API格式
        
        Args:
            date_str: 日期字符串，支持 "yyyy-mm-dd" 或 "yyyyMMdd" 格式
            
        Returns:
            str: "yyyyMMdd" 格式的日期字符串
        """
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            return date_obj.strftime('%Y%m%d')
        except ValueError:
            return date_str

    def download_data(self, stock_list, period, start_date='', end_date=''):
        """
        下载股票数据
        
        Args:
            stock_list: xtquant格式的股票代码列表，如 ["159915.SZ", "510300.SH"]
            period: 周期，如 "1m", "5m", "30m", "1d"
            start_date: 开始日期，格式 "yyyyMMdd" 或 "yyyy-mm-dd"
            end_date: 结束日期，格式 "yyyyMMdd" 或 "yyyy-mm-dd"
            
        Returns:
            dict: {股票代码: DataFrame}
        """
        try:
            # 如果是30分钟数据，先下载5分钟数据并合成
            if period == "30m":
                # 下载5分钟数据
                five_min_data = self.download_five_min_data(stock_list, start_date, end_date)
                if not five_min_data:
                    return None
                    
                # 合成30分钟数据
                return self.merge_to_30min(five_min_data)
            else:
                # 其他周期直接下载
                return self.my_download(stock_list, period, start_date, end_date)
                
        except Exception as e:
            print(f"下载数据时出错: {e}")
            return None

    def download_five_min_data(self, stock_list, start_date, end_date):
        """下载5分钟数据"""
        try:
            # 先检查本地数据
            need_download = []
            valid_data = {}
            used_local_data = set()
            for code in stock_list:
                try:
                    local_data = xtdata.get_local_data(
                        field_list=['time', 'open', 'high', 'low', 'close', 'volume', 'amount'],
                        stock_list=[code],
                        period='5m',
                        start_time=start_date,
                        end_time=end_date,
                        count=-1,
                        dividend_type='none',
                        fill_data=True,
                        data_dir=self.data_path
                    )
                    if local_data and code in local_data and not local_data[code].empty:
                        df = local_data[code].copy()
                        if not df.empty and self._is_data_complete(df, start_date, end_date, code):
                            valid_data[code] = df
                            used_local_data.add(code)
                        else:
                            need_download.append(code)
                    else:
                        need_download.append(code)
                except Exception as e:
                    print(f"检查{code}本地数据时出错: {e}")
                    need_download.append(code)
            # 下载缺失的数据
            if need_download:
                for code in need_download:
                    try:
                        xtdata.download_history_data(code, period='5m', start_time=start_date, end_time=end_date, incrementally=True)
                        time.sleep(0.1)  # 避免请求过快
                    except Exception as e:
                        print(f"下载{code}数据时出错: {e}")
                downloaded_data = xtdata.get_market_data_ex(
                    [],
                    need_download,
                    period='5m',
                    start_time=start_date,
                    end_time=end_date
                )
                if downloaded_data:
                    for code in need_download:
                        if code in downloaded_data and not downloaded_data[code].empty:
                            valid_data[code] = downloaded_data[code].copy()
            valid_data['_used_local_data'] = used_local_data
            return valid_data if len(valid_data) > 1 else None
        except Exception as e:
            print(f"下载5分钟数据时出错: {e}")
            return None
            
    def merge_to_30min(self, five_min_data):
        """将5分钟数据合成为30分钟数据"""
        try:
            merged_data = {}
            
            for code, df in five_min_data.items():
                if code == '_used_local_data':
                    merged_data[code] = df
                    continue
                    
                if df is None or df.empty:
                    continue
                
                # 确保时间列是datetime类型
                df['time'] = pd.to_datetime(df['time'], unit='ms')
                
                # 按30分钟重采样，使用'min'替代'T'
                resampled = df.resample('30min', on='time').agg({
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last',
                    'volume': 'sum',
                    'amount': 'sum'
                }).dropna()
                
                # 转换回时间戳
                resampled['time'] = resampled.index.astype(np.int64) // 10**6
                
                merged_data[code] = resampled.reset_index(drop=True)
            
            return merged_data
            
        except Exception as e:
            print(f"合成30分钟数据时出错: {e}")
            return None
            
    def _is_data_complete(self, df, start_date, end_date, stock_code=None):
        """检查数据是否完整"""
        try:
            if df.empty:
                if stock_code:
                    print(f"{stock_code} - 本地数据为空")
                return False
            
            # 转换日期格式并添加时区信息
            end_date = pd.to_datetime(end_date).tz_localize('Asia/Shanghai')
            
            print(f"\n检查 {stock_code} 数据完整性:")
            print(f"数据行数: {len(df)}")
            print(f"数据时间范围: {pd.to_datetime(df['time'].min(), unit='ms')} 至 {pd.to_datetime(df['time'].max(), unit='ms')}")
            
            # 获取交易日历
            trading_days_sh = xtdata.get_trading_dates(
                market='SH',
                start_time=(end_date - timedelta(days=30)).strftime('%Y%m%d'),
                end_time=end_date.strftime('%Y%m%d')
            )
            
            trading_days_sz = xtdata.get_trading_dates(
                market='SZ',
                start_time=(end_date - timedelta(days=30)).strftime('%Y%m%d'),
                end_time=end_date.strftime('%Y%m%d')
            )
            
            if not trading_days_sh and not trading_days_sz:
                if stock_code:
                    print(f"{stock_code} - 无法获取交易日历")
                return False
            
            # 合并两个市场的交易日历并排序
            trading_days = sorted(set(trading_days_sh + trading_days_sz))
            print(f"最近30个交易日数量: {len(trading_days)}")
            
            # 获取最近的交易日时间戳
            latest_trading_day = max(trading_days)
            latest_trading_day_str = pd.to_datetime(latest_trading_day/1000, unit='s', utc=True).tz_convert('Asia/Shanghai').strftime('%Y%m%d')
            
            # 获取本地数据的最后日期
            last_data_date = pd.to_datetime(df['time'].max()/1000, unit='s', utc=True).tz_convert('Asia/Shanghai')
            last_data_date_str = last_data_date.strftime('%Y%m%d')
            
            print(f"最近交易日: {latest_trading_day_str}")
            print(f"数据最后日期: {last_data_date_str}")
            
            # 判断本地数据的最后日期是否是最近交易日
            if last_data_date_str < latest_trading_day_str:
                if stock_code:
                    print(f"{stock_code} - 本地数据不是最新（本地数据截止日期：{last_data_date_str}，最近交易日：{latest_trading_day_str}）")
                return False
            
            print(f"{stock_code} - 数据完整性检查通过")
            return True
            
        except Exception as e:
            if stock_code:
                print(f"{stock_code} - 检查数据完整性时出错：{str(e)}")
            return False

    def my_download(self, stock_list, period, start_date='', end_date=''):
        """
        下载历史数据
        
        Args:
            stock_list: xtquant格式的股票代码列表
            period: 数据周期
            start_date: 开始日期
            end_date: 结束日期
            
        Note:
            这是一个内部方法，用于实际的数据下载操作
            包含下载进度显示和错误处理
        """
        try:
            data_dict = {}
            used_local_data = set()
            
            for code in stock_list:
                try:
                    # 下载历史数据
                    xtdata.download_history_data(code, period=period, start_time=start_date, end_time=end_date, incrementally=True)
                    time.sleep(0.1)  # 避免请求过快
                except Exception as e:
                    print(f"下载{code}数据时出错: {e}")
            
            # 获取市场数据
            data = xtdata.get_market_data_ex(
                [],
                stock_list,
                period=period,
                start_time=start_date,
                end_time=end_date
            )
            
            if data:
                for code in stock_list:
                    if code in data and not data[code].empty:
                        data_dict[code] = data[code].copy()
            
            data_dict['_used_local_data'] = used_local_data
            return data_dict if len(data_dict) > 1 else None
            
        except Exception as e:
            print(f"下载数据时出错: {e}")
            return None
    
    def subscribe_quote(self, stock_list, period):
        """
        订阅行情数据
        
        Args:
            stock_list: xtquant格式的股票代码列表
            period: 数据周期
            
        Note:
            订阅后需等待1秒确保订阅完成
        """
        for code in stock_list:
            xtdata.subscribe_quote(code, period=period)
        time.sleep(1)  # 等待订阅完成
    
    def get_market_data(self, stock_list, period, start_date, end_date):
        """
        获取市场数据
        
        Args:
            stock_list: xtquant格式的股票代码列表
            period: 数据周期
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            dict: {股票代码: DataFrame}
                DataFrame包含标准OHLCV数据
        
        Note:
            这是推荐的获取数据的主要方法
            自动处理字段列表和数据填充
        """
        field_list = ['time', 'open', 'high', 'low', 'close', 'volume', 'amount']
        return xtdata.get_market_data(
            field_list=field_list,
            stock_list=stock_list,
            period=period,
            start_time=start_date,
            end_time=end_date,
            count=-1,
            dividend_type='none',
            fill_data=True
        )

class StockCodeParser:
    """
    股票代码解析器
    
    用于处理不同格式的股票代码转换，支持:
    - 通达信格式 -> xtquant格式
    - ETF、股票等多种证券类型
    """
    
    @staticmethod
    def read_block_file(file_path, encoding='gbk'):
        """
        读取通达信板块文件
        
        Args:
            file_path: 板块文件路径(.blk)
            encoding: 文件编码，默认为'gbk'
            
        Returns:
            list: 股票代码列表（去掉前缀的原始代码）
        
        Raises:
            FileNotFoundError: 文件不存在时抛出
            Exception: 读取文件出错时抛出
        """
        try:
            code_list = []
            with open(file_path, 'r', encoding=encoding) as f:
                for line in f:
                    code = line.strip()
                    if len(code) > 0 and code[1:].isdigit():
                        code_list.append(code[1:])  # 去掉第一个字符
            return code_list
        except FileNotFoundError:
            print(f"找不到文件: {file_path}")
            raise
        except Exception as e:
            print(f"读取文件时出错: {e}")
            raise
    
    @staticmethod
    def convert_to_xtquant(code):
        """
        将通达信代码转换为xtquant格式
        
        Args:
            code: 6位数字的股票代码
            
        Returns:
            str: xtquant格式的代码（如: 600001.SH）
            None: 如果无法转换则返回None
            
        示例:
            >>> convert_to_xtquant('600001')
            '600001.SH'
            >>> convert_to_xtquant('000001')
            '000001.SZ'
        """
        if not code or not code.isdigit():
            return None
            
        # 上海证券交易所
        if code.startswith('6'):  # 上证A股
            return f"{code}.SH"
        elif code.startswith('5'):  # 上证ETF
            return f"{code}.SH"
        elif code.startswith('11'):  # 上证可转债
            return f"{code}.SH"
            
        # 深圳证券交易所
        elif code.startswith('0'):  # 深证主板
            return f"{code}.SZ"
        elif code.startswith('3'):  # 创业板
            return f"{code}.SZ"
        elif code.startswith('15'):  # 深证ETF
            return f"{code}.SZ"
        elif code.startswith('12'):  # 深证可转债
            return f"{code}.SZ"
            
        return None
    
    def read_stock_codes(self, file_path):
        """
        读取并转换股票代码
        
        Args:
            file_path: 通达信板块文件路径
            
        Returns:
            list: xtquant格式的股票代码列表
            
        Note:
            这是推荐使用的主要方法，会自动处理文件读取和代码转换
        """
        try:
            # 读取原始代码
            tdx_codes = self.read_block_file(file_path)
            
            # 转换为xtquant格式
            xtquant_codes = [
                code for code in (
                    self.convert_to_xtquant(code) 
                    for code in tdx_codes
                ) 
                if code is not None
            ]
            
            print(f"读取到 {len(xtquant_codes)} 个股票代码")
            return xtquant_codes
            
        except Exception as e:
            print(f"处理股票代码时出错: {e}")
            return []
    
    def read_stock_codes_by_type(self, file_path, stock_type=None):
        """
        按类型读取股票代码
        
        Args:
            file_path: 通达信板块文件路径
            stock_type: 股票类型，可选值：
                - 'ETF': 仅返回ETF
                - 'A': 仅返回A股
                - None: 返回所有代码
            
        Returns:
            list: 指定类型的xtquant格式股票代码列表
        """
        all_codes = self.read_stock_codes(file_path)
        
        if stock_type is None:
            return all_codes
            
        if stock_type.upper() == 'ETF':
            return [code for code in all_codes 
                   if code.startswith('5') or code.startswith('1')]
                   
        elif stock_type.upper() == 'A':
            return [code for code in all_codes 
                   if code.startswith('6') or code.startswith('0') or code.startswith('3')]
        
        return all_codes

class QMTConnector:
    """QMT连接管理器"""
    
    def __init__(self, qmt_path=r"e:\国金证券QMT交易端\bin.x64\XtItClient.exe"):
        """
        初始化连接管理器
        
        Args:
            qmt_path: QMT程序路径，默认为标准安装路径
        """
        self.qmt_path = qmt_path
        self.process_name = "xtminiqmt.exe"
    
    def check_process(self):
        """
        检查QMT进程是否运行
        
        Returns:
            bool: 进程是否存在
        """
        print("\n开始检查QMT进程...")
        
        for proc in psutil.process_iter(['name', 'pid']):
            try:
                if proc.info['name'].lower() == self.process_name.lower():
                    print(f"找到QMT进程: {proc.info['name']} (PID: {proc.info['pid']})")
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        
        return False
    
    def start_qmt(self):
        """
        启动QMT程序
        
        Returns:
            bool: 启动是否成功
        """
        try:
            if not os.path.exists(self.qmt_path):
                print(f"未找到QMT程序: {self.qmt_path}")
                messagebox.showerror("错误", "未找到QMT程序，请检查安装路径")
                return False
            
            print(f"找到QMT程序路径: {self.qmt_path}")
            subprocess.Popen(self.qmt_path)
            print("已执行QMT启动命令")
            
            messagebox.showinfo("提示", "QMT程序已启动！\n请确保登录后再点击确定。")
            return True
            
        except Exception as e:
            print(f"启动QMT程序失败: {e}")
            messagebox.showerror("错误", f"启动QMT程序失败: {e}")
            return False
    
    def ensure_connection(self):
        """
        确保与QMT的连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 尝试初始化连接
            DataDownloader()
            return True
            
        except Exception as e:
            print(f"xtquant连接失败: {e}")
            
            # 连接失败时检查并启动QMT
            if not self.check_process():
                if not self.start_qmt():
                    return False
            
            # 重试连接
            try:
                DataDownloader()
                return True
            except Exception as e:
                print(f"重试连接失败: {e}")
                return False

def check_cross_signals(df, current_price, code, last_cross_price=None):
    """
    检查均线交叉信号
    
    Args:
        df: 数据框
        current_price: 当前价格
        code: 股票代码
        last_cross_price: 上次交叉价格
        
    Returns:
        (是否交叉, 交叉类型, 当前均线值, 均线类型)
    """
    try:
        # 计算均价线
        df['amount_cumsum'] = df['amount'].cumsum()
        df['volume_cumsum'] = df['volume'].cumsum()
        
        # 根据股票类型调整均价除数
        if code.startswith(('15', '5')):  # ETF
            df['avg_price'] = df['amount_cumsum'] / df['volume_cumsum'] / 100
        else:  # 可转债
            df['avg_price'] = df['amount_cumsum'] / df['volume_cumsum'] / 10
        
        # 获取最新的均价
        current_avg = df['avg_price'].iloc[-1]
        
        # 添加调试信息 - 修复格式化字符串
        last_cross_str = f"{last_cross_price:.2f}" if last_cross_price is not None else "None"
        print(f"调试 - {code}: 当前价格={current_price:.2f}, 均价={current_avg:.2f}, 上次交叉价格={last_cross_str}")
        
        # 如果没有上次交叉价格，返回False
        if last_cross_price is None:
            print(f"调试 - {code}: 没有上次交叉价格，返回False")
            return False, None, current_avg, 'avg'
            
        # 获取倒数第二个均价（用于判断趋势）
        if len(df) >= 2:
            prev_avg = df['avg_price'].iloc[-2]
        else:
            prev_avg = current_avg
            
        # 添加调试信息
        print(f"调试 - {code}: 前一均价={prev_avg:.2f}")
        
        # 判断是否发生黄金交叉（价格从下方穿过均线）
        if current_price > current_avg and last_cross_price <= prev_avg:
            print(f"调试 - {code}: 触发金叉 - 当前价格({current_price:.2f}) > 均价({current_avg:.2f}) 且 上次价格({last_cross_price:.2f}) <= 前一均价({prev_avg:.2f})")
            return True, 'golden', current_avg, 'avg'
            
        # 判断是否发生死亡交叉（价格从上方穿过均线）
        if current_price < current_avg and last_cross_price >= prev_avg:
            print(f"调试 - {code}: 触发死叉 - 当前价格({current_price:.2f}) < 均价({current_avg:.2f}) 且 上次价格({last_cross_price:.2f}) >= 前一均价({prev_avg:.2f})")
            return True, 'death', current_avg, 'avg'
            
        # 添加调试信息 - 未触发交叉
        print(f"调试 - {code}: 未触发交叉 - 条件不满足")
        return False, None, current_avg, 'avg'
        
    except Exception as e:
        print(f"检查交叉信号时出错: {str(e)}")
        return False, None, None, None

def get_time_series_data(stock_code: str, my_download_func) -> pd.DataFrame:
    """
    获取股票的分时数据
    
    Args:
        stock_code: 股票代码（xtquant格式）
        my_download_func: 下载数据的函数
        
    Returns:
        pd.DataFrame: 包含分时数据的DataFrame，包含以下列：
            - time: 时间戳
            - open: 开盘价
            - high: 最高价
            - low: 最低价
            - close: 收盘价
            - volume: 成交量
            - amount: 成交额
    """
    try:
        # 获取当前日期
        today = datetime.now().strftime('%Y%m%d')
        
        # 下载今日分时数据
        my_download_func([stock_code], period='1m', start_date=today, end_date=today)
        
        # 获取数据
        data = xtdata.get_market_data_ex(
            [],  # 使用默认字段列表
            [stock_code],
            period='1m',
            start_time=today,
            end_time=today
        )
        
        if data is None or stock_code not in data or data[stock_code].empty:
            raise ValueError("未获取到分时数据")
            
        return data[stock_code]
        
    except Exception as e:
        raise Exception(f"获取分时数据失败: {str(e)}")

def show_time_series(stock_code, my_download_func):
    """显示分时图的主函数"""
    try:
        # 获取分时数据
        df = get_time_series_data(stock_code, my_download_func)
        
        # 绘制分时图
        plot_time_series(df, f"{stock_code} 分时图")
        
    except Exception as e:
        raise Exception(f"显示分时图失败: {str(e)}")

def calculate_ema(prices: pd.Series, period: int = 12) -> pd.Series:
    """
    计算指数移动平均线(EMA)
    
    Args:
        prices: 价格序列
        period: EMA周期，默认12
        
    Returns:
        EMA指标序列
    """
    try:
        # EMA = (当日价格 × α) + (昨日EMA × (1-α))
        # α = 2/(周期数+1)
        alpha = 2 / (period + 1)
        return prices.ewm(alpha=alpha, adjust=False).mean()
    except Exception as e:
        print(f"计算EMA时出错: {e}")
        return pd.Series()

def calculate_atr(df: pd.DataFrame, period: int = 14) -> pd.Series:
    """
    计算真实波幅(ATR)
    
    Args:
        df: 包含OHLC数据的DataFrame
        period: ATR周期，默认14
        
    Returns:
        ATR指标序列
    """
    try:
        # 确保数据类型正确
        for col in ['high', 'low', 'close']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
            
        # 计算真实波幅(TR)
        high_low = df['high'] - df['low']
        high_close = abs(df['high'] - df['close'].shift())
        low_close = abs(df['low'] - df['close'].shift())
        
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        
        # 计算ATR
        atr = tr.ewm(alpha=1/period, adjust=False).mean()
        
        return atr
    except Exception as e:
        print(f"计算ATR时出错: {e}")
        return pd.Series()

class BTIndicators:
    """Backtrader指标计算类
    使用backtrader内置指标确保与回测结果一致
    """
    
    @staticmethod
    def calculate_ema(data, period=12):
        """计算EMA指标
        Args:
            data: OHLCV数据
            period: 周期
        Returns:
            EMA值序列
        """
        cerebro = bt.Cerebro()
        cerebro.adddata(bt.feeds.PandasData(dataname=data))
        
        class EMAStrategy(bt.Strategy):
            def __init__(self):
                self.ema = bt.indicators.EMA(self.data.close, period=period)
                
        cerebro.addstrategy(EMAStrategy)
        results = cerebro.run()
        return results[0].ema.array
        
    @staticmethod    
    def calculate_atr(data, period=14):
        """计算ATR指标
        Args:
            data: OHLCV数据
            period: 周期
        Returns:
            ATR值序列
        """
        cerebro = bt.Cerebro()
        cerebro.adddata(bt.feeds.PandasData(dataname=data))
        
        class ATRStrategy(bt.Strategy):
            def __init__(self):
                self.atr = bt.indicators.ATR(self.data, period=period)
                
        cerebro.addstrategy(ATRStrategy)
        results = cerebro.run()
        return results[0].atr.array
        
    @staticmethod
    def calculate_rsi(data, period=14):
        """计算RSI指标
        Args:
            data: OHLCV数据
            period: 周期
        Returns:
            RSI值序列
        """
        cerebro = bt.Cerebro()
        cerebro.adddata(bt.feeds.PandasData(dataname=data))
        
        class RSIStrategy(bt.Strategy):
            def __init__(self):
                self.rsi = bt.indicators.RSI(self.data.close, period=period)
                
        cerebro.addstrategy(RSIStrategy)
        results = cerebro.run()
        return results[0].rsi.array

    @staticmethod
    def calculate_bollinger_bands(data, period=20):
        """计算布林带指标
        Args:
            data: OHLCV数据
            period: 周期
        Returns:
            (上轨,中轨,下轨)值序列
        """
        cerebro = bt.Cerebro()
        cerebro.adddata(bt.feeds.PandasData(dataname=data))
        
        class BBStrategy(bt.Strategy):
            def __init__(self):
                self.bb = bt.indicators.BollingerBands(self.data.close, period=period)
                
        cerebro.addstrategy(BBStrategy)
        results = cerebro.run()
        return (results[0].bb.top.array,
                results[0].bb.mid.array,
                results[0].bb.bot.array)

    @staticmethod
    def calculate_macd(data, period_me1=12, period_me2=26, period_signal=9):
        """计算MACD指标
        Args:
            data: OHLCV数据
            period_me1: 快线周期
            period_me2: 慢线周期
            period_signal: 信号线周期
        Returns:
            (MACD,Signal,Histogram)值序列
        """
        cerebro = bt.Cerebro()
        cerebro.adddata(bt.feeds.PandasData(dataname=data))
        
        class MACDStrategy(bt.Strategy):
            def __init__(self):
                self.macd = bt.indicators.MACD(
                    self.data.close,
                    period_me1=period_me1,
                    period_me2=period_me2,
                    period_signal=period_signal
                )
                
        cerebro.addstrategy(MACDStrategy)
        results = cerebro.run()
        return (results[0].macd.macd.array,
                results[0].macd.signal.array,
                results[0].macd.histo.array) 

    @staticmethod
    def check_trading_signals(df):
        """检查交易信号"""
        try:
            # 获取当前价格
            current_price = df.iloc[-1]['close']
            
            # 使用BTIndicators类计算指标
            ema_short = BTIndicators.calculate_ema(df, period=12)
            ema_long = BTIndicators.calculate_ema(df, period=50)
            atr = BTIndicators.calculate_atr(df, period=14)
            
            # 获取最新的指标值
            current_ema_short = ema_short[-1]
            current_ema_long = ema_long[-1]
            current_atr = atr[-1]
            
            # 计算交叉信号
            ema_cross = (ema_short[-2] <= ema_long[-2] and 
                        ema_short[-1] > ema_long[-1])
            
            atr_cross = (atr[-2] <= 0.8 and atr[-1] > 0.8)
            
            # 输出指标数据
            print(f"当前价格: {current_price:.3f}, EMA12: {current_ema_short:.3f}, EMA50: {current_ema_long:.3f}, ATR: {current_atr:.3f}")
            
            # 检查买入信号
            if ema_cross and current_atr > 0.8:
                print("EMA金叉且ATR > 0.8")
                return True, current_price
                
            if atr_cross and current_ema_short > current_ema_long and current_price > current_ema_short:
                print("ATR上穿0.8且多头排列")
                return True, current_price
                
            # 检查卖出信号
            if current_ema_short < current_ema_long and current_atr > 0.8:
                print("EMA死叉且ATR > 0.8")
                return True, current_price
                
            if current_price < current_ema_long and current_atr > 0.8:
                print("价格跌破EMA50且ATR > 0.8")
                return True, current_price
                
            return False, None
            
        except Exception as e:
            print(f"检查交易信号失败: {str(e)}")
            return False, None 

def my_download(stock_list: list, period: str, start_date='', end_date=''):
    """
    下载数据并显示进度
    
    Args:
        stock_list: 股票代码列表
        period: 数据周期，支持:
            - "1d","w","mon","q","y" -> "1d"
            - "1m"-"4m" -> "1m"
            - "5m"及以上 -> "5m"
            - "tick" -> "tick"
        start_date: 开始日期
        end_date: 结束日期
    """
    import string
    
    # 标准化周期参数
    if [i for i in ["d","w","mon","q","y"] if i in period]:
        period = "1d"
    elif "m" in period:
        numb = period.translate(str.maketrans("", "", string.ascii_letters))
        if int(numb) < 5:
            period = "1m"
        else:
            period = "5m"
    elif "tick" == period:
        pass
    else:
        raise KeyboardInterrupt("周期传入错误")

    # 显示下载进度
    n = 1
    num = len(stock_list)
    for i in stock_list:
        print(f"当前正在下载 {period} {n}/{num}")
        xtdata.download_history_data(i, period, start_date, end_date)
        n += 1
    print("下载结束") 