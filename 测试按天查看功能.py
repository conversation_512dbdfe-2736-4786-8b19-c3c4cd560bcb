#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试按天查看往日记录功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import json
from datetime import datetime, timedelta

class DailyViewDemo:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("按天查看往日记录功能演示")
        self.root.geometry("900x700")
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="按天查看往日交易记录功能演示", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 功能说明
        info_text = """
新增功能：
1. 在统计报表中添加"按天查看"选项
2. 显示所有可用交易日期的列表
3. 显示每个日期的交易记录数和总盈亏
4. 支持选择特定日期查看详细交易记录
5. 按股票分组统计单日交易情况
6. 支持导出单日交易记录为CSV文件
        """
        info_label = ttk.Label(main_frame, text=info_text, justify="left")
        info_label.pack(pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(0, 20))
        
        # 测试按钮
        ttk.Button(button_frame, text="扫描可用日期", 
                  command=self.scan_dates).pack(side="left", padx=(0, 10))
        ttk.Button(button_frame, text="模拟日期选择器", 
                  command=self.show_date_selector).pack(side="left", padx=(0, 10))
        ttk.Button(button_frame, text="查看今日记录", 
                  command=self.view_today).pack(side="left")
        
        # 结果显示区域
        self.result_text = tk.Text(main_frame, height=30, width=100)
        self.result_text.pack(fill="both", expand=True)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="right", fill="y")
    
    def add_result(self, text):
        """添加结果到显示区域"""
        self.result_text.insert(tk.END, text + "\n")
        self.result_text.see(tk.END)
        self.root.update()
    
    def clear_results(self):
        """清空结果显示"""
        self.result_text.delete(1.0, tk.END)
    
    def scan_dates(self):
        """扫描可用的交易日期"""
        self.clear_results()
        self.add_result("=== 扫描可用的交易日期 ===")
        
        available_dates = []
        
        # 扫描交易历史文件
        for filename in os.listdir("."):
            if filename.startswith("交易历史_") and filename.endswith(".json"):
                # 提取日期
                date_str = filename.replace("交易历史_", "").replace(".json", "")
                
                try:
                    # 加载文件获取记录数和总盈亏
                    with open(filename, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content:
                            records = json.loads(content)
                            record_count = len(records)
                            total_profit = sum(record.get('profit', 0) for record in records)
                            
                            available_dates.append({
                                'date': date_str,
                                'filename': filename,
                                'record_count': record_count,
                                'total_profit': total_profit
                            })
                except Exception as e:
                    self.add_result(f"读取文件 {filename} 失败: {str(e)}")
                    continue
        
        if not available_dates:
            self.add_result("未找到任何交易数据文件")
            return
        
        # 按日期排序（最新的在前）
        available_dates.sort(key=lambda x: x['date'], reverse=True)
        
        self.add_result(f"找到 {len(available_dates)} 个交易日期:")
        self.add_result("")
        self.add_result("日期\t\t星期\t记录数\t总盈亏")
        self.add_result("-" * 50)
        
        weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        for date_info in available_dates:
            date_str = date_info['date']
            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                formatted_date = date_obj.strftime('%Y-%m-%d')
                weekday = weekdays[date_obj.weekday()]
            except:
                formatted_date = date_str
                weekday = "未知"
            
            profit_str = f"{date_info['total_profit']:+.2f}元"
            self.add_result(f"{formatted_date}\t{weekday}\t{date_info['record_count']}条\t{profit_str}")
    
    def show_date_selector(self):
        """显示日期选择器模拟"""
        try:
            # 创建日期选择窗口
            selector_window = tk.Toplevel(self.root)
            selector_window.title("选择查看日期")
            selector_window.geometry("600x500")
            
            # 创建主框架
            main_frame = ttk.Frame(selector_window)
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)
            
            # 标题
            title_label = ttk.Label(main_frame, text="选择要查看的交易日期", 
                                   font=("Arial", 14, "bold"))
            title_label.pack(pady=(0, 15))
            
            # 扫描可用的交易数据文件
            available_dates = self.scan_available_dates()
            
            if not available_dates:
                ttk.Label(main_frame, text="未找到任何交易数据文件", 
                         font=("Arial", 12)).pack(pady=20)
                ttk.Button(main_frame, text="关闭", 
                          command=selector_window.destroy).pack(pady=10)
                return
            
            # 创建日期列表框架
            list_frame = ttk.LabelFrame(main_frame, text="可用的交易日期", padding=10)
            list_frame.pack(fill="both", expand=True, pady=(0, 10))
            
            # 创建表格显示日期和记录数
            columns = ("日期", "星期", "交易记录数", "总盈亏")
            tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=12)
            
            # 设置列标题和宽度
            tree.heading("日期", text="日期")
            tree.heading("星期", text="星期")
            tree.heading("交易记录数", text="交易记录数")
            tree.heading("总盈亏", text="总盈亏(元)")
            
            tree.column("日期", width=120, anchor="center")
            tree.column("星期", width=80, anchor="center")
            tree.column("交易记录数", width=100, anchor="center")
            tree.column("总盈亏", width=120, anchor="center")
            
            # 添加滚动条
            scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)
            
            # 填充数据
            weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
            for date_info in available_dates:
                date_str = date_info['date']
                try:
                    date_obj = datetime.strptime(date_str, '%Y%m%d')
                    formatted_date = date_obj.strftime('%Y-%m-%d')
                    weekday = weekdays[date_obj.weekday()]
                except:
                    formatted_date = date_str
                    weekday = "未知"
                
                total_profit = date_info['total_profit']
                profit_color = "+" if total_profit > 0 else ""
                
                values = (
                    formatted_date,
                    weekday,
                    f"{date_info['record_count']}条",
                    f"{profit_color}{total_profit:.2f}"
                )
                
                item = tree.insert("", "end", values=values)
                if total_profit > 0:
                    tree.set(item, "总盈亏", f"+{total_profit:.2f}")
            
            # 布局表格和滚动条
            tree.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")
            
            # 按钮框架
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill="x", pady=(10, 0))
            
            # 查看按钮
            def view_selected_date():
                selection = tree.selection()
                if not selection:
                    messagebox.showwarning("提示", "请选择一个日期")
                    return
                
                item = tree.item(selection[0])
                date_display = item['values'][0]
                messagebox.showinfo("演示", f"将查看 {date_display} 的详细交易记录")
                selector_window.destroy()
            
            view_btn = ttk.Button(button_frame, text="查看选中日期", command=view_selected_date)
            view_btn.pack(side="left", padx=(0, 10))
            
            close_btn = ttk.Button(button_frame, text="关闭", command=selector_window.destroy)
            close_btn.pack(side="right")
            
            # 双击事件
            def on_double_click(event):
                view_selected_date()
            
            tree.bind("<Double-1>", on_double_click)
            
        except Exception as e:
            messagebox.showerror("错误", f"显示日期选择器失败: {str(e)}")
    
    def scan_available_dates(self):
        """扫描可用的交易数据文件"""
        try:
            available_dates = []
            
            for filename in os.listdir("."):
                if filename.startswith("交易历史_") and filename.endswith(".json"):
                    date_str = filename.replace("交易历史_", "").replace(".json", "")
                    
                    try:
                        with open(filename, 'r', encoding='utf-8') as f:
                            content = f.read().strip()
                            if content:
                                records = json.loads(content)
                                record_count = len(records)
                                total_profit = sum(record.get('profit', 0) for record in records)
                                
                                available_dates.append({
                                    'date': date_str,
                                    'filename': filename,
                                    'record_count': record_count,
                                    'total_profit': total_profit
                                })
                    except Exception as e:
                        continue
            
            available_dates.sort(key=lambda x: x['date'], reverse=True)
            return available_dates
            
        except Exception as e:
            return []
    
    def view_today(self):
        """查看今日记录"""
        self.clear_results()
        today = datetime.now().strftime('%Y%m%d')
        trade_file = f"交易历史_{today}.json"
        
        self.add_result(f"=== 查看今日({today})交易记录 ===")
        
        if not os.path.exists(trade_file):
            self.add_result(f"未找到今日交易数据文件: {trade_file}")
            return
        
        try:
            with open(trade_file, 'r', encoding='utf-8') as f:
                trade_records = json.loads(f.read())
            
            if not trade_records:
                self.add_result("今日没有交易记录")
                return
            
            # 显示统计信息
            total_trades = len(trade_records)
            total_profit = sum(record.get('profit', 0) for record in trade_records)
            profit_trades = sum(1 for record in trade_records if record.get('profit', 0) > 0)
            loss_trades = total_trades - profit_trades
            win_rate = (profit_trades / total_trades * 100) if total_trades > 0 else 0
            
            self.add_result(f"总交易: {total_trades}次")
            self.add_result(f"盈利: {profit_trades}次")
            self.add_result(f"亏损: {loss_trades}次")
            self.add_result(f"胜率: {win_rate:.1f}%")
            self.add_result(f"总盈亏: {total_profit:.2f}元")
            self.add_result("")
            
            # 显示交易记录
            self.add_result("=== 交易记录明细 ===")
            self.add_result("股票代码\t买入价\t卖出价\t数量\t盈亏")
            self.add_result("-" * 50)
            
            for record in trade_records:
                code = record.get('code', '')
                buy_price = record.get('buy_price', 0)
                sell_price = record.get('sell_price', 0)
                quantity = record.get('quantity', 0)
                profit = record.get('profit', 0)
                
                self.add_result(f"{code}\t{buy_price:.3f}\t{sell_price:.3f}\t{quantity}\t{profit:+.2f}")
            
        except Exception as e:
            self.add_result(f"读取今日交易记录失败: {str(e)}")
    
    def run(self):
        """运行演示"""
        self.root.mainloop()

if __name__ == "__main__":
    demo = DailyViewDemo()
    demo.run()
