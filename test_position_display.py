#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试持仓显示的脚本
"""

import json
import os
from datetime import datetime

def main():
    """主函数"""
    print("开始测试持仓显示...")
    
    # 检查连接状态
    status_file = "connection_status.json"
    if os.path.exists(status_file):
        try:
            with open(status_file, 'r', encoding='utf-8') as f:
                status = json.load(f)
                print(f"连接状态: {status}")
                connected = status.get('connected', False)
        except Exception as e:
            print(f"读取连接状态文件失败: {str(e)}")
            connected = False
    else:
        print("未找到连接状态文件，假设未连接")
        connected = False
    
    # 读取持仓记录
    position_file = "自选股30m系统持仓记录.json"
    if os.path.exists(position_file):
        try:
            with open(position_file, 'r', encoding='utf-8') as f:
                position_records = json.load(f)
            print(f"读取 {len(position_records)} 条持仓记录")
            
            # 分析持仓记录
            real_positions = 0
            virtual_positions = 0
            
            for code, info in position_records.items():
                if 'buy_queue' in info and info['buy_queue']:
                    first_buy = info['buy_queue'][0]
                    is_virtual = first_buy.get('virtual', False)
                    is_real_trade = first_buy.get('real_trade', False)
                    
                    if is_virtual:
                        virtual_positions += 1
                        print(f"{code}: 虚拟持仓")
                    elif is_real_trade or not is_virtual:
                        real_positions += 1
                        print(f"{code}: 实际持仓")
                else:
                    is_virtual = info.get('virtual', False)
                    if is_virtual:
                        virtual_positions += 1
                        print(f"{code}: 虚拟持仓 (旧格式)")
                    else:
                        real_positions += 1
                        print(f"{code}: 实际持仓 (旧格式)")
            
            print(f"\n持仓统计:")
            print(f"  实际持仓: {real_positions}")
            print(f"  虚拟持仓: {virtual_positions}")
            print(f"  总持仓: {real_positions + virtual_positions}")
            
            # 模拟显示逻辑
            print(f"\n模拟显示逻辑:")
            if connected:
                print("连接状态: 已连接，将查询服务器持仓")
                print("如果查询失败，应显示本地实际持仓")
                print(f"  应显示实际持仓: {real_positions}个")
            else:
                print("连接状态: 未连接，将显示本地持仓")
                if real_positions > 0:
                    print(f"  应显示实际持仓: {real_positions}个")
                else:
                    print(f"  应显示虚拟持仓: {virtual_positions}个")
        
        except Exception as e:
            print(f"读取持仓记录失败: {str(e)}")
    else:
        print(f"持仓记录文件不存在: {position_file}")

if __name__ == "__main__":
    main()
