#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试重买标注修改效果
"""

import json
import sys
import os

def test_rebuy_label_changes():
    """测试重买标注的修改效果"""
    
    print("=== 测试重买标注修改效果 ===\n")
    
    # 读取当前持仓记录
    position_file = "可转债放量方法持仓记录.json"
    if not os.path.exists(position_file):
        print(f"❌ 持仓记录文件不存在: {position_file}")
        return
    
    with open(position_file, 'r', encoding='utf-8') as f:
        records = json.load(f)
    
    print(f"📁 读取持仓记录，共 {len(records)} 条")
    
    # 分析不同类型的持仓
    tech_rebuy_stocks = []
    double_buy_stocks = []
    normal_stocks = []
    signal_only_stocks = []
    
    for code, info in records.items():
        is_tech_rebuy = info.get('tech_rebuy_from_loss', False)
        is_double_buy = info.get('is_double_buy', False)
        is_signal_only = info.get('is_signal_only', False)
        total_quantity = info.get('total_quantity', 0)
        
        if is_signal_only:
            signal_only_stocks.append(code)
        elif is_tech_rebuy:
            tech_rebuy_stocks.append((code, total_quantity, info))
        elif is_double_buy:
            double_buy_stocks.append((code, total_quantity, info))
        else:
            normal_stocks.append((code, total_quantity, info))
    
    print(f"\n🔄 技术重买股票 ({len(tech_rebuy_stocks)} 只) - 应显示[重买]标注:")
    for code, qty, info in tech_rebuy_stocks:
        buy_time = info['buy_queue'][0]['buy_time']
        buy_price = info['buy_queue'][0]['buy_price']
        total_cost = info.get('total_cost', 0)
        original_loss = info.get('original_loss', 0)
        print(f"  {code}: 数量{qty}股, 价格{buy_price:.3f}, 成本{total_cost:.2f}元, 时间{buy_time}")
        print(f"    原亏损: {abs(original_loss):.2f}元")
    
    print(f"\n📈 双倍买入股票 ({len(double_buy_stocks)} 只) - 应取消[双倍]标注:")
    for code, qty, info in double_buy_stocks:
        buy_time = info['buy_queue'][0]['buy_time']
        buy_price = info['buy_queue'][0]['buy_price']
        total_cost = info.get('total_cost', 0)
        print(f"  {code}: 数量{qty}股, 价格{buy_price:.3f}, 成本{total_cost:.2f}元, 时间{buy_time}")
    
    print(f"\n📊 普通持仓股票 ({len(normal_stocks)} 只) - 无特殊标注:")
    for code, qty, info in normal_stocks:
        buy_time = info['buy_queue'][0]['buy_time']
        buy_price = info['buy_queue'][0]['buy_price']
        total_cost = info.get('total_cost', 0)
        print(f"  {code}: 数量{qty}股, 价格{buy_price:.3f}, 成本{total_cost:.2f}元, 时间{buy_time}")
    
    print(f"\n🔍 CCI信号记录 ({len(signal_only_stocks)} 只) - 等待EXP3突破:")
    for code in signal_only_stocks:
        info = records[code]
        buy_time = info['buy_queue'][0]['buy_time']
        buy_price = info['buy_queue'][0]['buy_price']
        print(f"  {code}: 信号价格{buy_price:.3f}, 时间{buy_time}")
    
    print(f"\n=== 模拟持仓显示效果 ===")
    
    # 模拟display_position函数的标注逻辑
    for code, info in records.items():
        if info.get('total_quantity', 0) > 0:  # 只看实际持仓
            is_tech_rebuy = info.get('tech_rebuy_from_loss', False)
            is_double_buy = info.get('is_double_buy', False)  # 这个应该被忽略
            
            # 新的标注逻辑
            label = ""
            if is_tech_rebuy:
                label = "[重买]"
            # 不再显示[双倍]标注
            
            buy_price = info['buy_queue'][0]['buy_price']
            total_quantity = info.get('total_quantity', 0)
            
            print(f"  {label}{code} 数量:{total_quantity}股 成本:{buy_price:.3f}")
    
    print(f"\n=== 技术重买买入数量分析 ===")
    print("修改后的技术重买逻辑:")
    print("- 买入金额: 2个单位 × 6000元 = 12000元")
    print("- 最小数量: 20股")
    print("- 计算公式: int((12000 / 价格) // 10) * 10")
    
    # 计算当前技术重买股票的理论数量
    for code, qty, info in tech_rebuy_stocks:
        buy_price = info['buy_queue'][0]['buy_price']
        theoretical_qty = int((12000 / buy_price) // 10) * 10
        if theoretical_qty < 20:
            theoretical_qty = 20
        
        print(f"\n{code}:")
        print(f"  当前数量: {qty}股")
        print(f"  理论数量: {theoretical_qty}股 (按12000元计算)")
        print(f"  价格: {buy_price:.3f}")
        
        if qty != theoretical_qty:
            print(f"  ⚠️  数量不匹配，可能是旧记录")
        else:
            print(f"  ✅ 数量匹配新逻辑")

if __name__ == "__main__":
    test_rebuy_label_changes()
