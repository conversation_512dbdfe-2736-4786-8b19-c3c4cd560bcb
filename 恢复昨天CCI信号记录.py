#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
恢复昨天（07-31）被误清理的CCI信号记录
"""

import json
import os
from datetime import datetime

def restore_yesterday_cci_signals():
    """恢复昨天被误清理的CCI信号记录"""
    
    print("=== 恢复昨天（07-31）的CCI信号记录 ===\n")
    
    # 昨天的CCI信号记录（从日志中提取）
    yesterday_signals = [
        {"code": "123160.SZ", "price": 147.000, "time": "2025-07-31 09:30:07"},
        {"code": "111009.SH", "price": 117.501, "time": "2025-07-31 10:10:42"},
        {"code": "123128.SZ", "price": 120.028, "time": "2025-07-31 10:58:50"},
        {"code": "123162.SZ", "price": 136.956, "time": "2025-07-31 11:00:10"},
        {"code": "123130.SZ", "price": 145.666, "time": "2025-07-31 11:15:07"},
        {"code": "123052.SZ", "price": 155.986, "time": "2025-07-31 13:15:08"},
        {"code": "123232.SZ", "price": 143.200, "time": "2025-07-31 13:36:59"},
        {"code": "123251.SZ", "price": 149.774, "time": "2025-07-31 13:45:07"},
        {"code": "128109.SZ", "price": 145.599, "time": "2025-07-31 14:00:08"},
        {"code": "118021.SH", "price": 239.826, "time": "2025-07-31 14:25:08"},
        {"code": "123203.SZ", "price": 148.526, "time": "2025-07-31 14:30:08"},
        {"code": "123128.SZ", "price": 119.971, "time": "2025-07-31 14:40:08"},  # 注意：123128.SZ有两个信号
        {"code": "123141.SZ", "price": 136.100, "time": "2025-07-31 15:39:42"}
    ]
    
    # 读取当前持仓记录
    position_file = "可转债放量方法持仓记录.json"
    if os.path.exists(position_file):
        with open(position_file, 'r', encoding='utf-8') as f:
            position_records = json.load(f)
        print(f"📁 已读取当前持仓记录，共 {len(position_records)} 条")
    else:
        position_records = {}
        print("📁 持仓记录文件不存在，将创建新文件")
    
    # 备份当前持仓记录
    backup_file = f"可转债放量方法持仓记录_备份_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(position_records, f, ensure_ascii=False, indent=2)
    print(f"💾 已备份当前持仓记录到: {backup_file}")
    
    # 恢复CCI信号记录
    restored_count = 0
    skipped_count = 0
    
    for signal in yesterday_signals:
        code = signal["code"]
        price = signal["price"]
        time_str = signal["time"]
        
        # 检查是否已经存在该股票的记录
        if code in position_records:
            # 检查是否是实际持仓还是信号记录
            existing_record = position_records[code]
            if existing_record.get('total_quantity', 0) > 0:
                print(f"⚠️  {code} 已有实际持仓，跳过恢复CCI信号")
                skipped_count += 1
                continue
            elif existing_record.get('is_signal_only', False):
                # 检查现有信号的时间，如果是今天的信号，则用昨天的信号替换
                existing_time = existing_record['buy_queue'][0]['buy_time']
                if existing_time.startswith('2025-08-01'):
                    print(f"🔄 {code} 用昨天的CCI信号替换今天的信号")
                else:
                    print(f"⚠️  {code} 已存在昨天的CCI信号记录，跳过恢复")
                    skipped_count += 1
                    continue
        
        # 解析时间信息
        signal_time = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
        current_period = signal_time.minute // 5
        
        # 创建CCI信号记录（使用与create_signal_record相同的格式）
        position_records[code] = {
            'buy_queue': [
                {
                    'buy_price': float(price),
                    'buy_time': time_str,
                    'quantity': 0,  # 信号记录数量为0
                    'fee': 0.0,
                    'actual_amount': 0.0,
                    'order_id': f"CCI_SIGNAL_RESTORED_{int(signal_time.timestamp())}",
                    'virtual': True,
                    'below_exp3_at_buy': False,
                    'crossed_exp3': False
                }
            ],
            'total_quantity': 0,  # 信号记录总数量为0
            'total_cost': 0.0,
            'total_fee': 0.0,
            'order_price': float(price),
            'signal_date': signal_time.strftime('%Y-%m-%d'),  # 单独记录信号产生日期
            'buy_period': current_period,
            'buy_hour': signal_time.hour,
            'periods_since_buy': 0,
            'profit_check_done': False,
            'add_count': 0,  # 加仓次数
            'is_cci_signal': True,  # 标记为CCI信号产生的记录
            'is_signal_only': True,  # 标记为仅信号记录，不是实际持仓
            'restored_from_log': True  # 标记为从日志恢复
        }
        
        print(f"✅ 已恢复 {code} CCI信号记录: 价格{price:.3f}, 时间{time_str}")
        restored_count += 1
    
    # 保存更新后的持仓记录
    with open(position_file, 'w', encoding='utf-8') as f:
        json.dump(position_records, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 恢复完成:")
    print(f"   ✅ 成功恢复: {restored_count} 条CCI信号记录")
    print(f"   ⚠️  跳过恢复: {skipped_count} 条记录")
    print(f"   💾 备份文件: {backup_file}")
    print(f"   📁 更新文件: {position_file}")
    
    # 验证恢复结果
    print(f"\n🔍 验证恢复结果:")
    signal_only_count = sum(1 for pos in position_records.values() 
                           if pos.get('is_signal_only', False))
    actual_position_count = sum(1 for pos in position_records.values() 
                               if pos.get('total_quantity', 0) > 0)
    
    print(f"   📈 CCI信号记录: {signal_only_count} 条")
    print(f"   💰 实际持仓: {actual_position_count} 条")
    print(f"   📋 总记录数: {len(position_records)} 条")

if __name__ == "__main__":
    restore_yesterday_cci_signals()
