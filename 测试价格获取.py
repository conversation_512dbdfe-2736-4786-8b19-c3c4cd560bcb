#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试价格获取功能
"""

import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_position_loading():
    """测试持仓记录加载"""
    print("🔍 测试持仓记录加载...")
    
    try:
        with open('自选股30m系统持仓记录.json', 'r', encoding='utf-8') as f:
            position_records = json.load(f)
        
        print(f"✅ 成功加载持仓记录，共 {len(position_records)} 只股票")
        
        for code, info in position_records.items():
            print(f"📊 {code}:")
            if 'buy_queue' in info:
                print(f"  - 新格式：数量={info.get('total_quantity', 0)}")
                if info['buy_queue']:
                    print(f"  - 买入价格：{info['buy_queue'][0]['buy_price']}")
            else:
                print(f"  - 旧格式：数量={info.get('quantity', 0)}")
                print(f"  - 买入价格：{info.get('buy_price', 0)}")
        
        return position_records
        
    except Exception as e:
        print(f"❌ 加载持仓记录失败: {str(e)}")
        return {}

def test_price_api():
    """测试价格获取API"""
    print("\n🔍 测试价格获取API...")
    
    try:
        # 尝试导入xtdata
        from xtquant import xtdata
        print("✅ xtdata模块导入成功")
        
        # 测试获取一只股票的价格
        test_code = "510880.SH"
        print(f"📈 测试获取 {test_code} 的价格...")
        
        try:
            tick = xtdata.get_full_tick([test_code])
            if tick and test_code in tick:
                price = tick[test_code]["lastPrice"]
                print(f"✅ 实时价格获取成功: {price}")
                return True
            else:
                print("⚠️ 实时价格获取失败，数据为空")
        except Exception as e:
            print(f"⚠️ 实时价格获取失败: {str(e)}")
        
        # 尝试K线数据
        print(f"📊 尝试获取 {test_code} 的K线数据...")
        try:
            from datetime import datetime
            today = datetime.now().strftime('%Y%m%d')
            
            minute_data = xtdata.get_market_data(
                field_list=['time', 'close'],
                stock_list=[test_code],
                period='5m',
                start_time=today,
                end_time=today,
                count=-1
            )
            
            if minute_data and test_code in minute_data['time'].index:
                print(f"✅ K线数据获取成功，共{len(minute_data['time'].loc[test_code])}根K线")
                return True
            else:
                print("❌ K线数据获取失败")
                
        except Exception as e:
            print(f"❌ K线数据获取失败: {str(e)}")
        
        return False
        
    except ImportError:
        print("❌ xtdata模块导入失败，请检查是否正确安装")
        return False
    except Exception as e:
        print(f"❌ 价格获取API测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🧪 开始测试持仓显示问题...")
    
    # 测试持仓记录加载
    position_records = test_position_loading()
    
    # 测试价格获取API
    price_api_ok = test_price_api()
    
    print(f"\n📋 测试结果总结:")
    print(f"  - 持仓记录加载: {'✅ 正常' if position_records else '❌ 失败'}")
    print(f"  - 价格获取API: {'✅ 正常' if price_api_ok else '❌ 失败'}")
    
    if position_records and not price_api_ok:
        print("\n💡 建议:")
        print("  - 持仓记录正常，但价格获取失败")
        print("  - 请检查xtdata连接状态")
        print("  - 可能需要重启迅投QMT客户端")
    elif not position_records:
        print("\n💡 建议:")
        print("  - 持仓记录加载失败")
        print("  - 请检查持仓记录文件是否存在且格式正确")

if __name__ == "__main__":
    main()
