class SignalAnalyzer:
    def __init__(self, strategy_params):
        self.strategy_params = strategy_params
        self.cross_records = {}  # 记录每个代码的交叉状态
        
    def analyze_signals(self, code, df):
        """分析信号"""
        try:
            # 检查数据完整性
            if df is None or len(df) < 2:
                return None, None, None, None
            
            # 获取前一个周期的价格和指标值
            prev_price = round(df['close'].iloc[-2], 3)
            prev_exp1 = round(df['exp1'].iloc[-2], 3)
            prev_exp2 = round(df['exp2'].iloc[-2], 3)
            prev_exp3 = round(df['exp3'].iloc[-2], 3)
            
            # 获取当前周期的价格和指标值
            curr_price = round(df['close'].iloc[-1], 3)
            curr_exp1 = round(df['exp1'].iloc[-1], 3)
            curr_exp2 = round(df['exp2'].iloc[-1], 3)
            curr_exp3 = round(df['exp3'].iloc[-1], 3)
            
            # 判断价格在指标上方/下方
            prev_above_exp1 = prev_price > prev_exp1
            prev_above_exp3 = prev_price > prev_exp3
            curr_above_exp1 = curr_price > curr_exp1
            curr_above_exp3 = curr_price > curr_exp3
            
            # 使用前一个周期和当前周期的数据判断交叉
            cross_exp1 = not prev_above_exp1 and curr_above_exp1
            cross_exp3 = not prev_above_exp3 and curr_above_exp3
            
            # 构建分析结果
            analysis = {
                'code': code,
                'time': df.index[-1].strftime('%Y-%m-%d %H:%M:%S'),
                'period': f"{df.index[-2].strftime('%H:%M:%S')}-{df.index[-1].strftime('%H:%M:%S')}",
                'price': {
                    'current': curr_price,
                    'previous': prev_price
                },
                'exp1': {
                    'current': curr_exp1,
                    'previous': prev_exp1
                },
                'exp2': {
                    'current': curr_exp2,
                    'previous': prev_exp2
                },
                'exp3': {
                    'current': curr_exp3,
                    'previous': prev_exp3
                },
                'crosses': {
                    'exp1': cross_exp1,
                    'exp3': cross_exp3
                }
            }
            
            # 初始化交叉记录
            if code not in self.cross_records:
                self.cross_records[code] = {'first_cross': None, 'wait_periods': 0}
            
            # 检查是否满足exp1或exp3小于exp2的条件
            exp_condition = prev_exp1 < prev_exp2 or prev_exp3 < prev_exp2
            
            # 如果是第一次上穿
            if (cross_exp1 or cross_exp3) and not self.cross_records[code]['first_cross'] and exp_condition:
                self.cross_records[code]['first_cross'] = 'exp1' if cross_exp1 else 'exp3'
                self.cross_records[code]['wait_periods'] = 0
                return analysis, 'first_cross', curr_price, 'EXP1' if cross_exp1 else 'EXP3'
            
            # 如果已经有第一次上穿，等待第二次上穿
            elif self.cross_records[code]['first_cross']:
                second_cross = (cross_exp3 if self.cross_records[code]['first_cross'] == 'exp1' else cross_exp1)
                if second_cross and exp_condition:
                    self.cross_records[code]['first_cross'] = None
                    self.cross_records[code]['wait_periods'] = 0
                    return analysis, 'golden', curr_exp1, 'EXP1'
                else:
                    self.cross_records[code]['wait_periods'] += 1
                    if self.cross_records[code]['wait_periods'] >= self.strategy_params['max_wait_periods']:
                        self.cross_records[code]['first_cross'] = None
                        self.cross_records[code]['wait_periods'] = 0
            
            # 检查同时上穿
            if cross_exp1 and cross_exp3 and exp_condition:
                return analysis, 'golden', curr_exp1, 'EXP1'
            
            return analysis, None, None, None
            
        except Exception as e:
            print(f"分析信号失败: {str(e)}")
            return None, None, None, None 