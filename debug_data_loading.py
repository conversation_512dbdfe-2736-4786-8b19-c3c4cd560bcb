#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据加载问题的脚本
"""

import json
import os
from datetime import datetime

def check_position_records():
    """检查持仓记录文件"""
    position_file = "自选股30m系统持仓记录.json"
    
    print("=== 检查持仓记录文件 ===")
    if os.path.exists(position_file):
        print(f"文件存在: {position_file}")
        try:
            with open(position_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    print("文件为空")
                    return {}
                else:
                    position_records = json.loads(content)
                    print(f"成功加载 {len(position_records)} 条持仓记录")
                    
                    for code, info in position_records.items():
                        print(f"\n股票代码: {code}")
                        if 'buy_queue' in info:
                            print(f"  新格式: buy_queue有 {len(info['buy_queue'])} 条记录")
                            if info['buy_queue']:
                                first_buy = info['buy_queue'][0]
                                print(f"  第一条买入: 价格={first_buy['buy_price']:.4f}, 数量={first_buy['quantity']}, virtual={first_buy.get('virtual', 'N/A')}")
                        else:
                            print(f"  旧格式: buy_price={info.get('buy_price', 'N/A')}, quantity={info.get('quantity', 'N/A')}, virtual={info.get('virtual', 'N/A')}")
                    
                    return position_records
        except Exception as e:
            print(f"加载持仓记录失败: {str(e)}")
            return {}
    else:
        print(f"文件不存在: {position_file}")
        return {}

def check_trade_history():
    """检查交易历史文件"""
    trade_file = "自选股30m系统永久交易历史.jsonl"
    
    print("\n=== 检查交易历史文件 ===")
    if os.path.exists(trade_file):
        print(f"文件存在: {trade_file}")
        try:
            trade_records = []
            with open(trade_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line:
                        try:
                            trade_record = json.loads(line)
                            trade_records.append(trade_record)
                        except json.JSONDecodeError:
                            print(f"第{line_num}行格式错误: {line}")
                            continue
            
            print(f"成功加载 {len(trade_records)} 条交易记录")
            
            total_profit = 0
            for i, record in enumerate(trade_records, 1):
                print(f"\n交易记录 {i}:")
                print(f"  股票: {record.get('code', 'N/A')}")
                print(f"  买入价: {record.get('buy_price', 'N/A')}")
                print(f"  卖出价: {record.get('sell_price', 'N/A')}")
                print(f"  数量: {record.get('quantity', 'N/A')}")
                print(f"  盈亏: {record.get('profit', 'N/A')}")
                print(f"  虚拟: {record.get('virtual', 'N/A')}")
                
                if 'profit' in record:
                    total_profit += record['profit']
            
            print(f"\n总盈亏: {total_profit:.2f}")
            return trade_records
            
        except Exception as e:
            print(f"加载交易历史失败: {str(e)}")
            return []
    else:
        print(f"文件不存在: {trade_file}")
        return []

def check_stock_profiles():
    """检查股票档案文件"""
    profiles_dir = "stock_profiles"
    
    print("\n=== 检查股票档案文件 ===")
    if os.path.exists(profiles_dir):
        print(f"目录存在: {profiles_dir}")
        
        profile_files = [f for f in os.listdir(profiles_dir) if f.endswith('.json')]
        print(f"找到 {len(profile_files)} 个档案文件")
        
        total_profile_profit = 0
        total_profile_trades = 0
        
        for profile_file in profile_files:
            file_path = os.path.join(profiles_dir, profile_file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    profile_data = json.load(f)
                
                code = profile_data.get('code', profile_file.replace('.json', ''))
                total_profit = profile_data.get('total_profit', 0)
                trade_count = profile_data.get('trade_count', 0)
                current_position = profile_data.get('current_position', 0)
                
                print(f"\n{code}:")
                print(f"  总盈亏: {total_profit:.2f}")
                print(f"  交易次数: {trade_count}")
                print(f"  当前持仓: {current_position}")
                
                total_profile_profit += total_profit
                total_profile_trades += trade_count
                
            except Exception as e:
                print(f"加载档案文件 {profile_file} 失败: {str(e)}")
        
        print(f"\n档案汇总:")
        print(f"  总盈亏: {total_profile_profit:.2f}")
        print(f"  总交易次数: {total_profile_trades}")
        
        return total_profile_profit, total_profile_trades
    else:
        print(f"目录不存在: {profiles_dir}")
        return 0, 0

def main():
    """主函数"""
    print("开始检查数据文件...")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查各个数据文件
    position_records = check_position_records()
    trade_records = check_trade_history()
    profile_profit, profile_trades = check_stock_profiles()
    
    # 汇总分析
    print("\n=== 数据汇总分析 ===")
    print(f"持仓记录数量: {len(position_records)}")
    print(f"交易历史数量: {len(trade_records)}")
    print(f"档案总盈亏: {profile_profit:.2f}")
    print(f"档案总交易: {profile_trades}")
    
    # 分析虚拟vs实际交易
    virtual_positions = 0
    real_positions = 0
    
    for code, info in position_records.items():
        if 'buy_queue' in info and info['buy_queue']:
            is_virtual = info['buy_queue'][0].get('virtual', False)
        else:
            is_virtual = info.get('virtual', False)
        
        if is_virtual:
            virtual_positions += 1
        else:
            real_positions += 1
    
    print(f"虚拟持仓: {virtual_positions}")
    print(f"实际持仓: {real_positions}")
    
    # 检查交易历史中的虚拟vs实际
    virtual_trades = sum(1 for record in trade_records if record.get('virtual', False))
    real_trades = len(trade_records) - virtual_trades
    
    print(f"虚拟交易: {virtual_trades}")
    print(f"实际交易: {real_trades}")

if __name__ == "__main__":
    main()
