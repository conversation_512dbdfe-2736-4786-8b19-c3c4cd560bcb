#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试持仓查询功能
验证在不启用ETF交易的情况下是否能正常查询持仓
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_position_query_logic():
    """测试持仓查询逻辑"""
    
    print("=== ETF交易系统持仓查询功能测试 ===\n")
    
    print("📋 测试场景说明:")
    print("1. 启用ETF交易 → 可以查询持仓 + 可以执行交易")
    print("2. 禁用ETF交易 → 可以查询持仓 + 不能执行交易（只读模式）")
    print()
    
    # 模拟不同的交易状态
    test_scenarios = [
        {
            "name": "启用ETF交易模式",
            "trading_enabled": True,
            "expected_behavior": {
                "can_query_positions": True,
                "can_execute_trades": True,
                "can_sync_positions": True,
                "display_mode": "实盘模式"
            }
        },
        {
            "name": "禁用ETF交易模式",
            "trading_enabled": False,
            "expected_behavior": {
                "can_query_positions": True,  # 修改后应该可以查询
                "can_execute_trades": False,
                "can_sync_positions": True,   # 修改后应该可以同步（只读）
                "display_mode": "只读模式"
            }
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"{i}. {scenario['name']}")
        print(f"   trading_enabled = {scenario['trading_enabled']}")
        
        behavior = scenario['expected_behavior']
        print(f"   预期行为:")
        print(f"     - 查询持仓: {'✅ 允许' if behavior['can_query_positions'] else '❌ 禁止'}")
        print(f"     - 执行交易: {'✅ 允许' if behavior['can_execute_trades'] else '❌ 禁止'}")
        print(f"     - 同步持仓: {'✅ 允许' if behavior['can_sync_positions'] else '❌ 禁止'}")
        print(f"     - 显示模式: {behavior['display_mode']}")
        print()
    
    print("🔧 修改前的问题:")
    print("   - 禁用ETF交易时，无法查询服务器持仓")
    print("   - 用户只想查看持仓但不想交易时，无法获取持仓信息")
    print("   - 查看持仓和执行交易被错误地绑定在一起")
    print()
    
    print("✅ 修改后的改进:")
    print("   - 持仓查询功能独立于交易执行功能")
    print("   - 禁用交易时仍可查询持仓（只读模式）")
    print("   - 只有实际的买卖操作才需要启用交易")
    print("   - 提供更好的用户体验")

def show_code_changes():
    """显示代码修改要点"""
    
    print("\n=== 代码修改要点 ===\n")
    
    changes = [
        {
            "function": "sync_server_positions()",
            "before": "if not self.trading_enabled: return False",
            "after": "允许查询，但标记为只读模式",
            "impact": "禁用交易时也能同步持仓"
        },
        {
            "function": "update_position_list()",
            "before": "if self.trading_enabled: 查询服务器持仓",
            "after": "无论是否启用都查询服务器持仓",
            "impact": "持仓显示不再依赖交易开关"
        },
        {
            "function": "持仓删除逻辑",
            "before": "自动删除服务器不存在的持仓",
            "after": "只有启用交易时才自动删除",
            "impact": "避免只读模式下误删持仓记录"
        }
    ]
    
    for i, change in enumerate(changes, 1):
        print(f"{i}. {change['function']}")
        print(f"   修改前: {change['before']}")
        print(f"   修改后: {change['after']}")
        print(f"   影响: {change['impact']}")
        print()

def check_current_position_file():
    """检查当前持仓文件状态"""
    
    print("\n=== 当前持仓文件状态 ===\n")
    
    try:
        with open('ETF交易系统持仓记录.json', 'r', encoding='utf-8') as f:
            position_records = json.load(f)
        
        print(f"📊 持仓记录统计:")
        print(f"   - 总持仓数量: {len(position_records)}")
        
        real_positions = 0
        virtual_positions = 0
        
        for code, position_info in position_records.items():
            buy_queue = position_info.get('buy_queue', [])
            if buy_queue:
                first_buy = buy_queue[0]
                if first_buy.get('virtual', False):
                    virtual_positions += 1
                else:
                    real_positions += 1
        
        print(f"   - 实际持仓: {real_positions}")
        print(f"   - 虚拟持仓: {virtual_positions}")
        print()
        
        print("📋 持仓详情:")
        for code, position_info in position_records.items():
            buy_queue = position_info.get('buy_queue', [])
            if buy_queue:
                first_buy = buy_queue[0]
                position_type = "虚拟" if first_buy.get('virtual', False) else "实际"
                quantity = position_info.get('total_quantity', 0)
                print(f"   {code}: {position_type}持仓 {quantity}股")
        
    except FileNotFoundError:
        print("❌ 未找到持仓记录文件")
    except Exception as e:
        print(f"❌ 读取持仓记录失败: {str(e)}")

if __name__ == "__main__":
    test_position_query_logic()
    show_code_changes()
    check_current_position_file()
