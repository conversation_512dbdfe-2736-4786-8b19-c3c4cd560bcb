#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示智能时间处理功能
展示程序如何区分和处理不同来源的买入时间
"""

import json
from datetime import datetime

def demonstrate_time_handling():
    """演示时间处理逻辑"""
    
    print("=== ETF交易系统 - 智能买入时间处理演示 ===\n")
    
    print("📋 处理规则说明:")
    print("1. 程序实际执行的买入操作 → 保留真实买入时间")
    print("2. 服务器同步的持仓记录 → 使用上两个交易日15:00的默认时间")
    print("3. 自动识别记录来源，智能选择处理方式\n")
    
    # 模拟不同场景的买入记录
    scenarios = [
        {
            "title": "🤖 程序实际买入操作",
            "description": "用户在交易时间内，程序检测到买入信号并实际执行买入",
            "before": {
                "buy_price": 1.2345678,
                "buy_time": "10:32:15",  # 只有时分秒
                "order_id": "ORDER_20250804_001",
                "virtual": False
            },
            "after": {
                "buy_price": 1.235,  # 格式化为3位小数
                "buy_time": "2025-08-04 10:32:15",  # 补充日期
                "order_id": "ORDER_20250804_001",
                "virtual": False
            },
            "reason": "程序产生的真实买入时间，保留并补充日期"
        },
        {
            "title": "🔄 服务器持仓同步",
            "description": "程序启动时从服务器同步发现的持仓，无法获取真实买入时间",
            "before": {
                "buy_price": 0.8588405172413792,
                "buy_time": "15:23:18",  # 同步时的时间戳
                "order_id": "SERVER_SYNC_1754292198",
                "use_server_data": True
            },
            "after": {
                "buy_price": 0.859,  # 格式化为3位小数
                "buy_time": "2025-07-31 15:00:00",  # 使用默认时间
                "order_id": "SERVER_SYNC_1754292198",
                "use_server_data": True
            },
            "reason": "服务器同步记录，使用上两个交易日15:00作为买入时间"
        },
        {
            "title": "✅ 程序完整时间记录",
            "description": "程序之前执行的买入，已经有完整的日期时间记录",
            "before": {
                "buy_price": 1.4567890123,
                "buy_time": "2025-08-03 14:25:30",  # 已有完整时间
                "order_id": "ORDER_20250803_005",
                "virtual": False
            },
            "after": {
                "buy_price": 1.457,  # 格式化为3位小数
                "buy_time": "2025-08-03 14:25:30",  # 保留原时间
                "order_id": "ORDER_20250803_005",
                "virtual": False
            },
            "reason": "程序产生的完整时间记录，直接保留"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"{i}. {scenario['title']}")
        print(f"   场景: {scenario['description']}")
        print(f"   修复前: {json.dumps(scenario['before'], ensure_ascii=False, indent=6)}")
        print(f"   修复后: {json.dumps(scenario['after'], ensure_ascii=False, indent=6)}")
        print(f"   处理逻辑: {scenario['reason']}")
        print()
    
    print("🎯 智能识别标准:")
    print("   - 订单ID以'SERVER_SYNC_'或'SYNC_'开头 → 服务器同步")
    print("   - 记录中有'use_server_data': true → 服务器同步")
    print("   - 'data_source'为'server_cost'或'current_price' → 服务器同步")
    print("   - 其他情况 → 程序产生的记录")
    print()
    
    print("📊 数值格式化标准:")
    print("   - 买入价格: 保留3位小数")
    print("   - 手续费: 保留5位小数")
    print("   - 金额: 保留2位小数")
    print("   - 时间: 统一为'YYYY-MM-DD HH:MM:SS'格式")

def show_current_position_status():
    """显示当前持仓记录的状态"""
    try:
        with open('ETF交易系统持仓记录.json', 'r', encoding='utf-8') as f:
            position_records = json.load(f)
        
        print("\n=== 当前持仓记录状态 ===\n")
        
        for code, position_info in position_records.items():
            buy_queue = position_info.get('buy_queue', [])
            if buy_queue:
                first_buy = buy_queue[0]
                
                # 判断记录类型
                order_id = first_buy.get('order_id', '')
                is_server_sync = (
                    order_id.startswith(('SERVER_SYNC_', 'SYNC_')) or
                    first_buy.get('use_server_data', False)
                )
                
                record_type = "🔄 服务器同步" if is_server_sync else "🤖 程序记录"
                
                print(f"{code}: {record_type}")
                print(f"   买入价格: {first_buy.get('buy_price', 'N/A')}")
                print(f"   买入时间: {first_buy.get('buy_time', 'N/A')}")
                print(f"   订单ID: {order_id}")
                print()
        
    except FileNotFoundError:
        print("❌ 未找到持仓记录文件")
    except Exception as e:
        print(f"❌ 读取持仓记录失败: {str(e)}")

if __name__ == "__main__":
    demonstrate_time_handling()
    show_current_position_status()
