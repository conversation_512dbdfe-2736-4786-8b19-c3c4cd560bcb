# 获利股票黑名单功能说明

## 功能概述

该功能实现了对获利股票的自动标记和买入限制，防止重复买入已经获利的股票。

## 主要特性

### 1. 自动标记获利股票
- 当股票卖出时如果获利（profit > 0），系统会自动将该股票添加到黑名单
- 黑名单中的股票将不再被买入（包括首次买入和加仓买入）

### 2. 持久化存储
- 黑名单数据保存在 `获利股票黑名单.json` 文件中
- 文件包含以下信息：
  - `blacklist`: 黑名单股票代码列表
  - `last_updated`: 最后更新时间
  - `total_count`: 黑名单股票总数

### 3. 自动清理机制
- 每天 9:28 自动检查黑名单
- 如果黑名单中的股票不在当前股票池中，则自动从黑名单中移除
- 确保黑名单只包含当前股票池中的股票

### 4. 买入限制
- 在买入信号触发时，系统会检查股票是否在黑名单中
- 如果在黑名单中，会显示 "🚫 {股票代码} 在获利黑名单中，跳过买入" 的提示
- 适用于所有买入类型：首次买入和加仓买入

## 文件结构

```json
{
  "blacklist": [
    "113050",
    "113052"
  ],
  "last_updated": "2025-07-22 12:52:03",
  "total_count": 2
}
```

## 工作流程

### 卖出时的处理
1. 股票卖出完成后，系统计算盈亏
2. 如果 `profit > 0`，调用 `add_to_profit_blacklist(code)` 方法
3. 将股票代码添加到黑名单集合中
4. 保存黑名单到文件
5. 显示提示信息：`🚫 {股票代码} 已添加到获利股票黑名单，今后不再买入`

### 买入时的检查
1. 买入信号触发时，检查 `code in self.profit_blacklist`
2. 如果在黑名单中，跳过买入并显示提示
3. 如果不在黑名单中，正常执行买入逻辑

### 定时清理（9:28）
1. 系统在每天 9:28 自动触发清理
2. 比较黑名单与当前股票池
3. 移除不在股票池中的股票
4. 更新黑名单文件
5. 显示清理结果

## 相关方法

### `load_profit_blacklist()`
- 从文件加载黑名单数据
- 返回股票代码的集合（set）

### `save_profit_blacklist()`
- 将黑名单数据保存到文件
- 包含时间戳和统计信息

### `add_to_profit_blacklist(code)`
- 将股票添加到黑名单
- 自动保存文件并显示提示

### `remove_from_profit_blacklist(code)`
- 从黑名单中移除股票
- 自动保存文件并显示提示

### `clean_profit_blacklist()`
- 清理不在股票池中的黑名单股票
- 在每天 9:28 自动执行

## 日志示例

```
[12:52:03] 已加载获利股票黑名单: 2 只股票
[09:28:00] ⏰ 到达黑名单检查时间 09:28，开始清理获利黑名单...
[09:28:01] 🧹 已从获利黑名单中清理 1 只不在股票池中的股票: 113051
[10:30:05] 🚫 113050 在获利黑名单中，跳过买入
[14:25:10] 🚫 113050 已添加到获利股票黑名单，今后不再买入
```

## 注意事项

1. 黑名单功能只对获利的股票生效（profit > 0）
2. 亏损的股票不会被加入黑名单，可以继续买入
3. 黑名单会在每天 9:28 自动清理，确保数据的有效性
4. 如果需要手动管理黑名单，可以直接编辑 `获利股票黑名单.json` 文件
5. 系统启动时会自动加载黑名单数据

## 测试

可以运行 `test_profit_blacklist.py` 脚本来测试黑名单功能的各个方面。
