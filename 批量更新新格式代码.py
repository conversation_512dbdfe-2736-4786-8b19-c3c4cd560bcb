#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量更新旧格式兼容代码为新格式
将所有直接访问 position_info['buy_price'] 和 position_info['quantity'] 的代码
更新为使用新格式的 buy_queue 结构
"""

import re
import os

def update_old_format_code(file_path):
    """更新单个文件中的旧格式代码"""
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    print(f"处理文件: {file_path}")
    
    try:
        # 读取原文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 创建备份
        backup_file = f"{file_path}.backup_format_update"
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  已备份到: {backup_file}")
        
        # 定义替换模式
        replacements = [
            # 模式1: 直接访问 buy_price 和 quantity
            (
                r"(\s+)buy_price\s*=\s*position_info\[['\"']buy_price['\"']\]\s*\n(\s+)quantity\s*=\s*position_info\.get\(['\"']quantity['\"'],\s*(\d+)\)",
                r"\1# 统一使用新格式获取买入价格和数量\n\1if 'buy_queue' in position_info and position_info['buy_queue']:\n\1    buy_price = position_info['buy_queue'][0]['buy_price']\n\1    quantity = position_info.get('total_quantity', \3)\n\1else:\n\1    self.add_record(f\"错误: {code} 持仓记录格式异常，缺少buy_queue\")\n\1    continue"
            ),
            
            # 模式2: 只访问 buy_price
            (
                r"(\s+)buy_price\s*=\s*position_info\[['\"']buy_price['\"']\]",
                r"\1# 统一使用新格式获取买入价格\n\1if 'buy_queue' in position_info and position_info['buy_queue']:\n\1    buy_price = position_info['buy_queue'][0]['buy_price']\n\1else:\n\1    self.add_record(f\"错误: 持仓记录格式异常，缺少buy_queue\")\n\1    continue"
            ),
            
            # 模式3: 只访问 quantity
            (
                r"(\s+)quantity\s*=\s*position_info\.get\(['\"']quantity['\"'],\s*(\d+)\)",
                r"\1# 统一使用新格式获取数量\n\1if 'buy_queue' in position_info and position_info['buy_queue']:\n\1    quantity = position_info.get('total_quantity', \2)\n\1else:\n\1    self.add_record(f\"错误: 持仓记录格式异常，缺少buy_queue\")\n\1    continue"
            ),
            
            # 模式4: 访问 total_quantity
            (
                r"(\s+)total_quantity\s*=\s*position_info\.get\(['\"']quantity['\"'],\s*(\d+)\)",
                r"\1# 统一使用新格式获取总数量\n\1if 'buy_queue' in position_info and position_info['buy_queue']:\n\1    total_quantity = position_info.get('total_quantity', \2)\n\1else:\n\1    self.add_record(f\"错误: 持仓记录格式异常，缺少buy_queue\")\n\1    continue"
            ),
            
            # 模式5: 在函数参数中直接访问
            (
                r"float\(position_info\[['\"']buy_price['\"']\]\)",
                r"position_info['buy_queue'][0]['buy_price'] if 'buy_queue' in position_info and position_info['buy_queue'] else 0"
            ),
        ]
        
        # 应用替换
        modified = False
        for pattern, replacement in replacements:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
                modified = True
                print(f"  应用替换模式: {pattern[:50]}...")
        
        # 特殊处理：更新兼容性检查代码
        special_patterns = [
            # 移除旧的兼容性检查
            (
                r"# 兼容新旧格式获取.*?\n(\s+)if 'buy_queue' in.*?else:\s*\n\s+.*?position_info\.get\('buy_price'\).*?\n",
                r"# 统一使用新格式\n\1if 'buy_queue' in position_info and position_info['buy_queue']:\n\1    buy_price = position_info['buy_queue'][0]['buy_price']\n\1else:\n\1    continue\n"
            ),
        ]
        
        for pattern, replacement in special_patterns:
            if re.search(pattern, content, re.DOTALL):
                content = re.sub(pattern, replacement, content, flags=re.DOTALL | re.MULTILINE)
                modified = True
                print(f"  应用特殊替换模式")
        
        if modified:
            # 保存修改后的文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  已更新文件: {file_path}")
            return True
        else:
            print(f"  未发现需要更新的模式: {file_path}")
            # 删除不必要的备份文件
            os.remove(backup_file)
            return True
            
    except Exception as e:
        print(f"  处理文件失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔄 开始批量更新新格式代码...")
    
    # 需要更新的文件列表
    files_to_update = [
        "可转债做T交易-cci.py"
    ]
    
    success_count = 0
    for file_path in files_to_update:
        if update_old_format_code(file_path):
            success_count += 1
    
    print(f"\n🏁 更新完成！")
    print(f"✅ 成功处理: {success_count}/{len(files_to_update)} 个文件")
    
    if success_count == len(files_to_update):
        print("🎉 所有文件都已成功更新为新格式！")
        return True
    else:
        print("⚠️  部分文件更新失败，请检查错误信息")
        return False

if __name__ == "__main__":
    main()
