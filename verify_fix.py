#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复效果的脚本
"""

import json
import os
from datetime import datetime

def main():
    """主函数"""
    print("验证修复效果...")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查数据文件
    position_file = "自选股30m系统持仓记录.json"
    trade_file = "自选股30m系统永久交易历史.jsonl"
    profiles_dir = "stock_profiles"
    
    print(f"\n检查数据文件:")
    print(f"✓ 持仓记录文件: {'存在' if os.path.exists(position_file) else '不存在'}")
    print(f"✓ 交易历史文件: {'存在' if os.path.exists(trade_file) else '不存在'}")
    print(f"✓ 股票档案目录: {'存在' if os.path.exists(profiles_dir) else '不存在'}")
    
    if not all([os.path.exists(position_file), os.path.exists(trade_file), os.path.exists(profiles_dir)]):
        print("\n❌ 数据文件不完整，请检查")
        return False
    
    # 加载并分析数据
    try:
        # 持仓记录
        with open(position_file, 'r', encoding='utf-8') as f:
            position_records = json.load(f)
        
        # 交易历史
        trade_records = []
        with open(trade_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    trade_record = json.loads(line)
                    trade_records.append(trade_record)
        
        # 股票档案
        stock_profiles = {}
        for profile_file in os.listdir(profiles_dir):
            if profile_file.endswith('.json'):
                file_path = os.path.join(profiles_dir, profile_file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    profile_data = json.load(f)
                code = profile_data.get('code', profile_file.replace('.json', ''))
                stock_profiles[code] = profile_data
        
        print(f"\n数据统计:")
        print(f"✓ 持仓记录: {len(position_records)}条")
        print(f"✓ 交易历史: {len(trade_records)}条")
        print(f"✓ 股票档案: {len(stock_profiles)}个")
        
        # 分析持仓类型
        real_positions = 0
        virtual_positions = 0
        
        for code, info in position_records.items():
            if 'buy_queue' in info and info['buy_queue']:
                is_virtual = info['buy_queue'][0].get('virtual', False)
            else:
                is_virtual = info.get('virtual', False)
            
            if is_virtual:
                virtual_positions += 1
            else:
                real_positions += 1
        
        print(f"\n持仓分析:")
        print(f"✓ 实际持仓: {real_positions}个")
        print(f"✓ 虚拟持仓: {virtual_positions}个")
        
        # 分析交易汇总
        total_profit = sum(record['profit'] for record in trade_records)
        profit_trades = sum(1 for record in trade_records if record['profit'] > 0)
        
        # 档案汇总
        profile_trades = 0
        profile_profit = 0.0
        
        for code, profile in stock_profiles.items():
            trade_count = profile.get('trade_count', 0)
            total_profit_profile = profile.get('total_profit', 0)
            profile_trades += trade_count
            profile_profit += total_profit_profile
        
        print(f"\n交易汇总:")
        print(f"✓ 历史交易记录: {len(trade_records)}条, 总盈亏: {total_profit:.2f}元")
        print(f"✓ 档案交易记录: {profile_trades}次, 总盈亏: {profile_profit:.2f}元")
        
        # 预期显示结果
        print(f"\n预期程序显示:")
        if real_positions > 0:
            print(f"✓ 当前持仓: {real_positions}个（实际交易模式）")
        else:
            print(f"✓ 当前持仓: {virtual_positions}个（虚拟交易模式）")
        
        final_trades = profile_trades if profile_trades > 0 else len(trade_records)
        final_profit = profile_profit if profile_trades > 0 else total_profit
        
        print(f"✓ 总交易次数: {final_trades}")
        print(f"✓ 盈利交易: {profit_trades}")
        print(f"✓ 总盈亏: {final_profit:.2f}元")
        
        print(f"\n✅ 数据验证完成，修复效果良好！")
        print(f"\n建议:")
        print(f"1. 重新启动自选股交易程序")
        print(f"2. 检查界面显示是否与上述预期一致")
        print(f"3. 如果显示为0，请检查程序的连接状态")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 数据分析失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("按回车键退出...")
