# 涨停时不进行止盈卖出功能修改说明

## 修改概述

根据您的要求，在卖出判断时从K线数据获取涨幅，若为涨停（涨幅在20%上下0.5%），则不进行止盈卖出，当不涨停后再进行止盈。

## 主要修改内容

### 1. 新增涨停检查方法

在 `可转债做T交易-cci.py` 文件中新增了 `check_limit_up_status` 方法：

```python
def check_limit_up_status(self, code, current_price):
    """
    检查股票是否涨停
    
    Args:
        code: 股票代码
        current_price: 当前价格
        
    Returns:
        tuple: (是否涨停, 涨停状态描述)
    """
```

该方法的功能：
- **优先使用日线数据**：通过 `xtdata.get_market_data(period='1d')` 获取最近2个交易日的数据
- **更准确的涨幅计算**：使用今日收盘价和昨日收盘价计算涨幅，避免5分钟K线的局限性
- **备用tick数据**：如果日线数据获取失败，使用 `xtdata.get_full_tick()` 作为备选
- 计算涨幅：`(今日价格 / 昨日收盘价 - 1) * 100`
- 判断涨幅是否在 19.5% 到 20.5% 范围内（即20%±0.5%）
- 返回是否涨停及状态描述

### 2. 修改止盈卖出逻辑

在止盈卖出判断中增加了涨停检查：

#### 第一次止盈（400元）
```python
if current_profit >= 400 and not profit_state['first_profit_sell_done']:
    if is_limit_up:
        self.add_record(f"🚫 {code} 触发第一次止盈(400元)但当前涨停({limit_status})，暂不卖出")
        continue
    # 原有的止盈卖出逻辑...
```

#### 第二次止盈（700元）
```python
elif current_profit >= 700 and not profit_state['second_profit_sell_done']:
    if is_limit_up:
        self.add_record(f"🚫 {code} 触发第二次止盈(700元)但当前涨停({limit_status})，暂不卖出")
        continue
    # 原有的止盈卖出逻辑...
```

## 功能特点

1. **精确涨停判断**：使用实时行情数据计算涨幅，判断是否在涨停范围内
2. **智能延迟卖出**：涨停时暂停止盈卖出，等待不涨停后再执行
3. **详细日志记录**：记录涨停状态和暂停卖出的原因
4. **保持原有逻辑**：不影响其他卖出条件（止损、技术指标等）

## 使用说明

1. 程序会在每次5分钟检查时自动判断持仓股票是否涨停
2. 如果股票涨停且达到止盈条件，会显示类似以下信息：
   ```
   🚫 113050.SZ 触发第一次止盈(400元)但当前涨停(涨幅19.8%)，暂不卖出: 当前价格163.500，盈利420.00元
   ```
3. 当股票不再涨停时，会正常执行止盈卖出

## 测试文件

创建了 `test_limit_up.py` 测试文件，可以独立测试涨停检查功能：

```bash
python test_limit_up.py
```

## 注意事项

1. 需要确保QMT连接正常，能够获取实时行情数据
2. 涨停判断基于实时行情，可能存在轻微延迟
3. 可转债的涨停幅度通常为20%，代码中设置为19.5%-20.5%的范围
4. 该功能只影响止盈卖出，不影响止损和技术指标卖出

## 技术实现

### 数据获取策略
1. **优先日线数据**：使用 `xtdata.get_market_data(period='1d', count=2)` 获取最近2个交易日数据
2. **备用实时数据**：如果日线数据获取失败，使用 `xtdata.get_full_tick()` 获取实时tick数据
3. **准确性优先**：日线数据基于完整交易日，避免5分钟K线周期的局限性

### 涨幅计算方法
- **日线数据**（推荐）：`(今日收盘价 / 昨日收盘价 - 1) * 100`
- **实时数据**（备选）：`(lastPrice / lastClose - 1) * 100`

### 集成方式
- 在止盈逻辑中增加涨停检查条件
- 保持原有的程序结构和其他功能不变
- 不影响5分钟K线的主要交易逻辑
