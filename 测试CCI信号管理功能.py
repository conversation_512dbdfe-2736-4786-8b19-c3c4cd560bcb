#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试CCI信号管理功能
"""

import json
import os
from datetime import datetime, timedelta

def create_test_position_records():
    """创建测试用的持仓记录，包含不同天数的CCI信号记录"""
    
    # 获取当前时间
    now = datetime.now()
    
    # 创建测试数据
    test_records = {
        # 今天的CCI信号记录
        "113001.SH": {
            "buy_price": 125.50,
            "order_price": 125.50,
            "buy_time": now.strftime('%Y-%m-%d %H:%M:%S'),
            "signal_date": now.strftime('%Y-%m-%d'),
            "quantity": 0,
            "fee": 0.0,
            "actual_amount": 0.0,
            "order_id": "CCI_SIGNAL_TODAY",
            "virtual": True,
            "buy_period": 2,
            "buy_hour": now.hour,
            "periods_since_buy": 0,
            "profit_check_done": False,
            "add_count": 0,
            "is_cci_signal": True,
            "is_signal_only": True
        },
        
        # 昨天的CCI信号记录
        "113002.SH": {
            "buy_price": 156.80,
            "order_price": 156.80,
            "buy_time": (now - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S'),
            "signal_date": (now - timedelta(days=1)).strftime('%Y-%m-%d'),
            "quantity": 0,
            "fee": 0.0,
            "actual_amount": 0.0,
            "order_id": "CCI_SIGNAL_YESTERDAY",
            "virtual": True,
            "buy_period": 3,
            "buy_hour": 10,
            "periods_since_buy": 0,
            "profit_check_done": False,
            "add_count": 0,
            "is_cci_signal": True,
            "is_signal_only": True
        },
        
        # 3天前的CCI信号记录（应该被清理）
        "113003.SH": {
            "buy_price": 189.20,
            "order_price": 189.20,
            "buy_time": (now - timedelta(days=3)).strftime('%Y-%m-%d %H:%M:%S'),
            "signal_date": (now - timedelta(days=3)).strftime('%Y-%m-%d'),
            "quantity": 0,
            "fee": 0.0,
            "actual_amount": 0.0,
            "order_id": "CCI_SIGNAL_3DAYS_AGO",
            "virtual": True,
            "buy_period": 1,
            "buy_hour": 9,
            "periods_since_buy": 0,
            "profit_check_done": False,
            "add_count": 0,
            "is_cci_signal": True,
            "is_signal_only": True
        },
        
        # 正常的实际持仓记录
        "113004.SH": {
            "buy_price": 145.30,
            "order_price": 145.30,
            "buy_time": now.strftime('%Y-%m-%d %H:%M:%S'),
            "quantity": 80,
            "fee": 11.62,
            "actual_amount": 11624.0,
            "order_id": "REAL_POSITION_001",
            "virtual": True,
            "buy_period": 2,
            "buy_hour": now.hour,
            "periods_since_buy": 0,
            "profit_check_done": False,
            "add_count": 0,
            "is_cci_signal": False,
            "is_signal_only": False
        }
    }
    
    return test_records

def test_signal_cleanup_logic():
    """测试信号清理逻辑"""
    print("=== 测试CCI信号清理逻辑 ===")
    
    # 创建测试数据
    test_records = create_test_position_records()
    
    print(f"创建了 {len(test_records)} 条测试记录:")
    for code, record in test_records.items():
        if record.get('is_signal_only', False):
            print(f"  CCI信号: {code} - {record['buy_time']}")
        else:
            print(f"  实际持仓: {code} - {record['quantity']}股")
    
    # 模拟清理逻辑
    current_time = datetime.now()
    expired_codes = []
    
    print(f"\n当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("检查过期信号:")
    
    for code, position_info in test_records.items():
        # 只处理CCI信号记录
        if not position_info.get('is_signal_only', False):
            continue
        
        try:
            buy_time_str = position_info.get('buy_time', '')
            signal_time = datetime.strptime(buy_time_str, '%Y-%m-%d %H:%M:%S')
            
            # 计算时间差
            time_diff = current_time - signal_time
            days_passed = time_diff.days
            
            print(f"  {code}: 信号时间 {buy_time_str}, 已过去 {days_passed} 天")
            
            # 如果超过2天，标记为过期
            if days_passed > 2:
                expired_codes.append(code)
                print(f"    -> 过期，将被清理")
            elif days_passed >= 1:
                print(f"    -> 警告，明天将被清理")
            else:
                print(f"    -> 正常")
                
        except Exception as e:
            print(f"  {code}: 处理时出错 - {str(e)}")
            expired_codes.append(code)
    
    print(f"\n清理结果:")
    if expired_codes:
        print(f"  需要清理的过期信号: {', '.join(expired_codes)}")
        for code in expired_codes:
            del test_records[code]
        print(f"  清理后剩余记录: {len(test_records)} 条")
    else:
        print("  无需清理的过期信号")
    
    return test_records

def test_buy_condition_check():
    """测试买入条件检查逻辑"""
    print("\n=== 测试买入条件检查逻辑 ===")
    
    # 模拟股票池
    stock_pool = ["113001.SH", "113004.SH"]  # 只包含部分股票
    
    # 模拟持仓记录
    position_records = {
        "113001.SH": {"is_signal_only": True},   # 在股票池中的CCI信号
        "113002.SH": {"is_signal_only": True},   # 不在股票池中的CCI信号
        "113004.SH": {"is_signal_only": False},  # 在股票池中的实际持仓
        "113005.SH": {"is_signal_only": False}   # 不在股票池中的实际持仓
    }
    
    # 模拟技术指标卖出等待重新买回列表
    tech_sell_waiting_rebuy = {"113006.SH": {}}
    
    print("股票池:", stock_pool)
    print("持仓记录:")
    for code, record in position_records.items():
        record_type = "CCI信号" if record.get('is_signal_only', False) else "实际持仓"
        print(f"  {code}: {record_type}")
    print("技术指标卖出等待重新买回:", list(tech_sell_waiting_rebuy.keys()))
    
    print("\n买入条件检查结果:")
    
    # 测试所有股票的买入条件检查逻辑
    test_codes = ["113001.SH", "113002.SH", "113004.SH", "113005.SH", "113006.SH"]
    
    for code in test_codes:
        is_in_stock_pool = code in stock_pool
        
        # 检查买入信号的条件
        should_check_buy = (is_in_stock_pool or 
                          code in tech_sell_waiting_rebuy or
                          (code in position_records and 
                           position_records[code].get('is_signal_only', False)))
        
        print(f"  {code}:")
        print(f"    在股票池中: {is_in_stock_pool}")
        print(f"    在技术指标卖出等待列表中: {code in tech_sell_waiting_rebuy}")
        print(f"    有CCI信号记录: {code in position_records and position_records[code].get('is_signal_only', False)}")
        print(f"    应该检查买入条件: {should_check_buy}")
        
        if should_check_buy:
            print(f"    -> ✅ 将检查买入信号")
        else:
            print(f"    -> ❌ 跳过买入判断，仅进行卖出判断")
        print()

def save_test_data():
    """保存测试数据到文件"""
    print("\n=== 保存测试数据 ===")
    
    test_records = create_test_position_records()
    
    # 保存到持仓记录文件
    filename = f"当日持仓记录_{datetime.now().strftime('%Y%m%d')}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(test_records, f, ensure_ascii=False, indent=2)
        
        print(f"测试数据已保存到: {filename}")
        print("可以启动主程序测试CCI信号管理功能")
        
        # 显示文件内容摘要
        signal_count = sum(1 for record in test_records.values() 
                          if record.get('is_signal_only', False))
        position_count = len(test_records) - signal_count
        
        print(f"文件包含: {signal_count} 个CCI信号记录, {position_count} 个实际持仓记录")
        
    except Exception as e:
        print(f"保存测试数据失败: {str(e)}")

def main():
    """主测试函数"""
    print("CCI信号管理功能测试")
    print("=" * 50)
    
    # 测试信号清理逻辑
    remaining_records = test_signal_cleanup_logic()
    
    # 测试买入条件检查逻辑
    test_buy_condition_check()
    
    # 询问是否保存测试数据
    try:
        save_choice = input("\n是否要保存测试数据到持仓记录文件？(y/n): ").lower().strip()
        if save_choice in ['y', 'yes', '是']:
            save_test_data()
        else:
            print("跳过保存测试数据")
    except KeyboardInterrupt:
        print("\n测试中断")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
