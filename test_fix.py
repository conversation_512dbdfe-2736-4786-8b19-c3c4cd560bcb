#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果的脚本
"""

import json
import os
import sys
import tkinter as tk
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_data_loading():
    """测试数据加载"""
    print("=== 测试数据加载 ===")
    
    # 测试持仓记录加载
    position_file = "自选股30m系统持仓记录.json"
    if os.path.exists(position_file):
        try:
            with open(position_file, 'r', encoding='utf-8') as f:
                position_records = json.load(f)
            print(f"✓ 持仓记录加载成功: {len(position_records)}条")
        except Exception as e:
            print(f"✗ 持仓记录加载失败: {str(e)}")
            return False
    else:
        print(f"✗ 持仓记录文件不存在: {position_file}")
        return False
    
    # 测试交易历史加载
    trade_file = "自选股30m系统永久交易历史.jsonl"
    if os.path.exists(trade_file):
        try:
            trade_records = []
            with open(trade_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        trade_record = json.loads(line)
                        trade_records.append(trade_record)
            print(f"✓ 交易历史加载成功: {len(trade_records)}条")
        except Exception as e:
            print(f"✗ 交易历史加载失败: {str(e)}")
            return False
    else:
        print(f"✗ 交易历史文件不存在: {trade_file}")
        return False
    
    # 测试股票档案加载
    profiles_dir = "stock_profiles"
    if os.path.exists(profiles_dir):
        try:
            profile_files = [f for f in os.listdir(profiles_dir) if f.endswith('.json')]
            stock_profiles = {}
            for profile_file in profile_files:
                file_path = os.path.join(profiles_dir, profile_file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    profile_data = json.load(f)
                code = profile_data.get('code', profile_file.replace('.json', ''))
                stock_profiles[code] = profile_data
            print(f"✓ 股票档案加载成功: {len(stock_profiles)}个")
        except Exception as e:
            print(f"✗ 股票档案加载失败: {str(e)}")
            return False
    else:
        print(f"✗ 股票档案目录不存在: {profiles_dir}")
        return False
    
    return True

def test_display_logic():
    """测试显示逻辑"""
    print("\n=== 测试显示逻辑 ===")
    
    # 加载数据
    position_file = "自选股30m系统持仓记录.json"
    with open(position_file, 'r', encoding='utf-8') as f:
        position_records = json.load(f)
    
    trade_file = "自选股30m系统永久交易历史.jsonl"
    trade_records = []
    with open(trade_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                trade_record = json.loads(line)
                trade_records.append(trade_record)
    
    profiles_dir = "stock_profiles"
    stock_profiles = {}
    for profile_file in os.listdir(profiles_dir):
        if profile_file.endswith('.json'):
            file_path = os.path.join(profiles_dir, profile_file)
            with open(file_path, 'r', encoding='utf-8') as f:
                profile_data = json.load(f)
            code = profile_data.get('code', profile_file.replace('.json', ''))
            stock_profiles[code] = profile_data
    
    # 模拟实际交易模式（服务器查询失败）
    print("模拟实际交易模式（服务器查询失败）:")
    real_positions = 0
    virtual_positions = 0
    
    for code, info in position_records.items():
        if 'buy_queue' in info and info['buy_queue']:
            first_buy = info['buy_queue'][0]
            is_virtual = first_buy.get('virtual', False)
            is_real_trade = first_buy.get('real_trade', False)
            
            if is_virtual:
                virtual_positions += 1
            elif is_real_trade or not is_virtual:
                real_positions += 1
                print(f"  应显示实际持仓: {code}")
        else:
            is_virtual = info.get('virtual', False)
            if is_virtual:
                virtual_positions += 1
            else:
                real_positions += 1
                print(f"  应显示实际持仓: {code} (旧格式)")
    
    print(f"  实际持仓总数: {real_positions}")
    print(f"  虚拟持仓总数: {virtual_positions}")
    
    # 模拟交易汇总
    print("\n模拟交易汇总:")
    
    # 计算档案汇总
    profile_total_trades = 0
    profile_total_profit = 0.0
    
    for code, profile in stock_profiles.items():
        trade_count = profile.get('trade_count', 0)
        total_profit = profile.get('total_profit', 0)
        
        profile_total_trades += trade_count
        profile_total_profit += total_profit
        
        if trade_count > 0:
            print(f"  {code}: 交易{trade_count}次, 盈亏{total_profit:.2f}元")
    
    print(f"  档案汇总: 总交易{profile_total_trades}次, 总盈亏{profile_total_profit:.2f}元")
    
    # 计算当日交易汇总
    daily_total_trades = len(trade_records)
    daily_total_profit = sum(record['profit'] for record in trade_records)
    
    print(f"  当日汇总: 总交易{daily_total_trades}次, 总盈亏{daily_total_profit:.2f}元")
    
    # 最终显示
    final_trades = profile_total_trades if profile_total_trades > 0 else daily_total_trades
    final_profit = profile_total_profit if profile_total_trades > 0 else daily_total_profit
    
    print(f"  最终显示: 总交易{final_trades}次, 总盈亏{final_profit:.2f}元")
    
    return real_positions > 0 and final_trades > 0

def test_gui_creation():
    """测试GUI创建"""
    print("\n=== 测试GUI创建 ===")
    
    try:
        # 创建简单的测试窗口
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("400x300")
        
        # 创建标签显示测试结果
        label = tk.Label(root, text="GUI创建成功！\n数据加载正常\n修复效果良好", 
                        font=("Arial", 12), justify=tk.CENTER)
        label.pack(expand=True)
        
        # 创建关闭按钮
        close_button = tk.Button(root, text="关闭", command=root.destroy)
        close_button.pack(pady=10)
        
        print("✓ GUI创建成功")
        
        # 显示窗口3秒后自动关闭
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
    except Exception as e:
        print(f"✗ GUI创建失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("开始测试修复效果...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"当前目录: {os.getcwd()}")
    
    # 测试数据加载
    data_ok = test_data_loading()
    
    if not data_ok:
        print("\n✗ 数据加载测试失败，请检查数据文件")
        return False
    
    # 测试显示逻辑
    display_ok = test_display_logic()
    
    if not display_ok:
        print("\n✗ 显示逻辑测试失败")
        return False
    
    # 测试GUI创建
    gui_ok = test_gui_creation()
    
    if not gui_ok:
        print("\n✗ GUI创建测试失败")
        return False
    
    print("\n✓ 所有测试通过！修复效果良好")
    print("\n建议:")
    print("1. 重新启动自选股交易程序")
    print("2. 检查界面是否正确显示持仓和交易汇总")
    print("3. 如果仍有问题，请检查程序的连接状态")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("按回车键退出...")
