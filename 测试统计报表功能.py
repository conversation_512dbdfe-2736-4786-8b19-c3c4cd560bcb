#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试统计报表功能
"""

import os
import json
from datetime import datetime, timedelta

def create_test_trading_data():
    """创建测试用的交易数据文件"""
    
    # 创建最近5天的测试数据
    current_date = datetime.now()
    
    for i in range(5):
        test_date = current_date - timedelta(days=i)
        # 只在工作日创建数据
        if test_date.weekday() < 5:  # 周一到周五
            date_str = test_date.strftime('%Y%m%d')
            filename = f"交易历史_{date_str}.json"
            
            # 创建测试交易记录
            test_records = []
            
            # 为每天创建几条测试记录
            for j in range(3):
                record = {
                    "code": f"11{j+1:04d}.SZ",  # 可转债代码
                    "buy_price": 100.0 + j,
                    "sell_price": 102.0 + j + (i * 0.5),  # 不同日期有不同收益
                    "quantity": 10,
                    "profit": (2.0 + (i * 0.5)) * 10,  # 每手盈利
                    "buy_time": f"{test_date.strftime('%Y-%m-%d')} 09:30:00",
                    "sell_time": f"{test_date.strftime('%Y-%m-%d')} 14:30:00",
                    "trade_date": date_str
                }
                test_records.append(record)
            
            # 添加一些亏损记录
            loss_record = {
                "code": f"123456.SZ",
                "buy_price": 105.0,
                "sell_price": 103.0,
                "quantity": 10,
                "profit": -20.0,  # 亏损
                "buy_time": f"{test_date.strftime('%Y-%m-%d')} 10:00:00",
                "sell_time": f"{test_date.strftime('%Y-%m-%d')} 15:00:00",
                "trade_date": date_str
            }
            test_records.append(loss_record)
            
            # 保存到文件
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(test_records, f, ensure_ascii=False, indent=2)
            
            print(f"已创建测试数据文件: {filename} (包含 {len(test_records)} 条记录)")

def test_load_recent_data():
    """测试加载最近交易数据的功能"""
    
    # 模拟 load_recent_trading_data 方法
    def load_recent_trading_data(days=5):
        try:
            all_trade_records = []
            current_date = datetime.now()
            
            # 获取最近的交易日
            trading_days = []
            check_date = current_date
            
            while len(trading_days) < days:
                # 检查是否是工作日（周一到周五）
                if check_date.weekday() < 5:  # 0-4 代表周一到周五
                    trading_days.append(check_date.strftime('%Y%m%d'))
                check_date -= timedelta(days=1)
            
            print(f"正在加载最近{days}个交易日的数据: {', '.join(trading_days)}")
            
            # 加载每个交易日的数据
            for date_str in trading_days:
                trade_file = f"交易历史_{date_str}.json"
                if os.path.exists(trade_file):
                    try:
                        with open(trade_file, 'r', encoding='utf-8') as f:
                            content = f.read().strip()
                            if content:
                                daily_records = json.loads(content)
                                # 为每条记录添加日期标识
                                for record in daily_records:
                                    record['trade_date'] = date_str
                                all_trade_records.extend(daily_records)
                                print(f"已加载 {date_str} 的 {len(daily_records)} 条交易记录")
                    except Exception as e:
                        print(f"加载 {date_str} 交易数据失败: {str(e)}")
                else:
                    print(f"未找到 {date_str} 的交易数据文件")
            
            print(f"总共加载了 {len(all_trade_records)} 条交易记录")
            return all_trade_records
            
        except Exception as e:
            print(f"加载最近交易数据失败: {str(e)}")
            return []
    
    # 测试加载功能
    records = load_recent_trading_data(5)
    
    if records:
        print("\n=== 加载的交易记录统计 ===")
        
        # 按日期统计
        by_date = {}
        for record in records:
            date = record.get('trade_date', 'unknown')
            if date not in by_date:
                by_date[date] = []
            by_date[date].append(record)
        
        for date, date_records in sorted(by_date.items()):
            total_profit = sum(r['profit'] for r in date_records)
            print(f"{date}: {len(date_records)} 条记录, 总盈亏: {total_profit:.2f}元")
        
        # 按股票统计
        by_stock = {}
        for record in records:
            code = record['code']
            if code not in by_stock:
                by_stock[code] = []
            by_stock[code].append(record)
        
        print(f"\n=== 按股票统计 ===")
        for code, stock_records in sorted(by_stock.items()):
            total_profit = sum(r['profit'] for r in stock_records)
            profit_count = sum(1 for r in stock_records if r['profit'] > 0)
            print(f"{code}: {len(stock_records)} 次交易, 盈利 {profit_count} 次, 总盈亏: {total_profit:.2f}元")

if __name__ == "__main__":
    print("=== 创建测试数据 ===")
    create_test_trading_data()
    
    print("\n=== 测试数据加载功能 ===")
    test_load_recent_data()
    
    print("\n测试完成！")
