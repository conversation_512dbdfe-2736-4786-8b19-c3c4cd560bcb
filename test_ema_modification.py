#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试EMA修改是否正确的脚本
"""

import pandas as pd
import numpy as np

def test_ema_calculation():
    """测试EMA计算是否正确"""
    # 创建测试数据
    data = {
        'time': pd.date_range('2024-01-01 09:30:00', periods=100, freq='5min'),
        'open': np.random.uniform(100, 110, 100),
        'high': np.random.uniform(105, 115, 100),
        'low': np.random.uniform(95, 105, 100),
        'close': np.random.uniform(100, 110, 100),
        'volume': np.random.randint(1000, 10000, 100),
        'amount': np.random.uniform(100000, 1000000, 100)
    }
    
    df = pd.DataFrame(data)
    
    # 模拟原程序中的EMA计算
    ema_period = 12  # 默认EMA周期
    df['ema'] = df['close'].ewm(span=ema_period).mean()
    
    print("EMA计算测试:")
    print(f"数据长度: {len(df)}")
    print(f"EMA周期: {ema_period}")
    print(f"最后5个EMA值:")
    print(df['ema'].tail())
    
    # 验证EMA计算是否正确
    manual_ema = df['close'].ewm(span=12).mean()
    is_correct = np.allclose(df['ema'], manual_ema, rtol=1e-10)
    print(f"EMA计算正确性: {'✅ 正确' if is_correct else '❌ 错误'}")
    
    return is_correct

def test_strategy_params():
    """测试策略参数设置"""
    strategy_params = {
        'm1': 12,  # 快速EMA周期
        'm2': 50,  # 慢速EMA周期
        'n': 14,   # ATR周期
        'm': 1.0,  # ATR乘数
        'max_wait_periods': 3,  # 等待第二个信号的最大周期数
        'ema_period': 12  # EMA周期，默认为12
    }
    
    print("\n策略参数测试:")
    print(f"EMA周期参数: {strategy_params.get('ema_period', 12)}")
    print(f"参数设置: {'✅ 正确' if 'ema_period' in strategy_params else '❌ 缺失'}")
    
    return 'ema_period' in strategy_params

def main():
    """主测试函数"""
    print("=" * 50)
    print("EMA修改验证测试")
    print("=" * 50)
    
    # 测试EMA计算
    ema_test = test_ema_calculation()
    
    # 测试策略参数
    params_test = test_strategy_params()
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print(f"EMA计算: {'✅ 通过' if ema_test else '❌ 失败'}")
    print(f"策略参数: {'✅ 通过' if params_test else '❌ 失败'}")
    
    overall_result = ema_test and params_test
    print(f"总体结果: {'✅ 所有测试通过' if overall_result else '❌ 部分测试失败'}")
    print("=" * 50)
    
    return overall_result

if __name__ == "__main__":
    main()
