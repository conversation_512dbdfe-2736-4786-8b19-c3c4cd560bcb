#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能时间处理逻辑
验证程序能够正确区分程序产生的买入时间和服务器同步的买入时间
"""

import json
from datetime import datetime

def test_time_classification():
    """测试时间分类逻辑"""
    
    # 模拟不同类型的买入记录
    test_cases = [
        {
            "name": "程序实际买入记录",
            "record": {
                "buy_price": 1.234,
                "buy_time": "14:32:15",
                "order_id": "ORDER_12345",
                "virtual": False
            },
            "expected": "程序产生，应保留并补充日期"
        },
        {
            "name": "服务器同步记录1",
            "record": {
                "buy_price": 1.234,
                "buy_time": "15:23:18",
                "order_id": "SERVER_SYNC_1754292198",
                "use_server_data": True
            },
            "expected": "服务器同步，应使用默认时间"
        },
        {
            "name": "服务器同步记录2",
            "record": {
                "buy_price": 1.234,
                "buy_time": "15:00:00",
                "order_id": "SYNC_1754292198",
                "data_source": "server_cost"
            },
            "expected": "服务器同步，应使用默认时间"
        },
        {
            "name": "程序完整时间记录",
            "record": {
                "buy_price": 1.234,
                "buy_time": "2025-08-04 10:15:30",
                "order_id": "ORDER_67890",
                "virtual": False
            },
            "expected": "程序产生，应保留原时间"
        },
        {
            "name": "默认时间记录",
            "record": {
                "buy_price": 1.234,
                "buy_time": "2025-07-31 15:00:00",
                "order_id": "SYNC_1754292198",
                "data_source": "current_price"
            },
            "expected": "服务器同步，应使用默认时间"
        }
    ]
    
    print("=== 智能时间处理测试 ===\n")
    
    for i, case in enumerate(test_cases, 1):
        print(f"{i}. {case['name']}")
        print(f"   记录: {case['record']}")
        
        # 模拟判断逻辑
        is_program_generated = classify_time_source(case['record'])
        
        if is_program_generated:
            result = "程序产生的时间"
            action = "保留并格式化"
        else:
            result = "服务器同步的时间"
            action = "使用默认时间"
        
        print(f"   判断: {result}")
        print(f"   处理: {action}")
        print(f"   预期: {case['expected']}")
        print()

def classify_time_source(buy_record):
    """分类时间来源（模拟程序中的逻辑）"""
    buy_time = buy_record.get('buy_time', '')
    
    # 检查订单ID是否表明是服务器同步
    order_id = buy_record.get('order_id', '')
    if order_id.startswith(('SERVER_SYNC_', 'SYNC_')):
        return False
    
    # 检查是否有服务器数据标记
    if buy_record.get('use_server_data', False):
        return False
    
    # 检查数据源
    data_source = buy_record.get('data_source', '')
    if data_source in ['server_cost', 'current_price']:
        return False
    
    # 检查时间格式，如果是完整的日期时间且不是默认的15:00:00，可能是程序产生的
    if len(buy_time) > 10 and ':' in buy_time:
        # 提取时间部分
        time_part = buy_time.split(' ')[-1] if ' ' in buy_time else buy_time
        # 如果不是15:00:00，很可能是程序实际执行时记录的时间
        if time_part != '15:00:00':
            return True
    
    # 默认认为是程序产生的（保守处理）
    return True

def format_buy_time(buy_time):
    """格式化买入时间"""
    try:
        # 如果已经是完整格式，直接返回
        if len(buy_time) >= 19 and buy_time.count('-') == 2 and buy_time.count(':') == 2:
            return buy_time
        
        # 如果只有时分秒，补充今天的日期
        if len(buy_time) <= 8 and ':' in buy_time:
            today = datetime.now().strftime('%Y-%m-%d')
            return f"{today} {buy_time}"
        
        return buy_time
        
    except Exception:
        return buy_time

def test_time_formatting():
    """测试时间格式化"""
    print("=== 时间格式化测试 ===\n")
    
    test_times = [
        "14:32:15",
        "2025-08-04 10:15:30",
        "15:00:00",
        "09:30:00"
    ]
    
    for time_str in test_times:
        formatted = format_buy_time(time_str)
        print(f"原时间: {time_str}")
        print(f"格式化: {formatted}")
        print()

if __name__ == "__main__":
    test_time_classification()
    test_time_formatting()
