#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试显示逻辑的脚本
"""

import json
import os
from datetime import datetime

def load_data():
    """加载数据文件"""
    # 加载持仓记录
    position_file = "自选股30m系统持仓记录.json"
    position_records = {}
    if os.path.exists(position_file):
        with open(position_file, 'r', encoding='utf-8') as f:
            position_records = json.load(f)
    
    # 加载交易历史
    trade_file = "自选股30m系统永久交易历史.jsonl"
    trade_records = []
    if os.path.exists(trade_file):
        with open(trade_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    try:
                        trade_record = json.loads(line)
                        trade_records.append(trade_record)
                    except json.JSONDecodeError:
                        continue
    
    # 加载股票档案
    stock_profiles = {}
    profiles_dir = "stock_profiles"
    if os.path.exists(profiles_dir):
        for profile_file in os.listdir(profiles_dir):
            if profile_file.endswith('.json'):
                file_path = os.path.join(profiles_dir, profile_file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        profile_data = json.load(f)
                    code = profile_data.get('code', profile_file.replace('.json', ''))
                    stock_profiles[code] = profile_data
                except Exception as e:
                    print(f"加载档案文件 {profile_file} 失败: {str(e)}")
    
    return position_records, trade_records, stock_profiles

def simulate_trading_enabled_mode(position_records, trade_records, stock_profiles):
    """模拟启用交易模式的显示逻辑"""
    print("=== 模拟启用交易模式 (trading_enabled=True) ===")
    
    # 模拟服务器查询失败的情况
    print("假设服务器查询失败，显示本地实际交易持仓...")
    
    current_positions = 0
    total_cost = 0
    total_market_value = 0
    
    for code, info in position_records.items():
        # 检查是否为实际交易持仓
        is_real_trade = False
        if 'buy_queue' in info and info['buy_queue']:
            # 新格式：检查队列中的第一个元素
            first_buy = info['buy_queue'][0]
            is_real_trade = not first_buy.get('virtual', False) or first_buy.get('real_trade', False)
        else:
            # 旧格式：检查顶层字段
            is_real_trade = not info.get('virtual', False)
        
        if is_real_trade:
            print(f"显示实际交易持仓: {code}")
            current_positions += 1
            
            # 获取持仓数量和买入价
            if 'buy_queue' in info and info['buy_queue']:
                volume = info['total_quantity']
                buy_price = info['buy_queue'][0]['buy_price']
            else:
                volume = info.get('quantity', 0)
                buy_price = info.get('buy_price', 0)
            
            # 模拟当前价格（实际应该从市场获取）
            current_price = buy_price * 1.02  # 假设上涨2%
            
            position_cost = buy_price * volume
            position_market_value = current_price * volume
            total_cost += position_cost
            total_market_value += position_market_value
            
            print(f"  数量: {volume}, 买入价: {buy_price:.4f}, 当前价: {current_price:.4f}")
        else:
            print(f"跳过虚拟持仓: {code}")
    
    floating_profit = total_market_value - total_cost
    print(f"\n持仓统计:")
    print(f"  当前持仓: {current_positions}")
    print(f"  持仓总成本: {total_cost:,.2f}")
    print(f"  当前市值: {total_market_value:,.2f}")
    print(f"  浮动盈亏: {floating_profit:,.2f}")

def simulate_virtual_trading_mode(position_records, trade_records, stock_profiles):
    """模拟虚拟交易模式的显示逻辑"""
    print("\n=== 模拟虚拟交易模式 (trading_enabled=False) ===")
    
    current_positions = 0
    total_cost = 0
    total_market_value = 0
    
    for code, info in position_records.items():
        # 检查是否为虚拟持仓
        is_virtual = False
        if 'buy_queue' in info and info['buy_queue']:
            # 新格式：检查队列中的第一个元素
            is_virtual = info['buy_queue'][0].get('virtual', False)
        else:
            # 旧格式：检查顶层字段
            is_virtual = info.get('virtual', False)
        
        if is_virtual:
            print(f"显示虚拟持仓: {code}")
            current_positions += 1
            
            # 获取持仓数量和买入价
            if 'buy_queue' in info and info['buy_queue']:
                volume = info['total_quantity']
                buy_price = info['buy_queue'][0]['buy_price']
            else:
                volume = info.get('quantity', 0)
                buy_price = info.get('buy_price', 0)
            
            # 模拟当前价格
            current_price = buy_price * 1.02  # 假设上涨2%
            
            position_cost = buy_price * volume
            position_market_value = current_price * volume
            total_cost += position_cost
            total_market_value += position_market_value
            
            print(f"  数量: {volume}, 买入价: {buy_price:.4f}, 当前价: {current_price:.4f}")
        else:
            print(f"跳过实际交易持仓: {code}")
    
    floating_profit = total_market_value - total_cost
    print(f"\n持仓统计:")
    print(f"  当前持仓: {current_positions}")
    print(f"  持仓总成本: {total_cost:,.2f}")
    print(f"  当前市值: {total_market_value:,.2f}")
    print(f"  浮动盈亏: {floating_profit:,.2f}")

def simulate_trade_summary(trade_records, stock_profiles):
    """模拟交易汇总显示逻辑"""
    print("\n=== 模拟交易汇总显示 ===")
    
    # 计算当日交易记录的汇总
    daily_total_trades = len(trade_records)
    daily_profit_trades = sum(1 for record in trade_records if record['profit'] > 0)
    daily_loss_trades = sum(1 for record in trade_records if record['profit'] <= 0)
    daily_total_profit = sum(record['profit'] for record in trade_records)
    daily_total_fee = sum(record.get('total_fee', 0) for record in trade_records)
    
    print(f"当日交易汇总:")
    print(f"  总数: {daily_total_trades}, 盈利: {daily_profit_trades}, 亏损: {daily_loss_trades}")
    print(f"  总盈亏: {daily_total_profit:.2f}, 总手续费: {daily_total_fee:.2f}")
    
    # 计算股票档案汇总
    profile_total_trades = 0
    profile_profit_trades = 0
    profile_loss_trades = 0
    profile_total_profit = 0.0
    profile_total_fee = 0.0
    
    for code, profile in stock_profiles.items():
        trade_count = profile.get('trade_count', 0)
        win_count = profile.get('win_count', 0)
        loss_count = profile.get('loss_count', 0)
        total_profit = profile.get('total_profit', 0)
        total_fee = profile.get('total_fee', 0)
        
        profile_total_trades += trade_count
        profile_profit_trades += win_count
        profile_loss_trades += loss_count
        profile_total_profit += total_profit
        profile_total_fee += total_fee
        
        if trade_count > 0:
            print(f"  {code}: 交易{trade_count}次, 盈利{win_count}次, 总盈亏{total_profit:.2f}")
    
    print(f"\n档案汇总:")
    print(f"  总数: {profile_total_trades}, 盈利: {profile_profit_trades}, 亏损: {profile_loss_trades}")
    print(f"  总盈亏: {profile_total_profit:.2f}, 总手续费: {profile_total_fee:.2f}")
    
    # 最终显示逻辑
    final_total_trades = profile_total_trades if profile_total_trades > 0 else daily_total_trades
    final_profit_trades = profile_profit_trades if profile_total_trades > 0 else daily_profit_trades
    final_loss_trades = profile_loss_trades if profile_total_trades > 0 else daily_loss_trades
    final_total_profit = profile_total_profit if profile_total_trades > 0 else daily_total_profit
    final_total_fee = profile_total_fee if profile_total_trades > 0 else daily_total_fee
    
    print(f"\n最终显示:")
    print(f"  总交易次数: {final_total_trades}")
    print(f"  盈利交易: {final_profit_trades}")
    print(f"  亏损交易: {final_loss_trades}")
    print(f"  总盈亏: {final_total_profit:.2f}元")
    print(f"  总手续费: {final_total_fee:.2f}元")

def main():
    """主函数"""
    print("开始测试显示逻辑...")
    
    # 加载数据
    position_records, trade_records, stock_profiles = load_data()
    
    print(f"数据加载完成:")
    print(f"  持仓记录: {len(position_records)}条")
    print(f"  交易历史: {len(trade_records)}条")
    print(f"  股票档案: {len(stock_profiles)}个")
    
    # 模拟不同模式的显示逻辑
    simulate_trading_enabled_mode(position_records, trade_records, stock_profiles)
    simulate_virtual_trading_mode(position_records, trade_records, stock_profiles)
    simulate_trade_summary(trade_records, stock_profiles)

if __name__ == "__main__":
    main()
