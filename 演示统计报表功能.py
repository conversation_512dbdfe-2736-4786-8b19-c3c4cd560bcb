#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
演示统计报表功能 - 展示最近5个交易日的统计功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import json
from datetime import datetime, timedelta

class StatisticsDemo:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("统计报表功能演示")
        self.root.geometry("800x600")
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="可转债交易统计报表功能演示", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 功能说明
        info_text = """
新增功能：
1. 支持选择统计范围：当日 或 最近5个交易日
2. 自动识别工作日，跳过周末
3. 合并多日数据进行统计分析
4. 按日期和股票分别统计
        """
        info_label = ttk.Label(main_frame, text=info_text, justify="left")
        info_label.pack(pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(0, 20))
        
        # 测试按钮
        ttk.Button(button_frame, text="测试当日统计", 
                  command=self.test_today_stats).pack(side="left", padx=(0, 10))
        ttk.Button(button_frame, text="测试最近5日统计", 
                  command=self.test_recent5_stats).pack(side="left", padx=(0, 10))
        ttk.Button(button_frame, text="查看可用数据文件", 
                  command=self.show_available_files).pack(side="left")
        
        # 结果显示区域
        self.result_text = tk.Text(main_frame, height=25, width=80)
        self.result_text.pack(fill="both", expand=True)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="right", fill="y")
    
    def add_result(self, text):
        """添加结果到显示区域"""
        self.result_text.insert(tk.END, text + "\n")
        self.result_text.see(tk.END)
        self.root.update()
    
    def clear_results(self):
        """清空结果显示"""
        self.result_text.delete(1.0, tk.END)
    
    def show_available_files(self):
        """显示可用的交易数据文件"""
        self.clear_results()
        self.add_result("=== 可用的交易数据文件 ===")
        
        files = []
        for filename in os.listdir("."):
            if filename.startswith("交易历史_") and filename.endswith(".json"):
                files.append(filename)
        
        if files:
            files.sort()
            for filename in files:
                try:
                    with open(filename, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        self.add_result(f"{filename}: {len(data)} 条记录")
                except Exception as e:
                    self.add_result(f"{filename}: 读取失败 - {str(e)}")
        else:
            self.add_result("未找到交易历史文件")
    
    def load_recent_trading_data(self, days=5):
        """加载最近几个交易日的交易数据"""
        try:
            all_trade_records = []
            current_date = datetime.now()
            
            # 获取最近的交易日
            trading_days = []
            check_date = current_date
            
            while len(trading_days) < days:
                # 检查是否是工作日（周一到周五）
                if check_date.weekday() < 5:  # 0-4 代表周一到周五
                    trading_days.append(check_date.strftime('%Y%m%d'))
                check_date -= timedelta(days=1)
            
            self.add_result(f"正在加载最近{days}个交易日的数据: {', '.join(trading_days)}")
            
            # 加载每个交易日的数据
            for date_str in trading_days:
                trade_file = f"交易历史_{date_str}.json"
                if os.path.exists(trade_file):
                    try:
                        with open(trade_file, 'r', encoding='utf-8') as f:
                            content = f.read().strip()
                            if content:
                                daily_records = json.loads(content)
                                # 为每条记录添加日期标识
                                for record in daily_records:
                                    record['trade_date'] = date_str
                                all_trade_records.extend(daily_records)
                                self.add_result(f"已加载 {date_str} 的 {len(daily_records)} 条交易记录")
                    except Exception as e:
                        self.add_result(f"加载 {date_str} 交易数据失败: {str(e)}")
                else:
                    self.add_result(f"未找到 {date_str} 的交易数据文件")
            
            self.add_result(f"总共加载了 {len(all_trade_records)} 条交易记录")
            return all_trade_records
            
        except Exception as e:
            self.add_result(f"加载最近交易数据失败: {str(e)}")
            return []
    
    def calculate_statistics(self, trade_records):
        """计算统计数据"""
        if not trade_records:
            return None
        
        # 按股票分组统计
        stock_stats = {}
        
        for record in trade_records:
            code = record['code']
            profit = record['profit']
            
            if code not in stock_stats:
                stock_stats[code] = {
                    'total_trades': 0,
                    'profit_trades': 0,
                    'loss_trades': 0,
                    'total_profit': 0.0,
                    'total_loss': 0.0,
                    'max_profit': 0.0,
                    'max_loss': 0.0
                }
            
            stats = stock_stats[code]
            stats['total_trades'] += 1
            
            if profit > 0:
                stats['profit_trades'] += 1
                stats['total_profit'] += profit
                stats['max_profit'] = max(stats['max_profit'], profit)
            else:
                stats['loss_trades'] += 1
                stats['total_loss'] += abs(profit)
                stats['max_loss'] = max(stats['max_loss'], abs(profit))
        
        # 计算总体统计
        total_trades = len(trade_records)
        total_profit = sum(record['profit'] for record in trade_records if record['profit'] > 0)
        total_loss = sum(abs(record['profit']) for record in trade_records if record['profit'] <= 0)
        profit_trades = sum(1 for record in trade_records if record['profit'] > 0)
        
        overall_stats = {
            'total_trades': total_trades,
            'profit_trades': profit_trades,
            'loss_trades': total_trades - profit_trades,
            'total_profit': total_profit,
            'total_loss': total_loss,
            'net_profit': total_profit - total_loss,
            'win_rate': (profit_trades / total_trades * 100) if total_trades > 0 else 0
        }
        
        return {
            'overall': overall_stats,
            'by_stock': stock_stats
        }
    
    def test_today_stats(self):
        """测试当日统计"""
        self.clear_results()
        self.add_result("=== 当日交易统计测试 ===")
        
        today = datetime.now().strftime('%Y%m%d')
        trade_file = f"交易历史_{today}.json"
        
        if os.path.exists(trade_file):
            try:
                with open(trade_file, 'r', encoding='utf-8') as f:
                    trade_records = json.loads(f.read())
                
                self.add_result(f"加载当日数据: {len(trade_records)} 条记录")
                
                stats = self.calculate_statistics(trade_records)
                if stats:
                    self.display_statistics(stats, f"当日({today})")
                
            except Exception as e:
                self.add_result(f"加载当日数据失败: {str(e)}")
        else:
            self.add_result(f"未找到当日交易数据文件: {trade_file}")
    
    def test_recent5_stats(self):
        """测试最近5日统计"""
        self.clear_results()
        self.add_result("=== 最近5个交易日统计测试 ===")
        
        trade_records = self.load_recent_trading_data(5)
        
        if trade_records:
            stats = self.calculate_statistics(trade_records)
            if stats:
                self.display_statistics(stats, "最近5个交易日")
        else:
            self.add_result("未找到交易数据")
    
    def display_statistics(self, stats, title):
        """显示统计结果"""
        overall = stats['overall']
        by_stock = stats['by_stock']
        
        self.add_result(f"\n=== {title} 统计结果 ===")
        self.add_result(f"总交易次数: {overall['total_trades']}")
        self.add_result(f"盈利次数: {overall['profit_trades']}")
        self.add_result(f"亏损次数: {overall['loss_trades']}")
        self.add_result(f"胜率: {overall['win_rate']:.1f}%")
        self.add_result(f"总盈利: {overall['total_profit']:.2f}元")
        self.add_result(f"总亏损: {overall['total_loss']:.2f}元")
        self.add_result(f"净盈亏: {overall['net_profit']:.2f}元")
        
        self.add_result(f"\n=== 分股票统计 (前10名) ===")
        # 按净盈亏排序
        sorted_stocks = sorted(by_stock.items(), 
                             key=lambda x: x[1]['total_profit'] - x[1]['total_loss'], 
                             reverse=True)
        
        for i, (code, stock_stats) in enumerate(sorted_stocks[:10]):
            net_profit = stock_stats['total_profit'] - stock_stats['total_loss']
            win_rate = (stock_stats['profit_trades'] / stock_stats['total_trades'] * 100) if stock_stats['total_trades'] > 0 else 0
            self.add_result(f"{i+1:2d}. {code}: {stock_stats['total_trades']}次交易, "
                          f"胜率{win_rate:.1f}%, 净盈亏{net_profit:.2f}元")
    
    def run(self):
        """运行演示"""
        self.root.mainloop()

if __name__ == "__main__":
    demo = StatisticsDemo()
    demo.run()
