# 调试信息5分钟间隔说明

## 问题描述

用户反馈调试信息更新太频繁：
```
09:51:22 [159516.SZ] 服务器计算盈亏: 市值10108.80 - 成本9950.60 = 158.20
```

要求调整到5分钟间隔显示。

## 解决方案

### 实现5分钟间隔控制

在 `display_position` 方法中添加时间间隔控制逻辑：

```python
# 5分钟间隔的调试信息
if not hasattr(self, '_last_debug_time'):
    self._last_debug_time = {}

current_time = time.time()
last_log_time = self._last_debug_time.get(code, 0)

if current_time - last_log_time >= 300:  # 5分钟 = 300秒
    self._last_debug_time[code] = current_time
    self.add_record(f"[{code}] 服务器计算盈亏: 市值{server_market_value:.2f} - 成本{cost_value:.2f} = {total_profit:.2f}")
```

### 控制机制

1. **时间缓存**：为每个股票代码维护上次记录时间
2. **间隔检查**：只有当距离上次记录超过300秒（5分钟）时才显示
3. **首次显示**：新股票首次出现时立即显示

### 数据结构

```python
self._last_debug_time = {
    '159516.SZ': 1703123456.789,  # 上次记录的时间戳
    '159517.SZ': 1703123789.123,
    # ... 其他股票
}
```

## 优化效果

### 优化前：
```
09:51:22 [159516.SZ] 服务器计算盈亏: 市值10108.80 - 成本9950.60 = 158.20
09:51:37 [159516.SZ] 服务器计算盈亏: 市值10115.20 - 成本9950.60 = 164.60
09:51:52 [159516.SZ] 服务器计算盈亏: 市值10098.40 - 成本9950.60 = 147.80
09:52:07 [159516.SZ] 服务器计算盈亏: 市值10122.60 - 成本9950.60 = 172.00
...（每15秒一次）
```

### 优化后：
```
09:51:22 [159516.SZ] 服务器计算盈亏: 市值10108.80 - 成本9950.60 = 158.20
（静默4分45秒）
09:56:22 [159516.SZ] 服务器计算盈亏: 市值10145.60 - 成本9950.60 = 195.00
（静默5分钟）
10:01:22 [159516.SZ] 服务器计算盈亏: 市值10089.20 - 成本9950.60 = 138.60
```

## 应用场景

此5分钟间隔控制适用于：

- **服务器数据计算**：使用服务器市值和成本价计算盈亏时的调试信息
- **实时持仓监控**：定期了解持仓盈亏变化情况
- **数据验证**：确认服务器数据计算的准确性

## 技术特点

1. **按股票独立控制**：每个股票代码有独立的时间记录
2. **精确时间控制**：使用 `time.time()` 获取精确的时间戳
3. **内存效率**：只存储必要的时间信息
4. **首次立即显示**：新股票或程序重启后立即显示第一次信息

## 用户体验

- **减少信息噪音**：避免频繁的重复信息
- **保持信息更新**：每5分钟了解最新的盈亏情况
- **专注重要变化**：减少干扰，便于关注重要信息
- **功能完整保留**：所有计算逻辑完全不变

## 注意事项

- 程序重启后会立即显示第一次调试信息
- 时间间隔从上次显示开始计算，不是固定的时间点
- 只影响调试信息显示，不影响实际的盈亏计算和界面更新
- 其他类型的调试信息（如错误、警告）不受此限制

这样既保留了调试信息的价值，又避免了过于频繁的信息干扰，提升了用户体验。
